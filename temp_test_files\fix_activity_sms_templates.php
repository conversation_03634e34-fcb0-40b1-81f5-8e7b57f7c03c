<?php

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\SmsTemplate;
use Illuminate\Support\Facades\DB;

echo "开始修复活动详情的SMS模板配置...\n";

try {
    // 查找consultation_notice模板
    $consultationTemplate = SmsTemplate::where('code', 'consultation_notice')->first();
    
    if (!$consultationTemplate) {
        echo "错误：未找到consultation_notice模板\n";
        exit(1);
    }
    
    echo "找到consultation_notice模板，ID: {$consultationTemplate->id}\n";
    echo "模板名称: {$consultationTemplate->name}\n";
    echo "云片模板ID: {$consultationTemplate->yunpian_template_id}\n";
    
    // 获取所有没有关联SMS模板的活动详情
    $activities = ActivityDetail::whereNull('sms_template_id')->get();
    
    echo "找到 {$activities->count()} 个未关联SMS模板的活动\n";
    
    if ($activities->count() > 0) {
        // 更新所有活动详情，关联到consultation_notice模板
        $updated = ActivityDetail::whereNull('sms_template_id')
            ->update(['sms_template_id' => $consultationTemplate->id]);
        
        echo "成功更新了 {$updated} 个活动的SMS模板配置\n";
        
        // 验证更新结果
        $verifyActivities = ActivityDetail::with('smsTemplate')
            ->where('sms_template_id', $consultationTemplate->id)
            ->get();
        
        echo "\n验证更新结果：\n";
        foreach ($verifyActivities as $activity) {
            echo "活动ID: {$activity->id}, 主题: {$activity->theme}, SMS模板: {$activity->smsTemplate->name}\n";
        }
    } else {
        echo "所有活动都已经关联了SMS模板\n";
    }
    
    echo "\n修复完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
    exit(1);
}