<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'description',
        'type',
        'group',
    ];

    protected $casts = [
        'value' => 'string',
    ];

    /**
     * 获取设置值
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }
        
        return match ($setting->type) {
            'boolean' => filter_var($setting->value, FILTER_VALIDATE_BOOLEAN),
            'integer' => (int) $setting->value,
            'float' => (float) $setting->value,
            'array', 'json' => json_decode($setting->value, true),
            default => $setting->value,
        };
    }

    /**
     * 设置值
     */
    public static function set(string $key, $value, string $description = '', string $type = 'string', string $group = 'general')
    {
        $processedValue = match ($type) {
            'boolean' => $value ? '1' : '0',
            'array', 'json' => json_encode($value),
            default => (string) $value,
        };

        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $processedValue,
                'description' => $description,
                'type' => $type,
                'group' => $group,
            ]
        );
    }

    /**
     * 获取审计日志保留天数
     */
    public static function getAuditLogRetentionDays(): int
    {
        return (int) self::get('audit_log_retention_days', 90);
    }

    /**
     * 设置审计日志保留天数
     */
    public static function setAuditLogRetentionDays(int $days): void
    {
        self::set('audit_log_retention_days', $days, 'integer');
    }

    /**
     * 获取站点名称
     */
    public static function getSiteName(): string
    {
        return self::get('site_name', '内容管理系统');
    }

    /**
     * 获取站点描述
     */
    public static function getSiteDescription(): string
    {
        return self::get('site_description', '基于Laravel和Filament的内容管理系统');
    }

    /**
     * 获取站点URL
     */
    public static function getSiteUrl(): string
    {
        return self::get('site_url', 'http://localhost');
    }

    /**
     * 获取站点关键词
     */
    public static function getSiteKeywords(): string
    {
        return self::get('site_keywords', 'CMS,内容管理,Laravel,Filament');
    }

    /**
     * 获取备案号
     */
    public static function getSiteIcp(): string
    {
        return self::get('site_icp', '');
    }

    /**
     * 获取允许上传的图片类型
     */
    public static function getUploadImageTypes(): array
    {
        return self::get('upload_image_types', ['gif', 'jpg', 'jpeg', 'png', 'tiff', 'bmp']);
    }

    /**
     * 获取允许上传的多媒体类型
     */
    public static function getUploadMediaTypes(): array
    {
        return self::get('upload_media_types', ['mp3', 'wmv', 'wma', 'rmvb', 'rm', 'avi', 'flv', 'mp4', 'mov']);
    }

    /**
     * 获取允许上传的文档类型
     */
    public static function getUploadDocumentTypes(): array
    {
        return self::get('upload_document_types', ['txt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'pdf']);
    }

    /**
     * 获取所有允许上传的文件类型
     */
    public static function getAllowedUploadTypes(): array
    {
        return array_merge(
            self::getUploadImageTypes(),
            self::getUploadMediaTypes(),
            self::getUploadDocumentTypes()
        );
    }

    /**
     * 获取文件上传最大大小（KB）
     */
    public static function getUploadMaxSize(): int
    {
        return (int) self::get('upload_max_size', 10240);
    }

    /**
     * 检查是否开放用户注册
     */
    public static function isUserRegistrationEnabled(): bool
    {
        return (bool) self::get('user_registration_enabled', true);
    }

    /**
     * 获取统计代码
     */
    public static function getStatisticsCode(): string
    {
        return self::get('statistics_code', '');
    }

    /**
     * 获取站长工具验证代码
     */
    public static function getWebmasterTools(): string
    {
        return self::get('webmaster_tools', '');
    }

    /**
     * 检查文件类型是否允许上传
     */
    public static function isFileTypeAllowed(string $extension): bool
    {
        $extension = strtolower($extension);
        return in_array($extension, self::getAllowedUploadTypes());
    }

    /**
     * 获取站点基本信息
     */
    public static function getSiteInfo(): array
    {
        return [
            'name' => self::getSiteName(),
            'description' => self::getSiteDescription(),
            'url' => self::getSiteUrl(),
            'keywords' => self::getSiteKeywords(),
            'icp' => self::getSiteIcp(),
        ];
    }
}