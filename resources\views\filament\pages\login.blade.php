@php
    $captcha = captcha_src();
@endphp

<x-filament-panels::page.simple>
    <x-slot name="title">
        {{ __('filament-panels::pages/auth/login.title') }}
    </x-slot>

    {{ \Filament\Support\Facades\FilamentView::renderHook('panels::auth.login.form.before') }}

    @if (filament()->hasRegistration())
        <x-slot name="subheading">
            {{ __('filament-panels::pages/auth/login.actions.register.before') }}

            {{ $this->registerAction }}
        </x-slot>
    @endif

    <x-filament-panels::form wire:submit="authenticate" method="POST">
        @csrf
        {{ $this->form }}

        <div class="mt-4 text-center">
            <img src="{{ captcha_src('flat') }}" alt="验证码" id="captcha" class="inline-block">
            <x-filament::button
                type="button"
                onclick="document.getElementById('captcha').src='{{ captcha_src('flat') }}?'+Math.random();"
                class="w-full mt-2"
            >
                刷新验证码
            </x-filament::button>
            <input type="text" name="captcha" wire:model.defer="captcha" class="form-input mt-2 w-full" placeholder="请输入验证码">
            @error('captcha') <div class="text-danger">{{ $message }}</div> @enderror
        </div>

        <div class="mt-4">
            <x-filament::button type="submit" form="authenticate" class="w-full">
                登录
            </x-filament::button>
        </div>
    </x-filament-panels::form>

    {{ \Filament\Support\Facades\FilamentView::renderHook('panels::auth.login.form.after') }}

    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', () => {
            Livewire.on('refresh-captcha', () => {
                document.getElementById('captcha').src = '{{ captcha_src('flat') }}?' + Math.random();
            });
        });
    </script>
    @endpush
</x-filament-panels::page.simple>
