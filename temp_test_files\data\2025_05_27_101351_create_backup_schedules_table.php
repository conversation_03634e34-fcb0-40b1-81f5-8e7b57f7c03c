<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('计划名称');
            $table->text('description')->nullable()->comment('计划描述');
            $table->enum('backup_type', ['full', 'structure', 'data'])->default('full')->comment('备份类型');
            $table->enum('frequency', ['hourly', 'daily', 'weekly', 'monthly', 'custom'])->comment('执行频率');
            $table->integer('frequency_value')->nullable()->comment('频率值(如小时的分钟数)');
            $table->time('execute_time')->nullable()->comment('执行时间');
            $table->tinyInteger('execute_day')->nullable()->comment('执行星期(0-6)');
            $table->tinyInteger('execute_date')->nullable()->comment('执行日期(1-31)');
            $table->boolean('is_enabled')->default(true)->comment('是否启用');
            $table->timestamp('last_run_at')->nullable()->comment('上次执行时间');
            $table->timestamp('next_run_at')->nullable()->comment('下次执行时间');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['is_enabled', 'next_run_at']);
            $table->index('frequency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_schedules');
    }
};
