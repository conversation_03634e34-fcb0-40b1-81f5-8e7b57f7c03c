<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use App\Models\SmsTemplate;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 演示：如何添加新的短信模板 ===\n\n";

echo "当前系统支持的短信模板:\n";
$existingTemplates = SmsTemplate::all(['code', 'name', 'template_params']);
foreach ($existingTemplates as $template) {
    $params = is_array($template->template_params) 
        ? $template->template_params 
        : json_decode($template->template_params, true);
    echo "- {$template->code}: {$template->name}\n";
    if ($params) {
        echo "  参数: " . implode(', ', array_keys($params)) . "\n";
    }
}

echo "\n=== 演示：添加新模板的步骤 ===\n";

echo "\n1. 假设我们要添加一个'活动取消通知'模板\n";
echo "   模板代码: activity_cancelled\n";
echo "   需要参数: name(用户姓名), topic(活动主题), reason(取消原因)\n";

echo "\n2. 在数据库中添加模板记录:\n";
echo "   INSERT INTO sms_templates (code, name, template_params, yunpian_template_id) VALUES\n";
echo "   ('activity_cancelled', '活动取消通知', '{\"name\":\"用户姓名\",\"topic\":\"活动主题\",\"reason\":\"取消原因\"}', '6065999');\n";

echo "\n3. 系统会自动支持这个新模板，无需修改代码！\n";

echo "\n=== 演示：通用系统的工作原理 ===\n";

// 模拟新模板的参数构建
function simulateNewTemplate() {
    // 模拟新模板配置
    $newTemplateParams = [
        'name' => '用户姓名',
        'topic' => '活动主题', 
        'reason' => '取消原因'
    ];
    
    // 模拟数据源
    $dataSources = [
        'name' => '张三',
        'topic' => '心理健康讲座',
        'time' => '2025年6月1日 14:00',
        'address' => '嘉定区教育学院',
        'obj' => '全体教师',
        'code' => 'ABC123',
        'pwd' => 'password123',
        'reason' => '因天气原因' // 新增的参数
    ];
    
    echo "新模板需要的参数: " . implode(', ', array_keys($newTemplateParams)) . "\n";
    echo "系统自动构建的参数:\n";
    
    $templateData = [];
    foreach ($newTemplateParams as $paramKey => $paramDescription) {
        if (isset($dataSources[$paramKey])) {
            $value = mb_substr($dataSources[$paramKey], 0, 20);
            $templateData[$paramKey] = $value;
            echo "- {$paramKey}: '{$value}' (长度: " . mb_strlen($value) . ")\n";
        } else {
            echo "- {$paramKey}: [数据源未找到] \n";
            $templateData[$paramKey] = '';
        }
    }
    
    return $templateData;
}

$simulatedData = simulateNewTemplate();

echo "\n=== 系统优势总结 ===\n";
echo "✅ 完全数据驱动：模板配置存储在数据库中\n";
echo "✅ 动态参数构建：根据模板配置自动选择需要的参数\n";
echo "✅ 参数长度控制：自动截断到20字符以内\n";
echo "✅ 扩展性强：添加新模板无需修改代码\n";
echo "✅ 容错性好：缺失参数会提供默认值并记录警告\n";
echo "✅ 调试友好：详细的日志记录便于排查问题\n";

echo "\n=== 如何扩展数据源 ===\n";
echo "如果新模板需要系统中不存在的参数，只需在 buildTemplateData 方法的 \$dataSources 数组中添加：\n";
echo "\n例如添加 'reason' 参数：\n";
echo "\$dataSources['reason'] = !empty(\$activityDetail->cancel_reason) ? \$activityDetail->cancel_reason : '未知原因';\n";

echo "\n=== 演示完成 ===\n";
echo "通用短信模板系统现在可以支持任意数量的模板，只需在数据库中配置即可！\n";