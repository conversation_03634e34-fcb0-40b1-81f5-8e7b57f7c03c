<?php

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\SmsTemplate;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;

echo "=== SMS发送功能测试（修复后）===\n";

try {
    // 获取一个已配置SMS模板的活动
    $activity = ActivityDetail::with('smsTemplate')
        ->whereNotNull('sms_template_id')
        ->first();
    
    if (!$activity) {
        echo "错误：未找到已配置SMS模板的活动\n";
        exit(1);
    }
    
    echo "测试活动信息：\n";
    echo "- ID: {$activity->id}\n";
    echo "- 主题: {$activity->theme}\n";
    echo "- 时间: {$activity->activity_time}\n";
    echo "- 地址: {$activity->address}\n";
    echo "- 目标: {$activity->target}\n";
    if ($activity->smsTemplate) {
        echo "- SMS模板: {$activity->smsTemplate->name}\n";
        echo "- 模板代码: {$activity->smsTemplate->code}\n";
        echo "- 云片模板ID: {$activity->smsTemplate->yunpian_template_id}\n";
        
        $templateParams = json_decode($activity->smsTemplate->template_params, true);
        echo "- 模板参数: " . json_encode($templateParams, JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "- SMS模板: 未配置\n";
        exit(1);
    }
    
    echo "\n=== 准备发送测试SMS ===\n";
    
    // 准备SMS参数
    $smsParams = [
        'topic' => $activity->theme,
        'time' => $activity->activity_time,
        'address' => $activity->address,
        'obj' => $activity->target
    ];
    
    echo "SMS参数: " . json_encode($smsParams, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 测试手机号（请替换为真实的测试号码）
    $testPhone = '13800138000';
    
    echo "\n发送到测试号码: {$testPhone}\n";
    
    // 创建SMS服务实例
    $smsService = new SmsService();
    
    // 发送SMS
    $templateCode = $activity->smsTemplate ? $activity->smsTemplate->code : null;
    if (!$templateCode) {
        echo "错误：无法获取模板代码\n";
        exit(1);
    }
    
    $result = $smsService->sendSms(
        $testPhone,
        $templateCode,
        $smsParams
    );
    
    echo "\n=== 发送结果 ===\n";
    if ($result['success']) {
        echo "✅ SMS发送成功！\n";
        echo "响应数据: " . json_encode($result['data'], JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "❌ SMS发送失败！\n";
        echo "错误信息: {$result['message']}\n";
        if (isset($result['data'])) {
            echo "响应数据: " . json_encode($result['data'], JSON_UNESCAPED_UNICODE) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";