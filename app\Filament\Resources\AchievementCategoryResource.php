<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AchievementCategoryResource\Pages;
use App\Models\AchievementCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use CodeWithDennis\FilamentSelectTree\SelectTree;

class AchievementCategoryResource extends Resource
{
    protected static ?string $model = AchievementCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-trophy';

    protected static ?string $navigationGroup = '成就管理';

    protected static ?int $navigationSort = 3;

    protected static ?int $navigationGroupSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('分类名称')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('slug')
                            ->label('分类别名')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('用于URL，只能包含字母、数字和连字符'),
                        SelectTree::make('parent_id')
                            ->label('父分类')
                            ->relationship('parent', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('选择父分类（留空为根分类）'),
                        Forms\Components\Textarea::make('description')
                            ->label('分类描述')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('显示设置')
                    ->schema([
                        Forms\Components\TextInput::make('icon')
                            ->label('分类图标')
                            ->maxLength(255)
                            ->helperText('可以使用 Heroicon 图标名称'),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('分类名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('slug')
                    ->label('分类别名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('父分类')
                    ->sortable(),
                Tables\Columns\TextColumn::make('path')
                    ->label('分类路径')
                    ->limit(50),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('启用')
                    ->falseLabel('禁用')
                    ->placeholder('全部'),
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('父分类')
                    ->relationship('parent', 'name')
                    ->placeholder('全部分类'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAchievementCategories::route('/'),
            'create' => Pages\CreateAchievementCategory::route('/create'),
            'edit' => Pages\EditAchievementCategory::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '成就分类';
    }

    public static function getModelLabel(): string
    {
        return '成就分类';
    }

    public static function getPluralModelLabel(): string
    {
        return '成就分类';
    }
}
