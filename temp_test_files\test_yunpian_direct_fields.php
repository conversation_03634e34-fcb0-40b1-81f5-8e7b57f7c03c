<?php

/**
 * 测试脚本：验证重新设计的云片网直接对应字段是否正确工作
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Models\Activity;
use Illuminate\Support\Facades\DB;

echo "=== 测试云片网直接对应字段 ===\n\n";

try {
    // 1. 创建测试数据
    echo "1. 创建测试数据...\n";
    
    // 创建活动
    $activity = Activity::create([
        'title' => '2025年春季教学研讨会',
        'content' => '测试活动内容',
        'status' => 'published',
        'user_id' => 1
    ]);
    
    // 创建活动详情，直接使用云片网对应字段
    $activityDetail = ActivityDetail::create([
        'activity_id' => $activity->id,
        'theme' => '教学研讨会主题',
        'deadline' => now()->addDays(7),
        'quota' => 100,
        'target' => '教师',
        'activity_time' => now()->addDays(10),
        'fee' => '免费',
        'registration_method' => 'online',
        'address' => '学校多功能厅',
        'sms_template' => '测试模板',
        // 新增的云片网直接对应字段
        'topic' => '2025年春季教学研讨会 - 创新教学方法探讨',
        'time' => '2025年02月15日 14:00',
        'obj' => '全体中小学教师'
    ]);
    
    // 创建报名记录
    $registration = Registration::create([
        'activity_detail_id' => $activityDetail->id,
        'name' => '张老师',
        'phone' => '13800138000',
        'email' => '<EMAIL>',
        'status' => 1,
        'source' => 'web'
    ]);
    
    echo "✅ 测试数据创建成功\n";
    echo "- 活动ID: {$activity->id}\n";
    echo "- 活动详情ID: {$activityDetail->id}\n";
    echo "- 报名ID: {$registration->id}\n\n";
    
    // 2. 模拟RegistrationController的buildTemplateData方法
    echo "2. 测试字段映射逻辑...\n";
    
    function buildTemplateData($registration) {
        $activityDetail = $registration->activityDetail;
        $activity = $activityDetail->activity;
        
        // 直接使用与云片网参数对应的字段，简化映射逻辑
        
        // #topic# - 活动主题 (优先使用直接对应的topic字段)
        $topic = !empty($activityDetail->topic) ? $activityDetail->topic : 
                (!empty($activity->title) ? $activity->title : 
                (!empty($activityDetail->theme) ? $activityDetail->theme : '未设置主题'));
        
        // #time# - 活动时间 (优先使用直接对应的time字段)
        $time = '';
        if (!empty($activityDetail->time)) {
            $time = $activityDetail->time;
        } elseif (!empty($activityDetail->activity_time)) {
            try {
                $time = \Carbon\Carbon::parse($activityDetail->activity_time)->format('Y年m月d日 H:i');
            } catch (\Exception $e) {
                $time = $activityDetail->activity_time;
            }
        } else {
            $time = '未设置时间';
        }
        
        // #address# - 活动地址 (直接使用address字段)
        $address = !empty($activityDetail->address) ? $activityDetail->address : '未设置地址';
        
        // #obj# - 目标对象 (优先使用直接对应的obj字段)
        $obj = !empty($activityDetail->obj) ? $activityDetail->obj : 
               (!empty($activityDetail->target_audience) ? $activityDetail->target_audience : 
               (!empty($activityDetail->target) ? $activityDetail->target : '未设置对象'));
        
        return [
            "topic" => $topic,    // 对应云片网 #topic#
            "time" => $time,      // 对应云片网 #time#
            "address" => $address, // 对应云片网 #address#
            "obj" => $obj         // 对应云片网 #obj#
        ];
    }
    
    $templateData = buildTemplateData($registration);
    
    echo "字段映射结果：\n";
    foreach ($templateData as $key => $value) {
        echo "- #{$key}#: '{$value}'\n";
    }
    
    // 3. 验证所有字段都有值
    echo "\n3. 验证字段完整性...\n";
    $emptyFields = [];
    foreach ($templateData as $key => $value) {
        if (empty($value) || strpos($value, '未设置') !== false) {
            $emptyFields[] = $key;
        }
    }
    
    if (empty($emptyFields)) {
        echo "✅ 所有字段都已正确填充！\n";
    } else {
        echo "❌ 以下字段仍为空或使用默认值: " . implode(', ', $emptyFields) . "\n";
    }
    
    // 4. 生成短信内容
    echo "\n4. 生成短信内容...\n";
    $smsTemplate = '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。';
    
    $smsContent = $smsTemplate;
    foreach ($templateData as $key => $value) {
        $smsContent = str_replace("#{$key}#", $value, $smsContent);
    }
    
    echo "短信模板: {$smsTemplate}\n";
    echo "生成内容: {$smsContent}\n";
    
    // 5. 检查是否还有未替换的占位符
    echo "\n5. 检查占位符替换...\n";
    if (preg_match('/#\w+#/', $smsContent)) {
        echo "❌ 短信内容中仍有未替换的占位符\n";
    } else {
        echo "✅ 所有占位符都已正确替换\n";
    }
    
    // 6. 清理测试数据
    echo "\n6. 清理测试数据...\n";
    $registration->delete();
    $activityDetail->delete();
    $activity->delete();
    echo "✅ 测试数据清理完成\n";
    
    echo "\n=== 测试完成 ===\n";
    echo "重新设计的云片网直接对应字段工作正常！\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
    
    // 尝试清理可能创建的数据
    try {
        if (isset($registration)) $registration->delete();
        if (isset($activityDetail)) $activityDetail->delete();
        if (isset($activity)) $activity->delete();
    } catch (Exception $cleanupError) {
        echo "清理数据时出错: " . $cleanupError->getMessage() . "\n";
    }
}