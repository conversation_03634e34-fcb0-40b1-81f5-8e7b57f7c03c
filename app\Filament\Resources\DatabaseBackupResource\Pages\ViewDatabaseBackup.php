<?php

namespace App\Filament\Resources\DatabaseBackupResource\Pages;

use App\Filament\Resources\DatabaseBackupResource;
use App\Services\DatabaseBackupService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class ViewDatabaseBackup extends ViewRecord
{
    protected static string $resource = DatabaseBackupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('download')
                ->label('下载备份')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    if (!$this->record->fileExists()) {
                        Notification::make()
                            ->title('文件不存在')
                            ->body('备份文件已被删除或移动')
                            ->danger()
                            ->send();
                        return;
                    }

                    return Storage::disk('local')->download(
                        $this->record->file_path, 
                        $this->record->name . '.sql'
                    );
                })
                ->visible(fn (): bool => 
                    $this->record->status === \App\Models\DatabaseBackup::STATUS_COMPLETED && 
                    $this->record->fileExists()
                ),
            Actions\Action::make('retry')
                ->label('重新备份')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->action(function () {
                    $backupService = app(DatabaseBackupService::class);
                    
                    try {
                        $backupService->executeBackup($this->record);
                        
                        Notification::make()
                            ->title('备份重试成功')
                            ->body('备份任务已重新开始执行')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('备份重试失败')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn (): bool => 
                    $this->record->status === \App\Models\DatabaseBackup::STATUS_FAILED
                )
                ->requiresConfirmation()
                ->modalHeading('重新执行备份')
                ->modalDescription('确定要重新执行此备份任务吗？')
                ->modalSubmitActionLabel('确认重试'),
            Actions\EditAction::make()
                ->visible(fn (): bool => 
                    $this->record->status === \App\Models\DatabaseBackup::STATUS_PENDING
                ),
            Actions\DeleteAction::make()
                ->before(function () {
                    // 删除备份文件
                    $this->record->deleteFile();
                }),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DatabaseBackupResource\Widgets\BackupDetailsWidget::class,
        ];
    }
}
