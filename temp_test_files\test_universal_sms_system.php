<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Models\SmsTemplate;
use App\Services\SmsService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试通用短信模板系统 ===\n\n";

// 1. 获取所有短信模板
$templates = SmsTemplate::all(['code', 'name', 'template_params', 'yunpian_template_id']);

echo "系统中的所有短信模板:\n";
foreach ($templates as $template) {
    echo "- {$template->code}: {$template->name}\n";
    $params = is_array($template->template_params) 
        ? $template->template_params 
        : json_decode($template->template_params, true);
    if ($params) {
        echo "  需要参数: " . implode(', ', array_keys($params)) . "\n";
    }
    echo "\n";
}

// 2. 获取一个测试活动
$activityDetail = ActivityDetail::with(['activity', 'smsTemplate'])
    ->whereNotNull('sms_template_id')
    ->first();

if (!$activityDetail) {
    echo "错误：未找到已配置SMS模板的活动\n";
    exit(1);
}

echo "测试活动信息:\n";
echo "- 活动ID: {$activityDetail->id}\n";
echo "- 活动主题: {$activityDetail->theme}\n";
echo "- 地址: {$activityDetail->address}\n";
echo "- 地址长度: " . mb_strlen($activityDetail->address) . "\n";
echo "- SMS模板ID: {$activityDetail->sms_template_id}\n";

if ($activityDetail->smsTemplate) {
    echo "- SMS模板: {$activityDetail->smsTemplate->name}\n";
    echo "- 模板代码: {$activityDetail->smsTemplate->code}\n";
    echo "- 云片模板ID: {$activityDetail->smsTemplate->yunpian_template_id}\n";
} else {
    echo "- SMS模板: 未配置\n";
}

echo "\n=== 测试通用参数构建功能 ===\n";

// 3. 模拟通用的buildTemplateData方法
function buildTemplateDataUniversal($registration, $activityDetail, $templateCode) {
    // 从数据库获取模板配置
    $smsTemplate = SmsTemplate::where('code', $templateCode)->first();
    if (!$smsTemplate || !$smsTemplate->template_params) {
        echo "警告：SMS模板配置未找到或参数为空 - {$templateCode}\n";
        return [];
    }
    
    // 获取模板需要的参数列表
    $requiredParams = is_array($smsTemplate->template_params) 
        ? $smsTemplate->template_params 
        : json_decode($smsTemplate->template_params, true);
        
    if (!$requiredParams) {
        echo "警告：SMS模板参数解析失败 - {$templateCode}\n";
        return [];
    }
    
    // 准备所有可能的数据源
    $dataSources = [
        'name' => !empty($registration->name) ? $registration->name : '用户',
        'topic' => !empty($activityDetail->topic) ? $activityDetail->topic : '活动通知',
        'time' => !empty($activityDetail->time) ? $activityDetail->formatted_time : '待定时间',
        'address' => !empty($activityDetail->address) ? $activityDetail->address : '待定地点',
        'obj' => !empty($activityDetail->obj) ? $activityDetail->obj : '全体人员',
        'code' => !empty($activityDetail->code) ? $activityDetail->code : '',
        'pwd' => !empty($activityDetail->pwd) ? $activityDetail->pwd : ''
    ];
    
    // 根据模板配置动态构建参数，云片网要求每个参数长度不超过20个字符
    $templateData = [];
    foreach ($requiredParams as $paramKey => $paramDescription) {
        if (isset($dataSources[$paramKey])) {
            $templateData[$paramKey] = mb_substr($dataSources[$paramKey], 0, 20);
        } else {
            echo "警告：SMS模板参数数据源未找到 - {$paramKey}({$paramDescription})\n";
            $templateData[$paramKey] = ''; // 提供空值作为默认值
        }
    }
    
    return $templateData;
}

// 创建一个测试注册记录
$testRegistration = new \stdClass();
$testRegistration->id = 999;
$testRegistration->name = '测试用户';

// 4. 测试不同的模板
$testTemplates = ['consultation_notice', 'account_activated', 'xinling_verification'];

foreach ($testTemplates as $templateCode) {
    echo "\n--- 测试模板: {$templateCode} ---\n";
    
    $template = SmsTemplate::where('code', $templateCode)->first();
    if (!$template) {
        echo "模板不存在，跳过\n";
        continue;
    }
    
    echo "模板名称: {$template->name}\n";
    echo "云片模板ID: {$template->yunpian_template_id}\n";
    
    $params = is_array($template->template_params) 
        ? $template->template_params 
        : json_decode($template->template_params, true);
    
    echo "模板需要的参数: " . implode(', ', array_keys($params)) . "\n";
    
    $testData = buildTemplateDataUniversal($testRegistration, $activityDetail, $templateCode);
    
    echo "构建的参数:\n";
    foreach ($testData as $key => $value) {
        echo "- {$key}: '{$value}' (长度: " . mb_strlen($value) . ")\n";
    }
}

echo "\n=== 测试实际短信发送 ===\n";

// 5. 测试consultation_notice模板的实际发送
$templateCode = 'consultation_notice';
$testMobile = '15701696507';

try {
    $smsService = new SmsService();
    
    if (!$smsService->validateMobile($testMobile)) {
        echo "错误：手机号码格式不正确\n";
        exit(1);
    }
    
    $testData = buildTemplateDataUniversal($testRegistration, $activityDetail, $templateCode);
    
    echo "开始发送短信...\n";
    echo "手机号: {$testMobile}\n";
    echo "模板代码: {$templateCode}\n";
    echo "参数: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    $result = $smsService->sendSms(
        $testMobile,
        $templateCode,
        $testData
    );
    
    if ($result) {
        echo "✅ 短信发送成功！\n";
        echo "返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "❌ 短信发送失败！\n";
    }
    
} catch (Exception $e) {
    echo "❌ 短信发送异常: " . $e->getMessage() . "\n";
    echo "异常详情: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 通用短信模板系统测试完成 ===\n";
echo "请查看 storage/logs/laravel.log 文件中的最新日志条目\n";