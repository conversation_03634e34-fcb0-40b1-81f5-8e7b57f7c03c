<?php

namespace App\Handlers;

use App\Models\WechatAccount;
use App\Models\WechatMessage;
use App\Services\WechatReplyService;
use Illuminate\Support\Facades\Log;

abstract class WechatEventHandler
{
    protected WechatReplyService $replyService;
    protected WechatAccount $account;
    protected array $message;

    public function __construct(WechatReplyService $replyService)
    {
        $this->replyService = $replyService;
    }

    /**
     * 设置微信账号
     */
    public function setAccount(WechatAccount $account): self
    {
        $this->account = $account;
        return $this;
    }

    /**
     * 设置消息数据
     */
    public function setMessage(array $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * 处理事件
     */
    abstract public function handle(): ?string;

    /**
     * 记录消息到数据库
     */
    protected function saveMessage(string $msgType, array $additionalData = []): WechatMessage
    {
        $messageData = [
            'wechat_account_id' => $this->account->id,
            'openid' => $this->message['FromUserName'] ?? '',
            'message_id' => $this->message['MsgId'] ?? null,
            'message_type' => $msgType,
            'status' => 'received',
            'created_time' => isset($this->message['CreateTime']) ? date('Y-m-d H:i:s', $this->message['CreateTime']) : now(),
        ];

        // 合并额外数据
        $messageData = array_merge($messageData, $additionalData);

        return WechatMessage::create($messageData);
    }

    /**
     * 获取自动回复内容
     */
    protected function getAutoReply(string $replyType, array $params = []): ?string
    {
        try {
            return $this->replyService->getReply(
                $this->account->id,
                $replyType,
                $params
            );
        } catch (\Exception $e) {
            Log::error('获取自动回复失败', [
                'account_id' => $this->account->id,
                'reply_type' => $replyType,
                'params' => $params,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * 记录回复消息
     */
    protected function saveReply(WechatMessage $originalMessage, string $replyContent, string $replyType = 'text'): void
    {
        try {
            $originalMessage->update([
                'reply_type' => $replyType,
                'reply_content' => $replyContent,
                'replied_at' => now(),
                'status' => 'replied',
            ]);
        } catch (\Exception $e) {
            Log::error('保存回复消息失败', [
                'message_id' => $originalMessage->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 构建文本回复
     */
    protected function buildTextReply(string $content): string
    {
        return sprintf(
            '<xml><ToUserName><![CDATA[%s]]></ToUserName><FromUserName><![CDATA[%s]]></FromUserName><CreateTime>%s</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[%s]]></Content></xml>',
            $this->message['FromUserName'],
            $this->message['ToUserName'],
            time(),
            $content
        );
    }

    /**
     * 构建图片回复
     */
    protected function buildImageReply(string $mediaId): string
    {
        return sprintf(
            '<xml><ToUserName><![CDATA[%s]]></ToUserName><FromUserName><![CDATA[%s]]></FromUserName><CreateTime>%s</CreateTime><MsgType><![CDATA[image]]></MsgType><Image><MediaId><![CDATA[%s]]></MediaId></Image></xml>',
            $this->message['FromUserName'],
            $this->message['ToUserName'],
            time(),
            $mediaId
        );
    }

    /**
     * 构建语音回复
     */
    protected function buildVoiceReply(string $mediaId): string
    {
        return sprintf(
            '<xml><ToUserName><![CDATA[%s]]></ToUserName><FromUserName><![CDATA[%s]]></FromUserName><CreateTime>%s</CreateTime><MsgType><![CDATA[voice]]></MsgType><Voice><MediaId><![CDATA[%s]]></MediaId></Voice></xml>',
            $this->message['FromUserName'],
            $this->message['ToUserName'],
            time(),
            $mediaId
        );
    }

    /**
     * 构建视频回复
     */
    protected function buildVideoReply(string $mediaId, string $title = '', string $description = ''): string
    {
        return sprintf(
            '<xml><ToUserName><![CDATA[%s]]></ToUserName><FromUserName><![CDATA[%s]]></FromUserName><CreateTime>%s</CreateTime><MsgType><![CDATA[video]]></MsgType><Video><MediaId><![CDATA[%s]]></MediaId><Title><![CDATA[%s]]></Title><Description><![CDATA[%s]]></Description></Video></xml>',
            $this->message['FromUserName'],
            $this->message['ToUserName'],
            time(),
            $mediaId,
            $title,
            $description
        );
    }

    /**
     * 构建音乐回复
     */
    protected function buildMusicReply(string $title, string $description, string $musicUrl, string $hqMusicUrl, string $thumbMediaId): string
    {
        return sprintf(
            '<xml><ToUserName><![CDATA[%s]]></ToUserName><FromUserName><![CDATA[%s]]></FromUserName><CreateTime>%s</CreateTime><MsgType><![CDATA[music]]></MsgType><Music><Title><![CDATA[%s]]></Title><Description><![CDATA[%s]]></Description><MusicUrl><![CDATA[%s]]></MusicUrl><HQMusicUrl><![CDATA[%s]]></HQMusicUrl><ThumbMediaId><![CDATA[%s]]></ThumbMediaId></Music></xml>',
            $this->message['FromUserName'],
            $this->message['ToUserName'],
            time(),
            $title,
            $description,
            $musicUrl,
            $hqMusicUrl,
            $thumbMediaId
        );
    }

    /**
     * 构建图文回复
     */
    protected function buildNewsReply(array $articles): string
    {
        $articleCount = count($articles);
        $articlesXml = '';
        
        foreach ($articles as $article) {
            $articlesXml .= sprintf(
                '<item><Title><![CDATA[%s]]></Title><Description><![CDATA[%s]]></Description><PicUrl><![CDATA[%s]]></PicUrl><Url><![CDATA[%s]]></Url></item>',
                $article['title'] ?? '',
                $article['description'] ?? '',
                $article['pic_url'] ?? '',
                $article['url'] ?? ''
            );
        }

        return sprintf(
            '<xml><ToUserName><![CDATA[%s]]></ToUserName><FromUserName><![CDATA[%s]]></FromUserName><CreateTime>%s</CreateTime><MsgType><![CDATA[news]]></MsgType><ArticleCount>%d</ArticleCount><Articles>%s</Articles></xml>',
            $this->message['FromUserName'],
            $this->message['ToUserName'],
            time(),
            $articleCount,
            $articlesXml
        );
    }
}