<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'operated_at',
        'operation_type',
        'module',
        'target_id',
        'request_ip',
        'request_url',
        'request_data',
        'status',
        'error_message',
        'remark',
    ];

    protected $casts = [
        'operated_at' => 'datetime',
        'request_data' => 'array',
    ];

    /**
     * 关联用户模型
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取操作类型的中文描述
     */
    public function getOperationTypeTextAttribute(): string
    {
        return match ($this->operation_type) {
            'CREATE' => '创建',
            'UPDATE' => '更新',
            'DELETE' => '删除',
            'VIEW' => '查看',
            'LOGIN' => '登录',
            'LOGOUT' => '登出',
            'EXPORT' => '导出',
            'IMPORT' => '导入',
            'CLEANUP' => '清理',
            default => $this->operation_type,
        };
    }

    /**
     * 获取状态的中文描述
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'SUCCESS' => '成功',
            'FAILED' => '失败',
            'ERROR' => '错误',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 获取用户名称（如果用户存在）
     */
    public function getUserNameAttribute(): string
    {
        return $this->user ? $this->user->name : '未知用户';
    }
}
