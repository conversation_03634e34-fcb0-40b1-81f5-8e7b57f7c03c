<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Http\Controllers\RegistrationController;
use Illuminate\Http\Request;

echo "=== 测试buildTemplateData方法 ===\n";

try {
    // 获取一个测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到注册记录\n";
        exit;
    }
    
    echo "注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    
    // 创建控制器实例
    $controller = new RegistrationController();
    
    // 使用反射来调用私有方法
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('buildTemplateData');
    $method->setAccessible(true);
    
    // 调用buildTemplateData方法
    $templateData = $method->invoke($controller, $registration);
    
    echo "\n=== 模板数据结果 ===\n";
    foreach ($templateData as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 检查关键字段是否为空
    echo "\n=== 字段检查 ===\n";
    if (empty($templateData['time'])) {
        echo "❌ time字段为空\n";
    } else {
        echo "✅ time字段有值: '{$templateData['time']}'\n";
    }
    
    if (empty($templateData['obj'])) {
        echo "❌ obj字段为空\n";
    } else {
        echo "✅ obj字段有值: '{$templateData['obj']}'\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
