<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kalnoy\Nestedset\NodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Comment extends Model
{
    use HasFactory, NodeTrait, SoftDeletes;

    protected $fillable = [
        'post_id',
        'article_id',
        'user_id',
        'parent_id',
        'author_name',
        'author_email',
        'author_ip',
        'content',
        'status',
        'is_private',
        'meta_data',
    ];

    protected $casts = [
        'meta_data' => 'array',
        'is_private' => 'boolean',
    ];

    // 关联文章
    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    // 关联文章
    public function article()
    {
        return $this->belongsTo(Article::class);
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 关联父评论
    public function parent()
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    // 关联子评论
    public function children()
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    // 关联通知
    public function notifications()
    {
        return $this->hasMany(CommentNotification::class);
    }

    // 获取所有已审核的评论
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    // 获取所有待审核的评论
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // 获取所有已拒绝的评论
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    // 获取所有公开评论
    public function scopePublic($query)
    {
        return $query->where('is_private', false);
    }

    // 获取评论作者名称
    public function getAuthorNameAttribute($value)
    {
        if ($this->user) {
            return $this->user->name;
        }
        return $value;
    }

    // 获取评论作者邮箱
    public function getAuthorEmailAttribute($value)
    {
        if ($this->user) {
            return $this->user->email;
        }
        return $value;
    }

    // 检查评论是否可以编辑
    public function canEdit(User $user): bool
    {
        return $user->id === $this->user_id || $user->hasRole('admin');
    }

    // 检查评论是否可以删除
    public function canDelete(User $user): bool
    {
        return $user->id === $this->user_id || $user->hasRole('admin');
    }

    // 检查评论是否可以审核
    public function canModerate(User $user): bool
    {
        return $user->hasRole('admin');
    }
}