<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class OptimizeMemoryForFilament
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 为 Filament 管理面板增加内存限制
        if ($request->is('admin/*')) {
            ini_set('memory_limit', '4G');
            
            // 对于特定的资源页面，进一步增加内存
            if ($request->is('admin/administrative-institutions/*')) {
                ini_set('memory_limit', '4G');
            }
            
            // 设置最大执行时间
            ini_set('max_execution_time', 600);
        }
        
        $response = $next($request);
        
        // 在响应后清理内存
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        return $response;
    }
}