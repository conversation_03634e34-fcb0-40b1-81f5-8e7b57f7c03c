<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\SmsTemplate;

echo "=== 最终SMS修复验证测试 ===\n";

// 复制修复后的buildTemplateData方法逻辑
function buildTemplateData($registration, $activityDetail = null, $templateCode = 'activity_registration_confirm')
{
    // 如果没有传入activityDetail，从registration中获取
    if ($activityDetail === null) {
        $activityDetail = $registration->activityDetail;
    }
    $activity = $activityDetail->activity;
    
    // 云片网字段映射
    $name = !empty($registration->name) ? $registration->name : "用户";
    
    // #topic# - 活动主题
    $topic = "";
    if (!empty($activityDetail->topic)) {
        $topic = $activityDetail->topic;
    } elseif (!empty($activity->title)) {
        $topic = $activity->title;
    } elseif (!empty($activityDetail->theme)) {
        $topic = $activityDetail->theme;
    } else {
        $topic = "活动通知";
    }
    
    // #time# - 活动时间
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        try {
            $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
        } catch (Exception $e) {
            $time = "待定时间";
        }
    } else {
        $time = "待定时间";
    }
    
    // #address# - 活动地址
    $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
    
    // #obj# - 目标对象
    $obj = "";
    if (!empty($activityDetail->target_audience)) {
        $obj = $activityDetail->target_audience;
    } elseif (!empty($activityDetail->target)) {
        $obj = $activityDetail->target;
    } else {
        $obj = "全体人员";
    }
    
    return [
        "name" => $name,
        "topic" => $topic,
        "time" => $time,
        "address" => $address,
        "obj" => $obj
    ];
}

// 模拟SMS服务的参数处理
function simulateSmsService($templateData)
{
    echo "\n=== 模拟SMS服务参数处理 ===\n";
    
    $tplValue = [];
    foreach ($templateData as $key => $value) {
        // 模拟SmsService中的处理逻辑
        $formattedKey = '#' . $key . '#';
        $tplValue[$formattedKey] = $value;
        echo "参数: {$formattedKey} = '{$value}'\n";
    }
    
    return $tplValue;
}

// 模拟模板内容替换
function simulateTemplateReplacement($templateContent, $tplValue)
{
    echo "\n=== 模拟模板内容替换 ===\n";
    echo "原始模板: {$templateContent}\n";
    
    $finalContent = $templateContent;
    foreach ($tplValue as $key => $value) {
        $finalContent = str_replace($key, $value, $finalContent);
    }
    
    echo "替换后内容: {$finalContent}\n";
    return $finalContent;
}

try {
    // 获取测试数据
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到注册记录\n";
        exit;
    }
    
    echo "注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    echo "手机号: {$registration->phone}\n";
    
    // 获取SMS模板
    $template = SmsTemplate::where('code', 'consultation_notification')->first();
    if (!$template) {
        echo "没有找到consultation_notification模板\n";
        exit;
    }
    
    echo "\n=== SMS模板信息 ===\n";
    echo "模板名称: {$template->name}\n";
    echo "模板代码: {$template->code}\n";
    echo "模板内容: {$template->description}\n";
    
    // 测试修复后的buildTemplateData方法
    echo "\n=== 测试修复后的buildTemplateData方法 ===\n";
    $templateData = buildTemplateData($registration);
    
    foreach ($templateData as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 检查关键字段
    echo "\n=== 关键字段检查 ===\n";
    $criticalFields = ['time', 'obj', 'topic', 'address', 'name'];
    $allFieldsValid = true;
    
    foreach ($criticalFields as $field) {
        if (empty($templateData[$field])) {
            echo "❌ {$field}字段为空\n";
            $allFieldsValid = false;
        } else {
            echo "✅ {$field}字段有值: '{$templateData[$field]}'\n";
        }
    }
    
    if ($allFieldsValid) {
        echo "\n✅ 所有关键字段都有值\n";
    } else {
        echo "\n❌ 存在空字段\n";
    }
    
    // 模拟SMS发送流程
    $tplValue = simulateSmsService($templateData);
    $finalContent = simulateTemplateReplacement($template->description, $tplValue);
    
    // 最终验证
    echo "\n=== 最终验证 ===\n";
    if (strpos($finalContent, '#') === false) {
        echo "✅ 模板替换成功，没有未替换的占位符\n";
        echo "✅ SMS修复验证通过\n";
    } else {
        echo "❌ 模板替换失败，仍有未替换的占位符\n";
        echo "❌ SMS修复验证失败\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
