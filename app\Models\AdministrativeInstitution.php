<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdministrativeInstitution extends Model
{
    use HasFactory, SoftDeletes;
    
    /**
     * 模型启动方法
     */
    protected static function boot()
    {
        parent::boot();
        
        // 删除时级联处理子级机构
        static::deleting(function ($institution) {
            // 如果是软删除，则软删除所有子级机构
            if ($institution->isForceDeleting()) {
                // 强制删除时，递归强制删除所有子级
                $institution->children()->each(function ($child) {
                    $child->forceDelete();
                });
            } else {
                // 软删除时，递归软删除所有子级
                $institution->children()->each(function ($child) {
                    $child->delete();
                });
            }
        });
        
        // 恢复时级联恢复子级机构
        static::restored(function ($institution) {
            // 恢复所有被软删除的子级机构
            $institution->children()->onlyTrashed()->each(function ($child) {
                $child->restore();
            });
        });
    }

    protected $fillable = [
        'name',
        // 'code', // 已移除：机构代码字段未被实际使用
        'province_code',
        'province_name',
        'city_code',
        'city_name',
        'district_code',
        'district_name',
        'town_code',
        'town_name',
        'administrative_level',
        'parent_id',
        'host_unit',
        'contact_person',
        'contact_phone',
        'contact_email',
        'address',
        'description',
        'sort_order',
        'is_active',
        'binding_status',
        'full_region_name',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 获取上级机构
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(AdministrativeInstitution::class, 'parent_id');
    }

    /**
     * 获取下级机构
     */
    public function children(): HasMany
    {
        return $this->hasMany(AdministrativeInstitution::class, 'parent_id');
    }

    /**
     * 获取完整地区名称
     */
    public function getFullRegionNameAttribute(): string
    {
        $parts = array_filter([
            $this->province_name,
            $this->city_name,
            $this->district_name,
            $this->town_name,
        ]);
        
        return implode(' - ', $parts);
    }

    /**
     * 获取行政级别中文名称
     */
    public function getAdministrativeLevelNameAttribute(): string
    {
        return match($this->administrative_level) {
            'province' => '省级',
            'city' => '市级',
            'district' => '区县级',
            'town' => '乡镇级',
            default => '未知',
        };
    }

    /**
     * 获取绑定状态中文名称
     */
    public function getBindingStatusNameAttribute(): string
    {
        return match($this->binding_status) {
            'bound' => '已绑定',
            'unbound' => '未绑定',
            default => '未知',
        };
    }

    /**
     * 根据行政级别获取可选字段
     */
    public function getRequiredFieldsByLevel(): array
    {
        return match($this->administrative_level) {
            'province' => ['name', 'host_unit'],
            'city' => ['name', 'host_unit', 'parent_id'],
            'district' => ['name', 'host_unit', 'parent_id'],
            'town' => ['name', 'host_unit', 'parent_id'],
            default => ['name'],
        };
    }

    /**
     * 获取层级路径
     */
    public function getHierarchyPath(): string
    {
        $path = [];
        $current = $this;
        
        while ($current) {
            array_unshift($path, $current->name);
            $current = $current->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * 作用域：按行政级别筛选
     */
    public function scopeByLevel($query, string $level)
    {
        return $query->where('administrative_level', $level);
    }

    /**
     * 作用域：按绑定状态筛选
     */
    public function scopeByBindingStatus($query, string $status)
    {
        return $query->where('binding_status', $status);
    }

    /**
     * 作用域：按地区筛选
     */
    public function scopeByRegion($query, ?string $provinceCode = null, ?string $cityCode = null, ?string $districtCode = null, ?string $townCode = null)
    {
        if ($provinceCode) {
            $query->where('province_code', $provinceCode);
        }
        if ($cityCode) {
            $query->where('city_code', $cityCode);
        }
        if ($districtCode) {
            $query->where('district_code', $districtCode);
        }
        if ($townCode) {
            $query->where('town_code', $townCode);
        }
        
        return $query;
    }

    /**
     * 作用域：启用状态
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}