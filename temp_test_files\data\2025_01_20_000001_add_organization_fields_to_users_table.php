<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('school')->constrained('organizations')->onDelete('set null')->comment('所属组织ID');
            $table->string('position')->nullable()->after('organization_id')->comment('职位');
            $table->string('employee_id')->nullable()->after('position')->comment('员工编号');
            $table->date('join_date')->nullable()->after('employee_id')->comment('入职日期');
            $table->enum('employment_status', ['active', 'inactive', 'resigned', 'suspended'])->default('active')->after('join_date')->comment('在职状态');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['organization_id']);
            $table->dropColumn(['organization_id', 'position', 'employee_id', 'join_date', 'employment_status']);
        });
    }
};