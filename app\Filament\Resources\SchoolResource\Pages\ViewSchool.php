<?php

namespace App\Filament\Resources\SchoolResource\Pages;

use App\Filament\Resources\SchoolResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Support\Enums\FontWeight;

class ViewSchool extends ViewRecord
{
    protected static string $resource = SchoolResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('编辑')
                ->icon('heroicon-o-pencil'),
            Actions\DeleteAction::make()
                ->label('删除')
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalHeading('删除学校')
                ->modalDescription('确定要删除这个学校吗？此操作不可撤销。')
                ->modalSubmitActionLabel('确认删除')
                ->modalCancelActionLabel('取消'),
            Actions\Action::make('back')
                ->label('返回列表')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url($this->getResource()::getUrl('index')),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('学校名称')
                                    ->weight(FontWeight::Bold)
                                    ->size('lg'),
                                TextEntry::make('school_type')
                                    ->label('学校类型')
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'kindergarten' => '幼儿园',
                                        'primary_secondary' => '中小学',
                                        default => $state,
                                    })
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'kindergarten' => 'warning',
                                        'primary_secondary' => 'success',
                                        default => 'gray',
                                    }),
                                TextEntry::make('grade_type')
                                    ->label('年级类型')
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'kindergarten' => '幼儿园',
                                        'primary_nine_year' => '小学九年一贯制',
                                        'junior_middle' => '初级中学',
                                        'complete_middle' => '完全中学',
                                        'senior_middle' => '高级中学',
                                        'twelve_year' => '十二年一贯制',
                                        'all_stages' => '全学段（幼儿园至高中）',
                                        default => $state,
                                    }),
                                TextEntry::make('education_system')
                                    ->label('学制')
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'five_four' => '五四制',
                                        'six_three' => '六三制',
                                        default => $state,
                                    }),
                                TextEntry::make('school_nature')
                                    ->label('学校性质')
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'public' => '公办',
                                        'private' => '民办',
                                        default => $state,
                                    })
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'public' => 'info',
                                        'private' => 'gray',
                                        default => 'gray',
                                    }),
                                TextEntry::make('binding_status')
                                    ->label('绑定状态')
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'bound' => '已绑定',
                                        'unbound' => '未绑定',
                                        default => $state,
                                    })
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'bound' => 'success',
                                        'unbound' => 'danger',
                                        default => 'gray',
                                    }),
                            ]),
                    ])
                    ->columns(2),
                
                Section::make('地区信息')
                    ->schema([
                        TextEntry::make('full_region_name')
                            ->label('完整地区')
                            ->weight(FontWeight::Medium),
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('province_name')
                                    ->label('省份'),
                                TextEntry::make('city_name')
                                    ->label('城市'),
                                TextEntry::make('district_name')
                                    ->label('区县'),
                                TextEntry::make('town_name')
                                    ->label('乡镇'),
                            ]),
                    ]),
                
                Section::make('组织绑定')
                    ->schema([
                        TextEntry::make('administrativeInstitution.name')
                            ->label('关联行政机构')
                            ->placeholder('未绑定')
                            ->url(fn ($record) => $record->administrativeInstitution ? 
                                route('filament.admin.resources.administrative-institutions.view', $record->administrativeInstitution) : null
                            )
                            ->openUrlInNewTab(),
                    ])
                    ->visible(fn ($record) => $record->binding_status === 'bound'),
                
                Section::make('联系信息')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('contact_person')
                                    ->label('联系人')
                                    ->placeholder('未填写'),
                                TextEntry::make('contact_phone')
                                    ->label('联系电话')
                                    ->placeholder('未填写'),
                            ]),
                        TextEntry::make('contact_address')
                            ->label('联系地址')
                            ->placeholder('未填写'),
                    ]),
                
                Section::make('其他信息')
                    ->schema([
                        TextEntry::make('description')
                            ->label('描述')
                            ->placeholder('无描述')
                            ->columnSpanFull(),
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('sort_order')
                                    ->label('排序')
                                    ->numeric(),
                                TextEntry::make('is_active')
                                    ->label('状态')
                                    ->formatStateUsing(fn (bool $state): string => $state ? '启用' : '禁用')
                                    ->badge()
                                    ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                                TextEntry::make('created_at')
                                    ->label('创建时间')
                                    ->dateTime('Y-m-d H:i:s'),
                            ]),
                    ]),
            ]);
    }
}