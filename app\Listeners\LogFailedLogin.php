<?php

namespace App\Listeners;

use App\Models\AuditLog;
use Illuminate\Auth\Events\Failed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Request;

class LogFailedLogin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Failed $event): void
    {
        try {
            AuditLog::create([
                'user_id' => null, // 登录失败时可能没有用户ID
                'operated_at' => now(),
                'operation_type' => 'LOGIN',
                'module' => '系统登录',
                'target_id' => null,
                'request_ip' => Request::ip(),
                'request_url' => Request::fullUrl(),
                'request_data' => [
                    'attempted_email' => $event->credentials['email'] ?? '未知',
                    'user_agent' => Request::userAgent(),
                    'attempt_time' => now()->toDateTimeString(),
                ],
                'status' => 'FAILED',
                'error_message' => '登录凭据无效',
                'remark' => '用户登录失败',
            ]);
        } catch (\Exception $e) {
            \Log::error('记录登录失败日志失败: ' . $e->getMessage());
        }
    }
}
