<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WechatMenuResource\Pages;
use App\Models\WechatAccount;
use App\Models\WechatMenu;
use App\Services\WechatMenuService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WechatMenuResource extends Resource
{
    protected static ?string $model = WechatMenu::class;

    protected static ?string $navigationIcon = 'heroicon-o-bars-3';

    protected static ?string $navigationLabel = '微信菜单';

    protected static ?string $modelLabel = '微信菜单';

    protected static ?string $pluralModelLabel = '微信菜单';

    protected static ?string $navigationGroup = '微信推文管理';

    protected static ?int $navigationSort = 21;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\Select::make('wechat_account_id')
                            ->label('微信账号')
                            ->options(WechatAccount::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable(),
                        
                        Forms\Components\Select::make('parent_id')
                            ->label('父级菜单')
                            ->options(function (callable $get) {
                                $accountId = $get('wechat_account_id');
                                if (!$accountId) {
                                    return [];
                                }
                                return WechatMenu::where('wechat_account_id', $accountId)
                                    ->whereNull('parent_id')
                                    ->pluck('name', 'id');
                            })
                            ->searchable()
                            ->reactive(),
                        
                        Forms\Components\TextInput::make('name')
                            ->label('菜单名称')
                            ->required()
                            ->maxLength(16)
                            ->helperText('菜单名称最多16个字符'),
                        
                        Forms\Components\Select::make('type')
                            ->label('菜单类型')
                            ->options([
                                'click' => '点击推事件',
                                'view' => '跳转URL',
                                'scancode_push' => '扫码推事件',
                                'scancode_waitmsg' => '扫码推事件且弹出"消息接收中"提示框',
                                'pic_sysphoto' => '弹出系统拍照发图',
                                'pic_photo_or_album' => '弹出拍照或者相册发图',
                                'pic_weixin' => '弹出微信相册发图器',
                                'location_select' => '弹出地理位置选择器',
                                'media_id' => '下发消息（除文本消息）',
                                'view_limited' => '跳转图文消息URL',
                                'miniprogram' => '小程序',
                            ])
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                $set('key', null);
                                $set('url', null);
                                $set('media_id', null);
                                $set('appid', null);
                                $set('pagepath', null);
                            }),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('菜单配置')
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->label('菜单KEY值')
                            ->maxLength(128)
                            ->visible(fn (callable $get) => in_array($get('type'), ['click', 'scancode_push', 'scancode_waitmsg', 'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin', 'location_select']))
                            ->required(fn (callable $get) => in_array($get('type'), ['click', 'scancode_push', 'scancode_waitmsg', 'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin', 'location_select'])),
                        
                        Forms\Components\TextInput::make('url')
                            ->label('网页链接')
                            ->url()
                            ->maxLength(1024)
                            ->visible(fn (callable $get) => in_array($get('type'), ['view', 'view_limited']))
                            ->required(fn (callable $get) => in_array($get('type'), ['view', 'view_limited'])),
                        
                        Forms\Components\TextInput::make('media_id')
                            ->label('媒体文件ID')
                            ->maxLength(128)
                            ->visible(fn (callable $get) => $get('type') === 'media_id')
                            ->required(fn (callable $get) => $get('type') === 'media_id'),
                        
                        Forms\Components\TextInput::make('appid')
                            ->label('小程序APPID')
                            ->maxLength(32)
                            ->visible(fn (callable $get) => $get('type') === 'miniprogram')
                            ->required(fn (callable $get) => $get('type') === 'miniprogram'),
                        
                        Forms\Components\TextInput::make('pagepath')
                            ->label('小程序页面路径')
                            ->maxLength(256)
                            ->visible(fn (callable $get) => $get('type') === 'miniprogram')
                            ->required(fn (callable $get) => $get('type') === 'miniprogram'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('其他设置')
                    ->schema([
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小越靠前'),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('wechatAccount.name')
                    ->label('微信账号')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('菜单名称')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('父级菜单')
                    ->placeholder('顶级菜单')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('type')
                    ->label('菜单类型')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'click' => '点击推事件',
                        'view' => '跳转URL',
                        'scancode_push' => '扫码推事件',
                        'scancode_waitmsg' => '扫码推事件且弹出提示框',
                        'pic_sysphoto' => '弹出系统拍照发图',
                        'pic_photo_or_album' => '弹出拍照或者相册发图',
                        'pic_weixin' => '弹出微信相册发图器',
                        'location_select' => '弹出地理位置选择器',
                        'media_id' => '下发消息',
                        'view_limited' => '跳转图文消息URL',
                        'miniprogram' => '小程序',
                        default => $state,
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('wechat_account_id')
                    ->label('微信账号')
                    ->options(WechatAccount::active()->pluck('name', 'id')),
                
                Tables\Filters\SelectFilter::make('type')
                    ->label('菜单类型')
                    ->options([
                        'click' => '点击推事件',
                        'view' => '跳转URL',
                        'miniprogram' => '小程序',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态'),
                
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('sync_menu')
                    ->label('同步菜单到微信')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('同步菜单到微信')
                    ->modalDescription('确定要将当前菜单同步到微信服务器吗？')
                    ->form([
                        Forms\Components\Select::make('wechat_account_id')
                            ->label('选择微信账号')
                            ->options(WechatAccount::active()->pluck('name', 'id'))
                            ->required(),
                    ])
                    ->action(function (array $data) {
                        try {
                            $menuService = app(WechatMenuService::class);
                            $result = $menuService->createMenu($data['wechat_account_id']);
                            
                            if ($result) {
                                Notification::make()
                                    ->title('菜单同步成功')
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('菜单同步失败')
                                    ->danger()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('菜单同步失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                
                Tables\Actions\Action::make('get_menu')
                    ->label('从微信获取菜单')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('info')
                    ->requiresConfirmation()
                    ->modalHeading('从微信获取菜单')
                    ->modalDescription('确定要从微信服务器获取当前菜单并导入吗？')
                    ->form([
                        Forms\Components\Select::make('wechat_account_id')
                            ->label('选择微信账号')
                            ->options(WechatAccount::active()->pluck('name', 'id'))
                            ->required(),
                    ])
                    ->action(function (array $data) {
                        try {
                            $menuService = app(WechatMenuService::class);
                            $result = $menuService->getMenu($data['wechat_account_id']);
                            
                            if ($result) {
                                Notification::make()
                                    ->title('菜单获取成功')
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('菜单获取失败')
                                    ->danger()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('菜单获取失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->defaultSort('sort_order')
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWechatMenus::route('/'),
            'create' => Pages\CreateWechatMenu::route('/create'),
            'edit' => Pages\EditWechatMenu::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}