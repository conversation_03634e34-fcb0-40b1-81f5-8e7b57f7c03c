<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 修复现场咨询通知模板 ===\n";

try {
    // 查找现场咨询通知模板
    $template = DB::table('sms_templates')
        ->where('name', '现场咨询通知')
        ->first();
    
    if ($template) {
        echo "找到现场咨询通知模板 (ID: {$template->id})\n";
        echo "当前description: '{$template->description}'\n";
        
        // 根据用户反馈设置正确的模板内容
        $correctDescription = "【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。";
        
        // 更新模板
        DB::table('sms_templates')
            ->where('id', $template->id)
            ->update([
                'description' => $correctDescription,
                'template_params' => json_encode(["topic" => "活动主题", "time" => "活动时间", "address" => "活动地址", "obj" => "目标对象"]),
                'param_description' => '参数说明：#topic#=活动主题，#time#=活动时间，#address#=活动地址，#obj#=目标对象',
                'updated_at' => now()
            ]);
        
        echo "✓ 已更新模板内容为: '{$correctDescription}'\n";
        
        // 验证更新结果
        $updatedTemplate = DB::table('sms_templates')
            ->where('id', $template->id)
            ->first();
        
        echo "\n验证更新结果:\n";
        echo "description: '{$updatedTemplate->description}'\n";
        echo "template_params: '{$updatedTemplate->template_params}'\n";
        echo "param_description: '{$updatedTemplate->param_description}'\n";
        
    } else {
        echo "未找到现场咨询通知模板\n";
        
        // 创建新模板
        $templateId = DB::table('sms_templates')->insertGetId([
            'name' => '现场咨询通知',
            'code' => 'consultation_notice',
            'description' => "【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。",
            'template_params' => json_encode(["topic" => "活动主题", "time" => "活动时间", "address" => "活动地址", "obj" => "目标对象"]),
            'param_description' => '参数说明：#topic#=活动主题，#time#=活动时间，#address#=活动地址，#obj#=目标对象',
            'template_category' => 'consultation',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "✓ 已创建新的现场咨询通知模板 (ID: {$templateId})\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 修复完成 ===\n";
