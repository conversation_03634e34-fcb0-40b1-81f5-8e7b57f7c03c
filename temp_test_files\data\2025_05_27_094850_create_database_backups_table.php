<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('database_backups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('备份名称');
            $table->string('file_path')->nullable()->comment('备份文件路径');
            $table->bigInteger('file_size')->nullable()->comment('文件大小(字节)');
            $table->enum('backup_type', ['full', 'structure', 'data'])->default('full')->comment('备份类型');
            $table->enum('status', ['pending', 'running', 'completed', 'failed'])->default('pending')->comment('备份状态');
            $table->text('description')->nullable()->comment('备份描述');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            $table->timestamp('backup_started_at')->nullable()->comment('备份开始时间');
            $table->timestamp('backup_completed_at')->nullable()->comment('备份完成时间');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['status', 'backup_type']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('database_backups');
    }
};
