<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CommentNotification extends Model
{
    protected $fillable = [
        'comment_id',
        'user_id',
        'type',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    // 关联评论
    public function comment()
    {
        return $this->belongsTo(Comment::class);
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 获取所有未读通知
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    // 获取所有已读通知
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    // 标记通知为已读
    public function markAsRead(): void
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    // 标记通知为未读
    public function markAsUnread(): void
    {
        if ($this->is_read) {
            $this->update([
                'is_read' => false,
                'read_at' => null,
            ]);
        }
    }
} 