<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;

echo "=== 简单调试短信空值问题 ===\n\n";

// 检查所有使用consultation_notice模板的活动
$activities = ActivityDetail::whereHas('smsTemplate', function($q) {
    $q->where('code', 'consultation_notice');
})->with(['activity', 'smsTemplate'])->get();

echo "找到 {$activities->count()} 个使用consultation_notice模板的活动:\n\n";

foreach ($activities as $activity) {
    echo "活动ID: {$activity->id}\n";
    echo "活动标题: '{$activity->activity->title}'\n";
    echo "活动时间: '{$activity->activity_time}'\n";
    echo "地址: '{$activity->address}'\n";
    echo "对象: '{$activity->target}'\n";
    
    // 检查空值
    $emptyFields = [];
    if (empty($activity->activity->title)) $emptyFields[] = 'title';
    if (empty($activity->activity_time)) $emptyFields[] = 'activity_time';
    if (empty($activity->address)) $emptyFields[] = 'address';
    if (empty($activity->target)) $emptyFields[] = 'target';
    
    if (!empty($emptyFields)) {
        echo "⚠️ 空字段: " . implode(', ', $emptyFields) . "\n";
    } else {
        echo "✅ 所有字段都有值\n";
    }
    
    // 模拟构建模板数据
    $templateData = [
        'name' => '测试用户',
        'topic' => $activity->activity ? $activity->activity->title : '',
        'time' => $activity->activity_time,
        'address' => $activity->address,
        'obj' => $activity->target
    ];
    
    // 构建SMS参数
    $pairs = [];
    foreach ($templateData as $key => $value) {
        $smsKey = "#{$key}#";
        $pairs[] = urlencode($smsKey) . '=' . urlencode($value);
    }
    $tplValue = implode('&', $pairs);
    
    echo "SMS参数: {$tplValue}\n";
    echo "---\n\n";
}

echo "调试完成。\n";