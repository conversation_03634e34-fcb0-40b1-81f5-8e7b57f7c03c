<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Http\Controllers\RegistrationController;
use Illuminate\Support\Facades\Log;

echo "=== 云片网字段映射测试 ===\n";

try {
    // 获取一个测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到测试注册记录\n";
        exit(1);
    }
    
    echo "测试注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    echo "手机号: {$registration->phone}\n";
    
    $activityDetail = $registration->activityDetail;
    echo "\n=== 活动详情信息 ===\n";
    echo "活动详情ID: {$activityDetail->id}\n";
    echo "活动标题: {$activityDetail->activity->title}\n";
    echo "旧字段 - theme: {$activityDetail->theme}\n";
    echo "新字段 - topic: {$activityDetail->topic}\n";
    echo "旧字段 - target: {$activityDetail->target}\n";
    echo "新字段 - target_audience: {$activityDetail->target_audience}\n";
    echo "活动时间: {$activityDetail->activity_time}\n";
    echo "活动地址: {$activityDetail->address}\n";
    
    // 使用Laravel容器创建RegistrationController实例
    $controller = app(RegistrationController::class);
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('buildTemplateData');
    $method->setAccessible(true);
    
    $templateData = $method->invoke($controller, $registration);
    
    echo "\n=== 云片网模板数据 ===\n";
    foreach ($templateData as $key => $value) {
        echo "#{$key}#: '{$value}'\n";
    }
    
    // 检查是否有空值
    $emptyFields = [];
    foreach ($templateData as $key => $value) {
        if (empty($value) || in_array($value, ['待定时间', '待定地点', '全体人员', '活动通知', '用户'])) {
            $emptyFields[] = $key;
        }
    }
    
    echo "\n=== 字段完整性检查 ===\n";
    if (empty($emptyFields)) {
        echo "✓ 所有字段都有有效数据\n";
    } else {
        echo "⚠ 以下字段使用了默认值: " . implode(', ', $emptyFields) . "\n";
    }
    
    echo "\n✓ 云片网字段映射测试完成\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试结束 ===\n";
