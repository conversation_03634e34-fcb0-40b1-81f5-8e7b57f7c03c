<?php

namespace App\Filament\Resources\PointCenterResource\Widgets;

use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\User;
use App\Models\UserPoint;
use Illuminate\Support\Facades\DB;

class PointCenterStatsWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        // 使用与主表格相同的查询逻辑
        $regionStats = collect(DB::select("
            SELECT
                region,
                COUNT(DISTINCT users.id) as user_count,
                COALESCE(SUM(user_points.points), 0) as total_points,
                COALESCE(SUM(CASE WHEN YEAR(user_points.earned_date) = YEAR(NOW()) AND MONTH(user_points.earned_date) = MONTH(NOW()) THEN user_points.points ELSE 0 END), 0) as month_points,
                COALESCE(SUM(CASE WHEN YEAR(user_points.earned_date) = YEAR(NOW()) AND QUARTER(user_points.earned_date) = QUARTER(NOW()) THEN user_points.points ELSE 0 END), 0) as quarter_points,
                COALESCE(SUM(CASE WHEN YEAR(user_points.earned_date) = YEAR(NOW()) THEN user_points.points ELSE 0 END), 0) as year_points
            FROM users
            LEFT JOIN user_points ON users.id = user_points.user_id
            WHERE users.is_verified = 1 AND users.region IS NOT NULL
            GROUP BY region
            ORDER BY total_points DESC
        "));

        // 计算统计数据
        $totalRegions = $regionStats->count();
        $topRegion = $regionStats->first();
        $monthlyTotal = $regionStats->sum('month_points');
        $quarterlyTotal = $regionStats->sum('quarter_points');
        $yearlyTotal = $regionStats->sum('year_points');

        return [
            Stat::make('参与地区数', $totalRegions)
                ->description('有积分记录的地区数量')
                ->descriptionIcon('heroicon-m-map-pin')
                ->color('success'),

            Stat::make('领先地区', $topRegion ? $topRegion->region : '暂无')
                ->description($topRegion ? "总积分: {$topRegion->total_points} 分" : '暂无数据')
                ->descriptionIcon('heroicon-m-trophy')
                ->color('warning'),

            Stat::make('本月总积分', $monthlyTotal)
                ->description('本月全部地区获得积分')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info'),

            Stat::make('本年总积分', $yearlyTotal)
                ->description('本年全部地区获得积分')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('primary'),
        ];
    }
}
