/**
 * CKEditor 5 自定义视频上传插件
 * 为微信文章编辑器添加本地视频上传功能
 */

class VideoUploadCommand {
    constructor(editor) {
        this.editor = editor;
    }

    execute() {
        const editor = this.editor;
        
        // 创建文件输入元素
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'video/*';
        fileInput.style.display = 'none';
        
        // 添加到页面
        document.body.appendChild(fileInput);
        
        // 监听文件选择
        fileInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                this.uploadVideo(file);
            }
            // 清理
            document.body.removeChild(fileInput);
        });
        
        // 触发文件选择
        fileInput.click();
    }

    uploadVideo(file) {
        const editor = this.editor;
        
        // 验证文件类型
        if (!file.type.startsWith('video/')) {
            alert('请选择视频文件');
            return;
        }
        
        // 验证文件大小 (50MB)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('视频文件大小不能超过50MB');
            return;
        }
        
        // 创建FormData
        const formData = new FormData();
        formData.append('upload', file);
        
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        // 显示上传进度
        const notification = this.showUploadProgress();
        
        // 发送上传请求
        fetch('/admin/ckeditor/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            this.hideUploadProgress(notification);
            
            if (data.url) {
                // 插入视频到编辑器
                this.insertVideoElement(data.url, file.name);
            } else {
                alert('视频上传失败：' + (data.error?.message || '未知错误'));
            }
        })
        .catch(error => {
            this.hideUploadProgress(notification);
            console.error('视频上传错误:', error);
            alert('视频上传失败：网络错误');
        });
    }

    insertVideoElement(videoUrl, fileName) {
        const editor = this.editor;
        
        // 创建视频HTML
        const videoHtml = `
            <figure class="media">
                <video controls style="width: 100%; max-width: 100%; height: auto;">
                    <source src="${videoUrl}" type="video/mp4">
                    <source src="${videoUrl}" type="video/webm">
                    您的浏览器不支持视频播放。
                </video>
                <figcaption>视频: ${fileName}</figcaption>
            </figure>
        `;
        
        // 插入到编辑器
        editor.model.change(writer => {
            const viewFragment = editor.data.processor.toView(videoHtml);
            const modelFragment = editor.data.toModel(viewFragment);
            editor.model.insertContent(modelFragment);
        });
    }

    showUploadProgress() {
        // 创建进度提示
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 9999;
            font-size: 14px;
        `;
        notification.textContent = '正在上传视频...';
        document.body.appendChild(notification);
        return notification;
    }

    hideUploadProgress(notification) {
        if (notification && notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }
}

class VideoUploadPlugin {
    constructor(editor) {
        this.editor = editor;
    }

    static get pluginName() {
        return 'VideoUpload';
    }

    init() {
        const editor = this.editor;
        
        // 注册命令
        editor.commands.add('uploadVideo', new VideoUploadCommand(editor));
        
        // 添加工具栏按钮
        editor.ui.componentFactory.add('uploadVideo', locale => {
            const command = editor.commands.get('uploadVideo');
            const buttonView = new editor.ui.ButtonView(locale);

            buttonView.set({
                label: '上传视频',
                icon: this.getVideoIcon(),
                tooltip: true
            });

            // 绑定按钮点击事件
            buttonView.on('execute', () => {
                command.execute();
            });

            return buttonView;
        });
    }

    getVideoIcon() {
        // 返回视频图标的SVG
        return `<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 3a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H2zm0 2h16v10H2V5z"/>
            <path d="M8 7l4 2.5L8 12V7z"/>
        </svg>`;
    }
}

// 导出插件
window.VideoUploadPlugin = VideoUploadPlugin;
