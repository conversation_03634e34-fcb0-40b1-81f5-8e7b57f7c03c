<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== 检查时间字段 ===\n";

try {
    // 检查activity_details表的所有字段
    echo "activity_details表的字段:\n";
    $columns = Schema::getColumnListing('activity_details');
    foreach ($columns as $column) {
        echo "- {$column}\n";
    }
    
    // 查看实际数据中的时间相关字段
    echo "\n=== 查看activity_details中的时间相关数据 ===\n";
    $activityDetails = DB::table('activity_details')
        ->select('id', 'activity_id', 'created_at', 'updated_at')
        ->limit(3)
        ->get();
    
    foreach ($activityDetails as $detail) {
        echo "\nactivity_detail ID: {$detail->id}\n";
        echo "activity_id: {$detail->activity_id}\n";
        echo "created_at: {$detail->created_at}\n";
        echo "updated_at: {$detail->updated_at}\n";
        
        // 检查对应的activities表中是否有时间字段
        $activity = DB::table('activities')
            ->where('id', $detail->activity_id)
            ->first();
        
        if ($activity) {
            echo "对应activity的字段:\n";
            foreach ($activity as $key => $value) {
                if (strpos($key, 'time') !== false || strpos($key, 'date') !== false) {
                    echo "  {$key}: '{$value}'\n";
                }
            }
        }
    }
    
    // 检查activities表的所有字段
    echo "\n=== activities表的所有字段 ===\n";
    $activityColumns = Schema::getColumnListing('activities');
    foreach ($activityColumns as $column) {
        echo "- {$column}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
