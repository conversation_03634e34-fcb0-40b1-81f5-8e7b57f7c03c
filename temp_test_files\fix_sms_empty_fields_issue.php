<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\Registration;
use Illuminate\Support\Facades\DB;

echo "=== SMS字段为空问题诊断和修复 ===\n";

// 1. 检查activity_details表结构
echo "\n1. 检查activity_details表结构:\n";
$columns = Schema::getColumnListing('activity_details');
echo "表字段: " . implode(', ', $columns) . "\n";

// 检查是否有topic和target_audience字段
$hasTopicField = in_array('topic', $columns);
$hasTargetAudienceField = in_array('target_audience', $columns);

echo "topic字段存在: " . ($hasTopicField ? '是' : '否') . "\n";
echo "target_audience字段存在: " . ($hasTargetAudienceField ? '是' : '否') . "\n";

// 2. 检查空字段的记录
echo "\n2. 检查存在空字段的记录:\n";
$emptyRecords = ActivityDetail::where(function($query) {
    $query->whereNull('activity_time')
          ->orWhereNull('target')
          ->orWhereNull('target_audience')
          ->orWhere('activity_time', '')
          ->orWhere('target', '')
          ->orWhere('target_audience', '');
})->get();

echo "发现 {$emptyRecords->count()} 条记录存在空字段\n";

foreach ($emptyRecords as $record) {
    echo "ID: {$record->id}, activity_time: '{$record->activity_time}', target: '{$record->target}', target_audience: '{$record->target_audience}'\n";
}

// 3. 检查最近的注册记录和对应的SMS发送
echo "\n3. 检查最近的注册记录:\n";
$recentRegistrations = Registration::with(['activityDetail.activity'])
    ->orderBy('created_at', 'desc')
    ->take(3)
    ->get();

foreach ($recentRegistrations as $registration) {
    echo "\n注册ID: {$registration->id}, 用户: {$registration->name}\n";
    $activityDetail = $registration->activityDetail;
    
    if ($activityDetail) {
        echo "  活动详情ID: {$activityDetail->id}\n";
        echo "  activity_time: " . ($activityDetail->activity_time ?? 'NULL') . "\n";
        echo "  target: " . ($activityDetail->target ?? 'NULL') . "\n";
        echo "  target_audience: " . ($activityDetail->target_audience ?? 'NULL') . "\n";
        echo "  topic: " . ($activityDetail->topic ?? 'NULL') . "\n";
        echo "  address: " . ($activityDetail->address ?? 'NULL') . "\n";
        
        // 模拟buildTemplateData方法
        $activity = $activityDetail->activity;
        
        // 构建模板数据
        $name = !empty($registration->name) ? $registration->name : "用户";
        
        // #topic# - 活动主题
        $topic = "";
        if (!empty($activityDetail->topic)) {
            $topic = $activityDetail->topic;
        } elseif (!empty($activity->title)) {
            $topic = $activity->title;
        } elseif (!empty($activityDetail->theme)) {
            $topic = $activityDetail->theme;
        } else {
            $topic = "活动通知";
        }
        
        // #time# - 活动时间
        $time = "";
        if (!empty($activityDetail->activity_time)) {
            try {
                $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
            } catch (Exception $e) {
                $time = "待定时间";
            }
        } else {
            $time = "待定时间";
        }
        
        // #address# - 活动地址
        $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
        
        // #obj# - 目标对象
        $obj = "";
        if (!empty($activityDetail->target_audience)) {
            $obj = $activityDetail->target_audience;
        } elseif (!empty($activityDetail->target)) {
            $obj = $activityDetail->target;
        } else {
            $obj = "全体人员";
        }
        
        echo "  构建的模板数据:\n";
        echo "    name: {$name}\n";
        echo "    topic: {$topic}\n";
        echo "    time: {$time}\n";
        echo "    address: {$address}\n";
        echo "    obj: {$obj}\n";
        
        // 生成SMS内容
        $smsContent = "【上海市嘉定区教育学院】现场咨询，主题为：{$topic}；时间为：{$time}；地点为：{$address}；对象为：{$obj}；请准时参加。";
        echo "  生成的SMS内容: {$smsContent}\n";
    }
}

// 4. 修复建议
echo "\n\n=== 修复建议 ===\n";

if (!$hasTopicField || !$hasTargetAudienceField) {
    echo "1. 需要运行数据库迁移添加缺失字段:\n";
    echo "   php artisan migrate\n";
}

if ($emptyRecords->count() > 0) {
    echo "2. 需要填充空字段数据:\n";
    echo "   - 可以从activity.title复制到activity_details.topic\n";
    echo "   - 可以设置默认的target_audience值\n";
    
    // 提供修复脚本
    echo "\n自动修复空字段 (y/n)? ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($input) === 'y') {
        echo "\n开始修复...\n";
        
        foreach ($emptyRecords as $record) {
            $updated = false;
            
            // 修复topic字段
            if (empty($record->topic) && $record->activity) {
                $record->topic = $record->activity->title ?? $record->theme ?? '活动通知';
                $updated = true;
                echo "修复记录 {$record->id} 的topic字段: {$record->topic}\n";
            }
            
            // 修复target_audience字段
            if (empty($record->target_audience)) {
                $record->target_audience = $record->target ?? '全体人员';
                $updated = true;
                echo "修复记录 {$record->id} 的target_audience字段: {$record->target_audience}\n";
            }
            
            // 修复activity_time字段
            if (empty($record->activity_time)) {
                $record->activity_time = now()->addDays(7); // 设置为一周后
                $updated = true;
                echo "修复记录 {$record->id} 的activity_time字段: {$record->activity_time}\n";
            }
            
            if ($updated) {
                $record->save();
            }
        }
        
        echo "修复完成！\n";
    }
}

echo "\n3. 确保ActivityDetail模型的fillable字段包含新字段:\n";
echo "   'topic', 'target_audience'\n";

echo "\n4. 验证RegistrationController中的buildTemplateData方法正确使用这些字段\n";

echo "\n=== 诊断完成 ===\n";