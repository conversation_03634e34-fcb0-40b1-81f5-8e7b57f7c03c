<?php

namespace App\Console\Commands;

use App\Models\WechatAccount;
use App\Services\WechatArticleCollectService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CollectWechatArticles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wechat:collect-articles 
                            {--account= : 指定公众号ID，不指定则采集所有启用自动采集的公众号}
                            {--count=20 : 每个公众号采集的文章数量}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '采集微信公众号文章';

    /**
     * Execute the console command.
     */
    public function handle(WechatArticleCollectService $collectService)
    {
        $accountId = $this->option('account');
        $count = (int) $this->option('count');
        
        $this->info('开始采集微信公众号文章...');
        
        if ($accountId) {
            // 采集指定公众号
            $account = WechatAccount::find($accountId);
            if (!$account) {
                $this->error("公众号 ID {$accountId} 不存在");
                return 1;
            }
            
            $this->collectAccount($account, $collectService, $count);
        } else {
            // 采集所有需要采集的公众号
            $accounts = WechatAccount::active()
                ->autoCollect()
                ->get()
                ->filter(fn($account) => $account->shouldCollect());
            
            if ($accounts->isEmpty()) {
                $this->info('没有需要采集的公众号');
                return 0;
            }
            
            $this->info("找到 {$accounts->count()} 个需要采集的公众号");
            
            foreach ($accounts as $account) {
                $this->collectAccount($account, $collectService, $count);
            }
        }
        
        $this->info('采集完成！');
        return 0;
    }
    
    /**
     * 采集单个公众号的文章
     */
    private function collectAccount(WechatAccount $account, WechatArticleCollectService $collectService, int $count): void
    {
        $this->line("正在采集公众号：{$account->name}");
        
        try {
            $articles = $collectService->collectFromAccount($account, $count);
            $this->info("  ✓ 成功采集 " . count($articles) . " 篇文章");
            
            foreach ($articles as $article) {
                $this->line("    - {$article->title}");
            }
        } catch (\Exception $e) {
            $this->error("  ✗ 采集失败：{$e->getMessage()}");
            
            Log::error('定时采集微信文章失败', [
                'account_id' => $account->id,
                'account_name' => $account->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}