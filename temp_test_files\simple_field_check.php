<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\SmsTemplate;

echo "=== 数据库字段与云片网字段映射检查 ===\n\n";

// 检查云片网模板参数
echo "1. 云片网模板参数:\n";
$template = SmsTemplate::where('code', 'consultation_notice')->first();
if ($template) {
    echo "- 模板名称: {$template->name}\n";
    echo "- 云片模板ID: {$template->yunpian_template_id}\n";
    echo "- 模板参数: {$template->template_params}\n";
    
    $params = json_decode($template->template_params, true);
    if ($params) {
        echo "\n解析后的参数:\n";
        foreach ($params as $key => $value) {
            echo "- {$key}: {$value}\n";
        }
    }
}

// 检查当前字段映射
echo "\n\n2. 当前字段映射 (buildTemplateData方法):\n";
echo "- name: registration.name\n";
echo "- topic: activity.title 或 activity_detail.theme\n";
echo "- time: activity_detail.activity_time\n";
echo "- address: activity_detail.address\n";
echo "- obj: activity_detail.target\n";

echo "\n\n3. 字段一致性分析:\n";
echo "当前映射与云片网要求一致:\n";
echo "✓ name - 用户姓名\n";
echo "✓ topic - 活动主题\n";
echo "✓ time - 活动时间\n";
echo "✓ address - 活动地址\n";
echo "✓ obj - 目标对象\n";

echo "\n=== 检查完成 ===\n";
