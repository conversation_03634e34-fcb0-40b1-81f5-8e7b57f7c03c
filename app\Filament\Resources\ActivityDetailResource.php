<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ActivityDetailResource\Pages;
use App\Models\ActivityDetail;
use App\Models\SmsTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Encoding\Encoding;
use Filament\Support\Enums\ActionSize;
use Filament\Actions\Action as BaseAction;

class ActivityDetailResource extends Resource
{
    protected static ?string $model = ActivityDetail::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationLabel = '活动场次';
    protected static ?string $navigationGroup = '报名系统';
    protected static ?int $navigationGroupSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('activity_id')
                    ->label('所属活动')
                    ->relationship('activity', 'title')
                    ->required(),
                
                // 基础活动信息
                Forms\Components\Section::make('基础活动信息')
                    ->schema([
                        Forms\Components\TextInput::make('topic')
                            ->label('活动主题')
                            ->required()
                            ->maxLength(255)
                            ->helperText('对应云片网模板参数 #topic#'),
                        Forms\Components\TextInput::make('obj')
                            ->label('报名对象')
                            ->required()
                            ->maxLength(255)
                            ->helperText('对应云片网模板参数 #obj#'),
                        Forms\Components\TextInput::make('address')
                            ->label('活动地点')
                            ->required()
                            ->helperText('对应云片网模板参数 #address#'),
                        Forms\Components\DateTimePicker::make('time')
                            ->label('活动时间')
                            ->required()
                            ->displayFormat('Y年n月j日 H:i')
                            ->format('Y-m-d H:i:s')
                            ->helperText('对应云片网模板参数 #time#，格式如：2025年1月21日 14:30'),
                        Forms\Components\DateTimePicker::make('activity_time')
                            ->label('开始时间（系统用）')
                            ->required()
                            ->helperText('系统内部使用的时间字段'),
                        Forms\Components\TextInput::make('code')
                            ->label('活动代码')
                            ->maxLength(255)
                            ->helperText('对应云片网模板参数 #code#，活动的唯一标识码，如：ACT001'),
                        Forms\Components\TextInput::make('name')
                            ->label('活动名称')
                            ->maxLength(255)
                            ->helperText('对应云片网模板参数 #name#，短信中显示的活动名称'),
                        Forms\Components\TextInput::make('pwd')
                            ->label('密码/验证码')
                            ->maxLength(255)
                            ->helperText('对应云片网模板参数 #pwd#，活动验证码或密码，可选填'),
                        Forms\Components\Select::make('sms_template_id')
                            ->label('短信模板ID')
                            ->options(function () {
                                return SmsTemplate::all()->mapWithKeys(function ($template) {
                                    return [$template->id => 'ID:' . $template->id . ' - ' . $template->name . ' (' . $template->code . ')'];
                                });
                            })
                            ->searchable()
                            ->placeholder('选择短信模板')
                            ->helperText('选择用于发送短信通知的模板'),
                    ])
                    ->columns(2),
                
                // 报名设置
                Forms\Components\Section::make('报名设置')
                    ->schema([
                        Forms\Components\TextInput::make('fee')
                            ->label('报名费用')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('quota')
                            ->label('报名限额')
                            ->required()
                            ->numeric()
                            ->default(0),
                        Forms\Components\TextInput::make('current_count')
                            ->label('当前报名数')
                            ->required()
                            ->numeric()
                            ->default(0),
                        Forms\Components\DateTimePicker::make('registration_deadline')
                            ->label('报名截止时间')
                            ->required(),
                        Forms\Components\Select::make('registration_method')
                            ->label('报名方式')
                            ->options([
                                'online' => '在线报名',
                                'onsite' => '现场报名',
                                'other' => '其他',
                            ])
                            ->required(),
                        Forms\Components\Textarea::make('reminder')
                            ->label('友情提醒')
                            ->maxLength(255)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                

                
                Forms\Components\Toggle::make('status')
                    ->label('启用状态')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('activity.title')
                    ->label('所属活动')
                    ->searchable(),
                Tables\Columns\TextColumn::make('topic')
                    ->label('活动主题')
                    ->searchable(),
                Tables\Columns\TextColumn::make('obj')
                    ->label('报名对象')
                    ->searchable(),
                Tables\Columns\TextColumn::make('time')
                    ->label('活动时间')
                    ->searchable(),
                Tables\Columns\TextColumn::make('activity_time')
                    ->label('开始时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('address')
                    ->label('活动地点')
                    ->searchable(),
                Tables\Columns\TextColumn::make('code')
                    ->label('活动代码')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('活动名称')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quota')
                    ->label('报名限额')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_count')
                    ->label('当前报名数')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('registration_deadline')
                    ->label('报名截止时间')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sms_template_id')
                    ->label('短信模板ID')
                    ->searchable()
                    ->placeholder('未设置')
                    ->toggleable(),
                Tables\Columns\IconColumn::make('status')
                    ->label('启用状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('activity')
                    ->relationship('activity', 'title'),
                Tables\Filters\SelectFilter::make('sms_template')
                    ->label('短信模板')
                    ->relationship('smsTemplate', 'name')
                    ->searchable(),
                Tables\Filters\TernaryFilter::make('status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Action::make('qrcode')
                    ->label('生成二维码')
                    ->icon('heroicon-o-qr-code')
                    ->modalContent(function (ActivityDetail $record) {
                        $url = route('registration.form', ['id' => $record->id]);
                        
                        $qrCode = new QrCode($url);
                        $qrCode->setSize(300);
                        $qrCode->setMargin(10);
                        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::High);
                        $qrCode->setEncoding(new Encoding('UTF-8'));
                        
                        $writer = new PngWriter();
                        $result = $writer->write($qrCode);
                        
                        return view('filament.resources.activity-detail-resource.qrcode-preview', [
                            'qrcode' => base64_encode($result->getString()),
                            'url' => $url
                        ]);
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->extraModalFooterActions([
                        BaseAction::make('download')
                            ->label('下载二维码')
                            ->icon('heroicon-o-arrow-down-tray')
                            ->action(function (ActivityDetail $record) {
                                $url = route('registration.form', ['id' => $record->id]);
                                
                                $qrCode = new QrCode($url);
                                $qrCode->setSize(300);
                                $qrCode->setMargin(10);
                                $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::High);
                                $qrCode->setEncoding(new Encoding('UTF-8'));
                                
                                $writer = new PngWriter();
                                $result = $writer->write($qrCode);
                                
                                return response()->streamDownload(function () use ($result) {
                                    echo $result->getString();
                                }, 'qrcode.png', [
                                    'Content-Type' => 'image/png',
                                ]);
                            })
                            ->size(ActionSize::Large)
                            ->color('primary'),
                    ])
                    ->modalWidth('md'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivityDetails::route('/'),
            'create' => Pages\CreateActivityDetail::route('/create'),
            'edit' => Pages\EditActivityDetail::route('/{record}/edit'),
        ];
    }
}