<?php

echo "=== 更新RegistrationController以匹配云片网字段 ===\n\n";

$controllerFile = 'app/Http/Controllers/RegistrationController.php';

if (!file_exists($controllerFile)) {
    echo "✗ 控制器文件不存在: {$controllerFile}\n";
    exit(1);
}

// 备份原文件
$backupFile = $controllerFile . '.backup.' . date('YmdHis');
copy($controllerFile, $backupFile);
echo "✓ 已备份原文件到: {$backupFile}\n";

// 读取文件内容
$content = file_get_contents($controllerFile);

// 更新buildTemplateData方法以使用更直观的字段映射
$oldBuildMethod = '/private function buildTemplateData\(\$registration\)\s*\{[^}]+\}/s';

$newBuildMethod = 'private function buildTemplateData($registration)
    {
        $activityDetail = $registration->activityDetail;
        $activity = $activityDetail->activity;
        
        // 云片网字段映射 - 使用更直观的字段名
        // #name# - 用户姓名
        $name = !empty($registration->name) ? $registration->name : "用户";
        
        // #topic# - 活动主题 (优先使用新的topic字段，然后是activity.title，最后是theme)
        $topic = "";
        if (!empty($activityDetail->topic)) {
            $topic = $activityDetail->topic;  // 新增的直观字段
        } elseif (!empty($activity->title)) {
            $topic = $activity->title;
        } elseif (!empty($activityDetail->theme)) {
            $topic = $activityDetail->theme;  // 兼容旧字段
        } else {
            $topic = "活动通知";
        }
        
        // #time# - 活动时间
        $time = "";
        if (!empty($activityDetail->activity_time)) {
            try {
                $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
            } catch (Exception $e) {
                $time = "待定时间";
            }
        } else {
            $time = "待定时间";
        }
        
        // #address# - 活动地址
        $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
        
        // #obj# - 目标对象 (优先使用新的target_audience字段，然后是target)
        $obj = "";
        if (!empty($activityDetail->target_audience)) {
            $obj = $activityDetail->target_audience;  // 新增的直观字段
        } elseif (!empty($activityDetail->target)) {
            $obj = $activityDetail->target;  // 兼容旧字段
        } else {
            $obj = "全体人员";
        }
        
        $templateData = [
            "name" => $name,      // 对应云片网 #name#
            "topic" => $topic,    // 对应云片网 #topic#
            "time" => $time,      // 对应云片网 #time#
            "address" => $address, // 对应云片网 #address#
            "obj" => $obj         // 对应云片网 #obj#
        ];
        
        // 记录日志以便调试
        \Log::info("构建云片网SMS模板数据", [
            "registration_id" => $registration->id,
            "activity_detail_id" => $activityDetail->id,
            "activity_id" => $activity->id,
            "yunpian_template_data" => $templateData
        ]);
        
        return $templateData;
    }';

// 执行替换
if (preg_match($oldBuildMethod, $content)) {
    $content = preg_replace($oldBuildMethod, $newBuildMethod, $content);
    
    // 写入更新后的内容
    file_put_contents($controllerFile, $content);
    
    echo "✓ 已更新buildTemplateData方法\n";
    echo "\n更新内容：\n";
    echo "- 优先使用activity_details.topic字段映射#topic#参数\n";
    echo "- 优先使用activity_details.target_audience字段映射#obj#参数\n";
    echo "- 保持向后兼容，支持旧字段theme和target\n";
    echo "- 添加了云片网字段映射注释\n";
    echo "- 更新了日志记录\n";
    
} else {
    echo "✗ 未找到buildTemplateData方法，可能已经被修改\n";
}

echo "\n=== 控制器更新完成 ===\n";
