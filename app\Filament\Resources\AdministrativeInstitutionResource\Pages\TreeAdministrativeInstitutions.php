<?php

namespace App\Filament\Resources\AdministrativeInstitutionResource\Pages;

use App\Filament\Resources\AdministrativeInstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use SolutionForest\FilamentTree\Components\Tree;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class TreeAdministrativeInstitutions extends ListRecords
{
    protected static string $resource = AdministrativeInstitutionResource::class;
    
    public ?string $selectedLevel = null;
    
    protected $listeners = ['refreshTree' => 'refreshTreeData'];
    
    public function updatedSelectedLevel()
    {
        $this->refreshTreeData();
    }
    
    public function refreshTreeData()
    {
        // 强制刷新组件状态
        $this->resetPage();
        $this->dispatch('$refresh');
        // 强制重新渲染整个组件
        $this->dispatch('refreshTree');
    }

    public function tree(): Tree
    {
        return Tree::make($this)
            ->primaryColumn('name')
            ->parentColumn('parent_id')
            ->enableSorting()
            ->sortColumn('sort_order')
            ->enableDragAndDrop()
            ->query(function (Builder $query) {
                if ($this->selectedLevel) {
                    // 根据选择的级别筛选数据，只显示该级别的机构
                    switch ($this->selectedLevel) {
                        case 'province':
                            // 只显示省级机构（顶级，parent_id为null）
                            $query->where('administrative_level', 'province')
                                  ->whereNull('parent_id');
                            break;
                        case 'city':
                            // 显示省级机构和市级机构，保持层级关系
                            $query->where(function ($q) {
                                $q->where('administrative_level', 'province')
                                  ->whereNull('parent_id');
                            })->orWhere(function ($q) {
                                $q->where('administrative_level', 'city')
                                  ->whereHas('parent', function ($parent) {
                                      $parent->where('administrative_level', 'province');
                                  });
                            });
                            break;
                        case 'district':
                            // 显示省级、市级和区县级机构，保持层级关系
                            $query->where(function ($q) {
                                $q->where('administrative_level', 'province')
                                  ->whereNull('parent_id');
                            })->orWhere(function ($q) {
                                $q->where('administrative_level', 'city')
                                  ->whereHas('parent', function ($parent) {
                                      $parent->where('administrative_level', 'province');
                                  });
                            })->orWhere(function ($q) {
                                $q->where('administrative_level', 'district')
                                  ->whereHas('parent', function ($parent) {
                                      $parent->where('administrative_level', 'city');
                                  });
                            });
                            break;
                        case 'town':
                            // 显示所有级别的机构，保持完整的层级关系
                            $query->where(function ($q) {
                                $q->where('administrative_level', 'province')
                                  ->whereNull('parent_id');
                            })->orWhere(function ($q) {
                                $q->where('administrative_level', 'city')
                                  ->whereHas('parent', function ($parent) {
                                      $parent->where('administrative_level', 'province');
                                  });
                            })->orWhere(function ($q) {
                                $q->where('administrative_level', 'district')
                                  ->whereHas('parent', function ($parent) {
                                      $parent->where('administrative_level', 'city');
                                  });
                            })->orWhere(function ($q) {
                                $q->where('administrative_level', 'town')
                                  ->whereHas('parent', function ($parent) {
                                      $parent->where('administrative_level', 'district');
                                  });
                            });
                            break;
                    }
                } else {
                    // 显示全部时，确保层级关系正确
                    $query->where(function ($q) {
                        $q->where('administrative_level', 'province')
                          ->whereNull('parent_id');
                    })->orWhere(function ($q) {
                        $q->where('administrative_level', 'city')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'province');
                          });
                    })->orWhere(function ($q) {
                        $q->where('administrative_level', 'district')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'city');
                          });
                    })->orWhere(function ($q) {
                        $q->where('administrative_level', 'town')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'district');
                          });
                    });
                }
                return $query->with(['parent', 'children'])->orderBy('sort_order')->orderBy('name');
            })
            ->columns([
                TextColumn::make('name')
                    ->label('单位名称')
                    ->searchable()
                    ->limit(50),
                // 机构代码列已移除：字段未被实际使用
                TextColumn::make('full_region_name')
                    ->label('地区')
                    ->getStateUsing(function ($record) {
                        // 确保地区信息正确显示
                        $parts = array_filter([
                            $record->province_name,
                            $record->city_name,
                            $record->district_name,
                            $record->town_name
                        ]);
                        return implode(' - ', $parts) ?: '未设置';
                    })
                    ->limit(40)
                    ->toggleable(),
                BadgeColumn::make('administrative_level')
                    ->label('行政级别')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'province' => '省级',
                        'city' => '市级',
                        'district' => '区县级',
                        'town' => '乡镇级',
                        default => '未知',
                    })
                    ->colors([
                        'danger' => 'province',
                        'warning' => 'city',
                        'success' => 'district',
                        'primary' => 'town',
                    ]),
                TextColumn::make('host_unit')
                    ->label('主办单位')
                    ->limit(30)
                    ->toggleable(),
                BadgeColumn::make('binding_status')
                    ->label('绑定状态')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'bound' => '已绑定',
                        'unbound' => '未绑定',
                        default => '未知',
                    })
                    ->colors([
                        'success' => 'bound',
                        'danger' => 'unbound',
                    ])
                    ->toggleable(),
                IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->toggleable(),
            ])
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->label('查看'),
                \Filament\Tables\Actions\EditAction::make()
                    ->label('编辑'),
                \Filament\Tables\Actions\DeleteAction::make()
                    ->label('删除'),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
             Actions\Action::make('filter_province')
                 ->label('省级')
                 ->icon('heroicon-o-building-office-2')
                 ->color($this->selectedLevel === 'province' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'province')
                 ->action(function () {
                     $this->selectedLevel = 'province';
                     $this->js('window.location.reload()');
                 })
                 ->tooltip('点击筛选省级机构'),
             Actions\Action::make('filter_city')
                 ->label('市级')
                 ->icon('heroicon-o-building-office')
                 ->color($this->selectedLevel === 'city' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'city')
                 ->action(function () {
                     $this->selectedLevel = 'city';
                     $this->js('window.location.reload()');
                 })
                 ->tooltip('点击筛选省级和市级机构'),
             Actions\Action::make('filter_district')
                 ->label('区县级')
                 ->icon('heroicon-o-map')
                 ->color($this->selectedLevel === 'district' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'district')
                 ->action(function () {
                     $this->selectedLevel = 'district';
                     $this->js('window.location.reload()');
                 })
                 ->tooltip('点击筛选省级、市级和区县级机构'),
             Actions\Action::make('filter_town')
                 ->label('乡镇级')
                 ->icon('heroicon-o-home')
                 ->color($this->selectedLevel === 'town' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'town')
                 ->action(function () {
                     $this->selectedLevel = 'town';
                     $this->js('window.location.reload()');
                 })
                 ->tooltip('点击筛选所有级别机构'),
             Actions\Action::make('clear_filter')
                 ->label('显示全部')
                 ->icon('heroicon-o-x-mark')
                 ->color('gray')
                 ->outlined()
                 ->action(function () {
                     $this->selectedLevel = null;
                     $this->js('window.location.reload()');
                 })
                 ->tooltip('清除筛选，显示所有机构'),
             Actions\Action::make('list_view')
                 ->label('列表视图')
                 ->icon('heroicon-o-list-bullet')
                 ->color('info')
                 ->url(static::getResource()::getUrl('index'))
                 ->tooltip('切换到列表视图管理'),
             Actions\CreateAction::make()
                 ->label('新增行政机构'),
        ];
    }

    public function getTitle(): string
    {
        return '行政机构树形管理';
    }

    public function getHeading(): string
    {
        return '行政机构树形管理';
    }

    public function getSubheading(): ?string
    {
        return '通过树形结构管理行政机构的层级关系，支持拖拽排序';
    }
}