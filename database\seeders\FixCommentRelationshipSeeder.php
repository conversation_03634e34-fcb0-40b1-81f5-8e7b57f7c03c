<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Comment;
use App\Models\Article;
use App\Models\Post;
use App\Models\User;

class FixCommentRelationshipSeeder extends Seeder
{
    /**
     * 修复评论关联关系问题
     */
    public function run(): void
    {
        $this->command->info('🔧 修复评论关联关系...');

        // 检查当前评论状况
        $totalComments = Comment::count();
        $commentsWithPost = Comment::whereNotNull('post_id')->count();
        $commentsWithArticle = Comment::whereNotNull('article_id')->count();
        $orphanedComments = Comment::whereNull('post_id')->whereNull('article_id')->count();

        $this->command->info("评论总数: {$totalComments}");
        $this->command->info("关联Post的评论: {$commentsWithPost}");
        $this->command->info("关联Article的评论: {$commentsWithArticle}");
        $this->command->info("孤立评论: {$orphanedComments}");

        if ($orphanedComments > 0) {
            $this->fixOrphanedComments();
        }

        // 验证修复结果
        $this->validateCommentRelationships();
    }

    /**
     * 修复孤立的评论
     */
    private function fixOrphanedComments()
    {
        $this->command->info('');
        $this->command->info('🔄 修复孤立评论...');

        // 获取所有文章
        $articles = Article::all();
        $users = User::all();

        if ($articles->isEmpty()) {
            $this->command->warn('没有找到文章，无法修复评论关联');
            return;
        }

        // 获取孤立的评论
        $orphanedComments = Comment::whereNull('post_id')->whereNull('article_id')->get();

        $fixedCount = 0;
        foreach ($orphanedComments as $comment) {
            // 随机分配给一篇文章
            $randomArticle = $articles->random();
            $randomUser = $users->random();

            $comment->update([
                'article_id' => $randomArticle->id,
                'user_id' => $randomUser->id,
                'status' => 'approved', // 设置为已审核
            ]);

            $fixedCount++;
        }

        $this->command->info("✅ 已修复 {$fixedCount} 条孤立评论");

        // 更新文章的评论计数
        $this->updateArticleCommentCounts();
    }

    /**
     * 更新文章评论计数
     */
    private function updateArticleCommentCounts()
    {
        $this->command->info('🔄 更新文章评论计数...');

        Article::chunk(50, function ($articles) {
            foreach ($articles as $article) {
                $commentCount = $article->comments()->where('status', 'approved')->count();
                $article->update(['comment_count' => $commentCount]);
            }
        });

        $this->command->info('✅ 文章评论计数已更新');
    }

    /**
     * 验证评论关联关系
     */
    private function validateCommentRelationships()
    {
        $this->command->info('');
        $this->command->info('🔍 验证评论关联关系...');

        $totalComments = Comment::count();
        $commentsWithPost = Comment::whereNotNull('post_id')->count();
        $commentsWithArticle = Comment::whereNotNull('article_id')->count();
        $orphanedComments = Comment::whereNull('post_id')->whereNull('article_id')->count();
        $approvedComments = Comment::where('status', 'approved')->count();

        $this->command->info("评论总数: {$totalComments}");
        $this->command->info("关联Post的评论: {$commentsWithPost}");
        $this->command->info("关联Article的评论: {$commentsWithArticle}");
        $this->command->info("孤立评论: {$orphanedComments}");
        $this->command->info("已审核评论: {$approvedComments}");

        if ($orphanedComments === 0) {
            $this->command->info('✅ 所有评论都有正确的关联关系！');
        } else {
            $this->command->error("❌ 仍有 {$orphanedComments} 条孤立评论");
        }

        // 验证文章评论关联
        $this->command->info('');
        $this->command->info('📊 文章评论统计:');
        
        $articlesWithComments = Article::has('comments')->count();
        $totalArticles = Article::count();
        
        $this->command->info("总文章数: {$totalArticles}");
        $this->command->info("有评论的文章: {$articlesWithComments}");

        // 显示前5篇文章的评论数
        $topArticles = Article::withCount('comments')
            ->orderBy('comments_count', 'desc')
            ->limit(5)
            ->get();

        $this->command->info('');
        $this->command->info('📈 评论最多的文章:');
        foreach ($topArticles as $article) {
            $this->command->info("  - {$article->title}: {$article->comments_count} 条评论");
        }

        // 检查评论状态分布
        $this->command->info('');
        $this->command->info('📋 评论状态分布:');
        $statusCounts = Comment::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();

        foreach ($statusCounts as $status) {
            $this->command->info("  - {$status->status}: {$status->count} 条");
        }
    }
}
