<?php

namespace App\Jobs;

use App\Models\WechatArticle;
use App\Services\WechatArticleCollectService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ConvertWechatArticleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180; // 3分钟超时
    public $tries = 2; // 最多重试2次

    /**
     * Create a new job instance.
     */
    public function __construct(
        public WechatArticle $wechatArticle,
        public ?int $categoryId = null
    ) {
        $this->onQueue('wechat-convert');
    }

    /**
     * Execute the job.
     */
    public function handle(WechatArticleCollectService $collectService): void
    {
        Log::info('开始转换微信文章为本地文章', [
            'wechat_article_id' => $this->wechatArticle->id,
            'title' => $this->wechatArticle->title,
            'category_id' => $this->categoryId
        ]);

        try {
            // 检查是否已经转换过
            if ($this->wechatArticle->article_id) {
                Log::warning('微信文章已经转换过，跳过', [
                    'wechat_article_id' => $this->wechatArticle->id,
                    'article_id' => $this->wechatArticle->article_id
                ]);
                return;
            }

            $article = $collectService->convertToArticle($this->wechatArticle, $this->categoryId);
            
            Log::info('微信文章转换完成', [
                'wechat_article_id' => $this->wechatArticle->id,
                'article_id' => $article->id,
                'title' => $article->title
            ]);
        } catch (\Exception $e) {
            Log::error('微信文章转换失败', [
                'wechat_article_id' => $this->wechatArticle->id,
                'title' => $this->wechatArticle->title,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 标记转换失败
            $this->wechatArticle->markAsFailed($e->getMessage());
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('微信文章转换任务失败', [
            'wechat_article_id' => $this->wechatArticle->id,
            'title' => $this->wechatArticle->title,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        // 标记转换失败
        $this->wechatArticle->markAsFailed($exception->getMessage());
    }
}