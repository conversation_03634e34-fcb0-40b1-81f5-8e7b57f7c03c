/**
 * CKEditor 5 自定义视频上传功能
 * 为微信文章编辑器添加本地视频上传功能
 */

// 视频上传功能
function uploadVideoFile(file, editor) {
    // 验证文件类型
    if (!file.type.startsWith('video/')) {
        alert('请选择视频文件');
        return;
    }

    // 验证文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('视频文件大小不能超过50MB');
        return;
    }

    // 创建FormData
    const formData = new FormData();
    formData.append('upload', file);

    // 获取CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // 显示上传进度
    const notification = showUploadProgress();

    // 发送上传请求
    fetch('/admin/ckeditor/upload', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        hideUploadProgress(notification);

        if (data.url) {
            // 插入视频到编辑器
            insertVideoElement(data.url, file.name, editor);
        } else {
            alert('视频上传失败：' + (data.error?.message || '未知错误'));
        }
    })
    .catch(error => {
        hideUploadProgress(notification);
        console.error('视频上传错误:', error);
        alert('视频上传失败：网络错误');
    });
}

// 插入视频元素到编辑器
function insertVideoElement(videoUrl, fileName, editor) {
    // 创建视频HTML
    const videoHtml = `
        <figure class="media">
            <video controls style="width: 100%; max-width: 100%; height: auto;">
                <source src="${videoUrl}" type="video/mp4">
                <source src="${videoUrl}" type="video/webm">
                您的浏览器不支持视频播放。
            </video>
            <figcaption>视频: ${fileName}</figcaption>
        </figure>
    `;

    // 插入到编辑器
    editor.model.change(writer => {
        const viewFragment = editor.data.processor.toView(videoHtml);
        const modelFragment = editor.data.toModel(viewFragment);
        editor.model.insertContent(modelFragment);
    });
}

// 显示上传进度
function showUploadProgress() {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #007cba;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        z-index: 9999;
        font-size: 14px;
    `;
    notification.textContent = '正在上传视频...';
    document.body.appendChild(notification);
    return notification;
}

// 隐藏上传进度
function hideUploadProgress(notification) {
    if (notification && notification.parentNode) {
        notification.parentNode.removeChild(notification);
    }
}

// 创建视频上传按钮功能
function createVideoUploadButton() {
    return function(editor) {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'video/*';
        fileInput.style.display = 'none';

        document.body.appendChild(fileInput);

        fileInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                uploadVideoFile(file, editor);
            }
            document.body.removeChild(fileInput);
        });

        fileInput.click();
    };
}

// 全局暴露函数
window.createVideoUploadButton = createVideoUploadButton;
