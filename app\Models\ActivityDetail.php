<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ActivityDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'activity_id',
        'activity_time',
        'address',
        'fee',
        'quota',
        'current_count',
        'deadline',
        'registration_deadline',
        'registration_method',
        'reminder',
        'status',
        'sms_template',
        'sms_template_id',
        'wechat_template_id',
        // 云片网短信模板直接对应字段
        'topic', // #topic# - 活动主题
        'time',  // #time# - 活动时间
        'obj',   // #obj# - 报名对象
        'code',  // #code# - 活动代码
        'name',  // #name# - 活动名称
        'pwd',   // #pwd# - 密码/验证码
        // 保留字段（向后兼容）
        'theme',
        'target',
        'target_audience',
    ];

    protected $casts = [
        'activity_time' => 'datetime',
        'time' => 'datetime',
        'registration_deadline' => 'datetime',
        'status' => 'boolean',
    ];

    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }

    public function registrations(): HasMany
    {
        return $this->hasMany(Registration::class);
    }

    public function wechatTemplate(): BelongsTo
    {
        return $this->belongsTo(WechatTemplate::class);
    }

    // 新增：短信模板关联
    public function smsTemplate(): BelongsTo
    {
        return $this->belongsTo(SmsTemplate::class);
    }

    /**
     * 获取短信模板内容（兼容旧数据）
     */
    public function getSmsTemplateContent(): ?string
    {
        // 优先使用新的模板关联
        if ($this->smsTemplate) {
            return $this->smsTemplate->description;
        }
        
        // 兼容旧的直接存储内容
        return $this->sms_template;
    }

    /**
     * 检查是否使用云片网模板
     */
    public function usesYunpianTemplate(): bool
    {
        return $this->sms_template_id && $this->smsTemplate && $this->smsTemplate->yunpian_template_id;
    }

    /**
     * 获取格式化的活动时间（用于短信模板）
     */
    public function getFormattedTimeAttribute(): string
    {
        if ($this->time) {
            return $this->time->format('Y年n月j日 H:i');
        }
        return '';
    }
}