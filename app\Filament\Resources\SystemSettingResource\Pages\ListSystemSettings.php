<?php

namespace App\Filament\Resources\SystemSettingResource\Pages;

use App\Filament\Resources\SystemSettingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Models\SystemSetting;
use Filament\Notifications\Notification;

class ListSystemSettings extends ListRecords
{
    protected static string $resource = SystemSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('cleanAuditLogs')
                ->label('清理审计日志')
                ->icon('heroicon-o-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('清理过期审计日志')
                ->modalDescription('此操作将删除超过保留期限的审计日志，是否继续？')
                ->action(function () {
                    $days = SystemSetting::getAuditLogRetentionDays();
                    
                    try {
                        \Artisan::call('audit:clean', ['--days' => $days]);
                        
                        Notification::make()
                            ->title('清理完成')
                            ->body('已成功清理过期的审计日志')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('清理失败')
                            ->body('清理审计日志时发生错误：' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }
}