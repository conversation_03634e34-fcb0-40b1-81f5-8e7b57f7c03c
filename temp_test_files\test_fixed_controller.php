<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Http\Controllers\RegistrationController;
use App\Services\SmsService;
use App\Services\WechatService;
use Illuminate\Support\Facades\Log;

echo "=== 测试修复后的RegistrationController ===\n";

try {
    // 获取一个测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到注册记录\n";
        exit;
    }
    
    echo "注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    echo "手机号: {$registration->phone}\n";
    
    // 创建控制器实例（模拟依赖注入）
    $smsService = new class {
        public function sendSms($phone, $templateCode, $data) {
            echo "\n=== 模拟SMS发送 ===\n";
            echo "手机号: {$phone}\n";
            echo "模板代码: {$templateCode}\n";
            echo "模板数据:\n";
            foreach ($data as $key => $value) {
                echo "  {$key}: '{$value}'\n";
            }
            
            // 检查关键字段
            if (empty($data['time'])) {
                echo "❌ time字段为空\n";
                return false;
            }
            if (empty($data['obj'])) {
                echo "❌ obj字段为空\n";
                return false;
            }
            
            echo "✅ 所有字段都有值\n";
            return true;
        }
    };
    
    $wechatService = new class {
        // 空的微信服务模拟
    };
    
    $controller = new RegistrationController($smsService, $wechatService);
    
    // 使用反射调用私有方法
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('buildTemplateData');
    $method->setAccessible(true);
    
    // 测试1：只传registration参数
    echo "\n=== 测试1：只传registration参数 ===\n";
    $templateData1 = $method->invoke($controller, $registration);
    foreach ($templateData1 as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 测试2：传递所有参数
    echo "\n=== 测试2：传递所有参数 ===\n";
    $templateData2 = $method->invoke($controller, $registration, $registration->activityDetail, 'activity_registration_confirm');
    foreach ($templateData2 as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 测试SMS发送
    echo "\n=== 测试SMS发送 ===\n";
    $result = $smsService->sendSms($registration->phone, 'activity_registration_confirm', $templateData2);
    
    if ($result) {
        echo "✅ SMS发送测试成功\n";
    } else {
        echo "❌ SMS发送测试失败\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
