{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "vite": "^6.0.11", "vue": "^3.5.16"}, "dependencies": {"@tiptap/core": "^2.12.0", "@tiptap/extension-youtube": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/vue-3": "^2.12.0"}}