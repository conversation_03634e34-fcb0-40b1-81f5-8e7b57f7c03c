<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserPoint;
use App\Services\PointService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AchievementController extends Controller
{
    protected $pointService;

    public function __construct(PointService $pointService)
    {
        $this->pointService = $pointService;
    }

    /**
     * 成就中心页面
     */
    public function achievementCenter()
    {
        $user = Auth::user();
        
        if (!$user || !$user->isVerified()) {
            return view('achievement.not-verified');
        }

        // 获取用户积分统计
        $userStats = [
            'total_points' => $user->total_points,
            'month_points' => $user->month_points,
            'quarter_points' => $user->quarter_points,
            'year_points' => $user->year_points,
            'total_ranking' => $this->pointService->getUserRanking($user, 'total'),
            'year_ranking' => $this->pointService->getUserRanking($user, 'year'),
        ];

        // 获取排行榜数据
        $rankings = User::where('is_verified', true)
            ->whereHas('points')
            ->withSum('points as total_points', 'points')
            ->orderByDesc('total_points')
            ->limit(10)
            ->get();

        return view('achievement.center', compact('userStats', 'rankings'));
    }

    /**
     * 积分中心页面
     */
    public function pointCenter()
    {
        // 获取地区排行榜
        $regionRankings = User::where('is_verified', true)
            ->whereHas('points')
            ->select('region')
            ->selectRaw('COUNT(*) as user_count')
            ->selectRaw('SUM((SELECT SUM(points) FROM user_points WHERE user_points.user_id = users.id)) as total_points')
            ->selectRaw('SUM((SELECT SUM(points) FROM user_points WHERE user_points.user_id = users.id AND YEAR(earned_date) = YEAR(NOW()) AND MONTH(earned_date) = MONTH(NOW()))) as month_points')
            ->selectRaw('SUM((SELECT SUM(points) FROM user_points WHERE user_points.user_id = users.id AND YEAR(earned_date) = YEAR(NOW()) AND QUARTER(earned_date) = QUARTER(NOW()))) as quarter_points')
            ->selectRaw('SUM((SELECT SUM(points) FROM user_points WHERE user_points.user_id = users.id AND YEAR(earned_date) = YEAR(NOW()))) as year_points')
            ->groupBy('region')
            ->whereNotNull('region')
            ->orderByDesc('total_points')
            ->limit(10)
            ->get();

        return view('achievement.point-center', compact('regionRankings'));
    }

    /**
     * API: 记录用户行为
     */
    public function trackBehavior(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => '用户未登录']);
        }

        $user = Auth::user();
        $action = $request->input('action');
        $targetType = $request->input('target_type');
        $targetId = $request->input('target_id');
        $duration = $request->input('duration', 0);
        $url = $request->input('url');

        $result = false;

        switch ($action) {
            case 'browse':
                $result = $this->pointService->recordBrowsePoints(
                    $user, 
                    $targetType, 
                    $targetId, 
                    $duration, 
                    $url
                );
                break;
            
            case 'video_view':
                $result = $this->pointService->recordVideoPoints(
                    $user, 
                    $targetId, 
                    $duration, 
                    $url
                );
                break;
        }

        return response()->json([
            'success' => $result,
            'message' => $result ? '行为记录成功' : '行为记录失败或不符合积分条件'
        ]);
    }

    /**
     * API: 获取用户积分信息
     */
    public function getUserPoints()
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => '用户未登录']);
        }

        $user = Auth::user();
        
        if (!$user->isVerified()) {
            return response()->json(['success' => false, 'message' => '用户未实名认证']);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'total_points' => $user->total_points,
                'month_points' => $user->month_points,
                'quarter_points' => $user->quarter_points,
                'year_points' => $user->year_points,
                'total_ranking' => $this->pointService->getUserRanking($user, 'total'),
                'year_ranking' => $this->pointService->getUserRanking($user, 'year'),
            ]
        ]);
    }

    /**
     * API: 获取排行榜数据
     */
    public function getRankings(Request $request)
    {
        $type = $request->input('type', 'total'); // total, month, quarter, year
        $limit = $request->input('limit', 10);

        $query = User::where('is_verified', true)
            ->whereHas('points');

        switch ($type) {
            case 'month':
                $query->withSum(['points as total_points' => function ($q) {
                    $q->whereYear('earned_date', now()->year)
                      ->whereMonth('earned_date', now()->month);
                }], 'points');
                break;
            case 'quarter':
                $quarter = ceil(now()->month / 3);
                $startMonth = ($quarter - 1) * 3 + 1;
                $endMonth = $quarter * 3;
                $query->withSum(['points as total_points' => function ($q) use ($startMonth, $endMonth) {
                    $q->whereYear('earned_date', now()->year)
                      ->whereMonth('earned_date', '>=', $startMonth)
                      ->whereMonth('earned_date', '<=', $endMonth);
                }], 'points');
                break;
            case 'year':
                $query->withSum(['points as total_points' => function ($q) {
                    $q->whereYear('earned_date', now()->year);
                }], 'points');
                break;
            default: // total
                $query->withSum('points as total_points', 'points');
                break;
        }

        $rankings = $query->orderByDesc('total_points')
            ->limit($limit)
            ->get(['id', 'real_name', 'region', 'school', 'avatar'])
            ->map(function ($user, $index) {
                return [
                    'rank' => $index + 1,
                    'name' => $user->real_name,
                    'region' => $user->region,
                    'school' => $user->school,
                    'points' => $user->total_points ?? 0,
                    'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $rankings
        ]);
    }
}
