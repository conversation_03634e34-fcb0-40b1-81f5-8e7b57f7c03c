<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WechatMessageResource\Pages;
use App\Models\WechatAccount;
use App\Models\WechatMessage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WechatMessageResource extends Resource
{
    protected static ?string $model = WechatMessage::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = '微信消息';

    protected static ?string $modelLabel = '微信消息';

    protected static ?string $pluralModelLabel = '微信消息';

    protected static ?string $navigationGroup = '微信推文管理';

    protected static ?int $navigationSort = 22;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\Select::make('wechat_account_id')
                            ->label('微信账号')
                            ->options(WechatAccount::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable(),
                        
                        Forms\Components\TextInput::make('openid')
                            ->label('用户OpenID')
                            ->required()
                            ->maxLength(64),
                        
                        Forms\Components\TextInput::make('msg_id')
                            ->label('消息ID')
                            ->maxLength(64),
                        
                        Forms\Components\Select::make('msg_type')
                            ->label('消息类型')
                            ->options([
                                'text' => '文本消息',
                                'image' => '图片消息',
                                'voice' => '语音消息',
                                'video' => '视频消息',
                                'shortvideo' => '小视频消息',
                                'location' => '地理位置消息',
                                'link' => '链接消息',
                                'event' => '事件消息',
                            ])
                            ->required(),
                        
                        Forms\Components\Select::make('status')
                            ->label('处理状态')
                            ->options([
                                'pending' => '待处理',
                                'processed' => '已处理',
                                'failed' => '处理失败',
                            ])
                            ->default('pending'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('消息内容')
                    ->schema([
                        Forms\Components\Textarea::make('content')
                            ->label('消息内容')
                            ->rows(3),
                        
                        Forms\Components\TextInput::make('media_id')
                            ->label('媒体文件ID')
                            ->maxLength(128),
                        
                        Forms\Components\TextInput::make('pic_url')
                            ->label('图片链接')
                            ->url()
                            ->maxLength(512),
                        
                        Forms\Components\TextInput::make('title')
                            ->label('消息标题')
                            ->maxLength(256),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('消息描述')
                            ->rows(2),
                        
                        Forms\Components\TextInput::make('url')
                            ->label('消息链接')
                            ->url()
                            ->maxLength(512),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('事件信息')
                    ->schema([
                        Forms\Components\Select::make('event')
                            ->label('事件类型')
                            ->options([
                                'subscribe' => '关注事件',
                                'unsubscribe' => '取消关注事件',
                                'SCAN' => '扫描带参数二维码事件',
                                'LOCATION' => '上报地理位置事件',
                                'CLICK' => '自定义菜单事件',
                                'VIEW' => '点击菜单跳转链接时的事件',
                            ]),
                        
                        Forms\Components\TextInput::make('event_key')
                            ->label('事件KEY值')
                            ->maxLength(128),
                        
                        Forms\Components\TextInput::make('ticket')
                            ->label('二维码ticket')
                            ->maxLength(128),
                    ])
                    ->columns(3),
                
                Forms\Components\Section::make('地理位置信息')
                    ->schema([
                        Forms\Components\TextInput::make('location_x')
                            ->label('纬度')
                            ->numeric(),
                        
                        Forms\Components\TextInput::make('location_y')
                            ->label('经度')
                            ->numeric(),
                        
                        Forms\Components\TextInput::make('scale')
                            ->label('地图缩放大小')
                            ->numeric(),
                        
                        Forms\Components\TextInput::make('label')
                            ->label('地理位置信息')
                            ->maxLength(256),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('回复信息')
                    ->schema([
                        Forms\Components\Select::make('reply_type')
                            ->label('回复类型')
                            ->options([
                                'text' => '文本回复',
                                'image' => '图片回复',
                                'voice' => '语音回复',
                                'video' => '视频回复',
                                'music' => '音乐回复',
                                'news' => '图文回复',
                            ]),
                        
                        Forms\Components\Textarea::make('reply_content')
                            ->label('回复内容')
                            ->rows(3),
                        
                        Forms\Components\TextInput::make('reply_media_id')
                            ->label('回复媒体ID')
                            ->maxLength(128),
                        
                        Forms\Components\DateTimePicker::make('replied_at')
                            ->label('回复时间'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('wechatAccount.name')
                    ->label('微信账号')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('openid')
                    ->label('用户OpenID')
                    ->searchable()
                    ->limit(20)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 20 ? $state : null;
                    }),
                
                Tables\Columns\TextColumn::make('msg_type')
                    ->label('消息类型')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'text' => '文本消息',
                        'image' => '图片消息',
                        'voice' => '语音消息',
                        'video' => '视频消息',
                        'shortvideo' => '小视频消息',
                        'location' => '地理位置消息',
                        'link' => '链接消息',
                        'event' => '事件消息',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'text' => 'primary',
                        'image' => 'success',
                        'voice' => 'warning',
                        'video' => 'info',
                        'event' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('content')
                    ->label('消息内容')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                
                Tables\Columns\TextColumn::make('event')
                    ->label('事件类型')
                    ->formatStateUsing(fn (?string $state): string => match ($state) {
                        'subscribe' => '关注事件',
                        'unsubscribe' => '取消关注事件',
                        'SCAN' => '扫描二维码',
                        'LOCATION' => '上报位置',
                        'CLICK' => '菜单点击',
                        'VIEW' => '菜单跳转',
                        default => $state ?? '-',
                    })
                    ->placeholder('-'),
                
                Tables\Columns\TextColumn::make('status')
                    ->label('处理状态')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => '待处理',
                        'processed' => '已处理',
                        'failed' => '处理失败',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'processed' => 'success',
                        'failed' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('接收时间')
                    ->dateTime()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('replied_at')
                    ->label('回复时间')
                    ->dateTime()
                    ->placeholder('-')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('wechat_account_id')
                    ->label('微信账号')
                    ->options(WechatAccount::active()->pluck('name', 'id')),
                
                Tables\Filters\SelectFilter::make('msg_type')
                    ->label('消息类型')
                    ->options([
                        'text' => '文本消息',
                        'image' => '图片消息',
                        'voice' => '语音消息',
                        'video' => '视频消息',
                        'event' => '事件消息',
                    ]),
                
                Tables\Filters\SelectFilter::make('status')
                    ->label('处理状态')
                    ->options([
                        'pending' => '待处理',
                        'processed' => '已处理',
                        'failed' => '处理失败',
                    ]),
                
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('开始日期'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('结束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWechatMessages::route('/'),
            'create' => Pages\CreateWechatMessage::route('/create'),
            'view' => Pages\ViewWechatMessage::route('/{record}'),
            'edit' => Pages\EditWechatMessage::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}