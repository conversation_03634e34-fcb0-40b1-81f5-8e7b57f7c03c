<?php

namespace App\Filament\Resources\DatabaseBackupResource\Widgets;

use App\Models\DatabaseBackup;
use Filament\Widgets\Widget;

class BackupDetailsWidget extends Widget
{
    protected static string $view = 'filament.widgets.backup-details';

    public DatabaseBackup $record;

    protected function getViewData(): array
    {
        return [
            'backup' => $this->record,
        ];
    }
}
