<?php

namespace App\Handlers;

use Illuminate\Support\Facades\Log;

class ImageMessageHandler extends WechatEventHandler
{
    /**
     * 处理图片消息
     */
    public function handle(): ?string
    {
        $mediaId = $this->message['MediaId'] ?? '';
        $picUrl = $this->message['PicUrl'] ?? '';
        
        // 保存消息到数据库
        $message = $this->saveMessage('image', [
            'media_id' => $mediaId,
            'media_url' => $picUrl,
        ]);

        Log::info('收到图片消息', [
            'account_id' => $this->account->id,
            'openid' => $this->message['FromUserName'],
            'media_id' => $mediaId,
            'pic_url' => $picUrl,
            'message_id' => $message->id,
        ]);

        // 获取默认回复
        $reply = $this->getAutoReply('default', [
            'openid' => $this->message['FromUserName'],
            'message_type' => 'image',
        ]);

        if ($reply) {
            // 解析回复内容
            $replyData = json_decode($reply, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($replyData)) {
                $replyContent = $this->buildReplyFromData($replyData);
                $replyType = $replyData['type'] ?? 'text';
            } else {
                // 纯文本回复
                $replyContent = $this->buildTextReply($reply);
                $replyType = 'text';
            }

            // 保存回复记录
            $this->saveReply($message, $reply, $replyType);

            return $replyContent;
        }

        // 没有匹配的回复，标记为已处理但未回复
        $message->update(['status' => 'processed']);

        return null;
    }

    /**
     * 根据回复数据构建回复内容
     */
    private function buildReplyFromData(array $replyData): string
    {
        $type = $replyData['type'] ?? 'text';

        switch ($type) {
            case 'text':
                return $this->buildTextReply($replyData['content'] ?? '');

            case 'image':
                return $this->buildImageReply($replyData['media_id'] ?? '');

            case 'voice':
                return $this->buildVoiceReply($replyData['media_id'] ?? '');

            case 'video':
                return $this->buildVideoReply(
                    $replyData['media_id'] ?? '',
                    $replyData['title'] ?? '',
                    $replyData['description'] ?? ''
                );

            case 'music':
                return $this->buildMusicReply(
                    $replyData['title'] ?? '',
                    $replyData['description'] ?? '',
                    $replyData['music_url'] ?? '',
                    $replyData['hq_music_url'] ?? '',
                    $replyData['thumb_media_id'] ?? ''
                );

            case 'news':
                return $this->buildNewsReply($replyData['articles'] ?? []);

            default:
                return $this->buildTextReply($replyData['content'] ?? '感谢您发送的图片');
        }
    }
}