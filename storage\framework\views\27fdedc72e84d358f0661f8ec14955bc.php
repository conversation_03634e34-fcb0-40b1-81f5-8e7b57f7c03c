<?php
    $name = $getName() ?? config('filament-ckeditor-field.upload_url');
    $uploadUrl = $getUploadUrl();
    $placeholder = $getPlaceholder();
    $isConcealed = $isConcealed();
?>

<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <?php if (isset($component)) { $__componentOriginal505efd9768415fdb4543e8c564dad437 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal505efd9768415fdb4543e8c564dad437 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.input.wrapper','data' => ['valid' => $errors->count() === 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::input.wrapper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['valid' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errors->count() === 0)]); ?>
        <div wire:ignore>
            <!-- 加载自定义视频上传插件 -->
            <script src="<?php echo e(asset('js/ckeditor-video-upload.js')); ?>"></script>

            <script type="text/javascript">
                // 添加视频上传按钮到工具栏
                function addVideoUploadButton(editor) {
                    // 查找工具栏
                    const toolbar = document.querySelector('.ck-toolbar');
                    if (!toolbar) return;

                    // 创建视频上传按钮
                    const videoButton = document.createElement('button');
                    videoButton.className = 'ck ck-button ck-off';
                    videoButton.type = 'button';
                    videoButton.title = '上传视频';
                    videoButton.innerHTML = `
                        <svg class="ck ck-icon ck-button__icon" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H2zm0 2h16v10H2V5z"/>
                            <path d="M8 7l4 2.5L8 12V7z"/>
                        </svg>
                        <span class="ck ck-button__label">上传视频</span>
                    `;

                    // 添加点击事件
                    videoButton.addEventListener('click', () => {
                        createVideoUploadButton()(editor);
                    });

                    // 插入到工具栏中（在图片按钮后面）
                    const imageButton = toolbar.querySelector('[title*="图"]') || toolbar.querySelector('[title*="Image"]');
                    if (imageButton && imageButton.parentNode) {
                        imageButton.parentNode.insertBefore(videoButton, imageButton.nextSibling);
                    } else {
                        // 如果找不到图片按钮，就添加到工具栏末尾
                        toolbar.appendChild(videoButton);
                    }
                }

                // Initialize the instance and event listener flags if not already set
                if (!window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"]) {
                    window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"] = {
                        instance: null,
                        eventListenerAdded: false
                    };
                }

                function createCKEditor() {
                    // Destroy existing editor to prevent duplicates
                    if (window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"].instance) {
                        // destroyCKEditor();
                        return;
                    }

                    // Create new editor instance
                    ClassicEditor
                        .create(document.querySelector('#ckeditor-<?php echo e($name); ?>'), {
                            language: 'zh-cn',
                            plugins: [
                                AccessibilityHelp,
                                Alignment,
                                Autoformat,
                                AutoImage,
                                AutoLink,
                                Autosave,
                                BlockQuote,
                                Bold,
                                Code,
                                CodeBlock,
                                Essentials,
                                FindAndReplace,
                                FontBackgroundColor,
                                FontColor,
                                FontFamily,
                                FontSize,
                                GeneralHtmlSupport,
                                Heading,
                                Highlight,
                                HorizontalLine,
                                HtmlComment,
                                HtmlEmbed,
                                ImageBlock,
                                ImageCaption,
                                ImageInline,
                                ImageInsert,
                                ImageInsertViaUrl,
                                ImageResize,
                                ImageStyle,
                                ImageTextAlternative,
                                ImageToolbar,
                                ImageUpload,
                                Indent,
                                IndentBlock,
                                Italic,
                                Link,
                                LinkImage,
                                List,
                                ListProperties,
                                MediaEmbed,
                                PageBreak,
                                Paragraph,
                                PasteFromOffice,
                                RemoveFormat,
                                SelectAll,
                                ShowBlocks,
                                SimpleUploadAdapter,
                                SourceEditing,
                                SpecialCharacters,
                                SpecialCharactersArrows,
                                SpecialCharactersCurrency,
                                SpecialCharactersEssentials,
                                SpecialCharactersLatin,
                                SpecialCharactersMathematical,
                                SpecialCharactersText,
                                Strikethrough,
                                Style,
                                Subscript,
                                Superscript,
                                Table,
                                TableCaption,
                                TableCellProperties,
                                TableColumnResize,
                                TableProperties,
                                TableToolbar,
                                TextTransformation,
                                TodoList,
                                Underline,
                                Undo
                            ],
                            toolbar: {
                                items: [
                                    'undo',
                                    'redo',
                                    '|',
                                    'sourceEditing',
                                    'showBlocks',
                                    '|',
                                    'heading',
                                    'style',
                                    '|',
                                    'fontSize',
                                    'fontFamily',
                                    'fontColor',
                                    'fontBackgroundColor',
                                    '|',
                                    'bold',
                                    'italic',
                                    'underline',
                                    '|',
                                    'link',
                                    'insertImage',
                                    'mediaEmbed',
                                    'insertTable',
                                    'highlight',
                                    'blockQuote',
                                    'codeBlock',
                                    '|',
                                    'alignment',
                                    '|',
                                    'bulletedList',
                                    'numberedList',
                                    'todoList',
                                    'outdent',
                                    'indent'
                                ],
                                shouldNotGroupWhenFull: false
                            },
                            autosave: {
                                save( editor ) {
                                    Livewire.dispatch('contentUpdated', { content: editor.getData(), editor: 'ckeditor-<?php echo e($name); ?>' })
                                }
                            },
                            fontFamily: {
                                supportAllValues: true
                            },
                            fontSize: {
                                options: [10, 12, 14, 'default', 18, 20, 22],
                                supportAllValues: true
                            },
                            heading: {
                                options: [
                                    {
                                        model: 'paragraph',
                                        title: 'Paragraph',
                                        class: 'ck-heading_paragraph'
                                    },
                                    {
                                        model: 'heading1',
                                        view: 'h1',
                                        title: 'Heading 1',
                                        class: 'ck-heading_heading1'
                                    },
                                    {
                                        model: 'heading2',
                                        view: 'h2',
                                        title: 'Heading 2',
                                        class: 'ck-heading_heading2'
                                    },
                                    {
                                        model: 'heading3',
                                        view: 'h3',
                                        title: 'Heading 3',
                                        class: 'ck-heading_heading3'
                                    },
                                    {
                                        model: 'heading4',
                                        view: 'h4',
                                        title: 'Heading 4',
                                        class: 'ck-heading_heading4'
                                    },
                                    {
                                        model: 'heading5',
                                        view: 'h5',
                                        title: 'Heading 5',
                                        class: 'ck-heading_heading5'
                                    },
                                    {
                                        model: 'heading6',
                                        view: 'h6',
                                        title: 'Heading 6',
                                        class: 'ck-heading_heading6'
                                    }
                                ]
                            },
                            htmlSupport: {
                                allow: [
                                    {
                                        name: /^.*$/,
                                        styles: true,
                                        attributes: true,
                                        classes: true
                                    }
                                ]
                            },
                            image: {
                                toolbar: [
                                    'toggleImageCaption',
                                    'imageTextAlternative',
                                    '|',
                                    'imageStyle:inline',
                                    'imageStyle:wrapText',
                                    'imageStyle:breakText',
                                    '|',
                                    'resizeImage'
                                ]
                            },
                            link: {
                                addTargetToExternalLinks: true,
                                defaultProtocol: 'https://',
                                decorators: {
                                    toggleDownloadable: {
                                        mode: 'manual',
                                        label: 'Downloadable',
                                        attributes: {
                                            download: 'file'
                                        }
                                    }
                                }
                            },
                            list: {
                                properties: {
                                    styles: true,
                                    startIndex: true,
                                    reversed: true
                                }
                            },
                            menuBar: {
                                isVisible: true
                            },
                            placeholder: '<?php echo e($placeholder); ?>',
                            style: {
                                definitions: [
                                    {
                                        name: 'Article category',
                                        element: 'h3',
                                        classes: ['category']
                                    },
                                    {
                                        name: 'Title',
                                        element: 'h2',
                                        classes: ['document-title']
                                    },
                                    {
                                        name: 'Subtitle',
                                        element: 'h3',
                                        classes: ['document-subtitle']
                                    },
                                    {
                                        name: 'Info box',
                                        element: 'p',
                                        classes: ['info-box']
                                    },
                                    {
                                        name: 'Side quote',
                                        element: 'blockquote',
                                        classes: ['side-quote']
                                    },
                                    {
                                        name: 'Marker',
                                        element: 'span',
                                        classes: ['marker']
                                    },
                                    {
                                        name: 'Spoiler',
                                        element: 'span',
                                        classes: ['spoiler']
                                    },
                                    {
                                        name: 'Code (dark)',
                                        element: 'pre',
                                        classes: ['fancy-code', 'fancy-code-dark']
                                    },
                                    {
                                        name: 'Code (bright)',
                                        element: 'pre',
                                        classes: ['fancy-code', 'fancy-code-bright']
                                    }
                                ]
                            },
                            table: {
                                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
                            },
                            <!--[if BLOCK]><![endif]--><?php if(isset($uploadUrl)): ?>

                            simpleUpload: {
                                uploadUrl: '<?php echo e($uploadUrl); ?>',
                                withCredentials: true,
                                headers: {
                                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                                }
                            }

                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        })
                        .then(editor => {
                            window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"].instance = editor;

                            // Find the main ckeditor class and add some helpful class names to it
                            
                            
                            document.getElementsByClassName('ck-editor__main')[0].classList.add('prose', 'max-w-none', 'dark:prose-invert')

                            // 添加自定义视频上传按钮
                            addVideoUploadButton(editor);

                            // Listen to changes
                            editor.model.document.on('change:data', () => {
                                // Emit Livewire event
                                Livewire.dispatch('contentUpdated', { content: editor.getData(), editor: 'ckeditor-<?php echo e($name); ?>' })
                            });
                        })
                        .catch(err => {
                            console.error(err);
                        });
                }

                function destroyCKEditor() {
                    if (window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"].instance) {
                        window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"].instance.destroy()
                            .then(() => {
                                window.ckeditorInstances["ckeditor-<?php echo e($name); ?>"].instance = null;
                            })
                            .catch(err => {
                                console.error('Failed to destroy editor:', err);
                            });
                    }
                }
            </script>
            <div
                x-data="{
                    state: $wire.$entangle('<?php echo e($getStatePath()); ?>'),
                    init() {
                        // Remove existing event listeners to prevent duplicates
                        document.removeEventListener('livewire:navigated', createCKEditor);
                        document.removeEventListener('livewire:navigate', destroyCKEditor);

                        // Add event listeners if not already added
                        if (!window.ckeditorInstances['ckeditor-<?php echo e($name); ?>'].eventListenerAdded) {
                            // todo: Look into the { once: true } option if necessary
                            document.addEventListener('livewire:navigated', createCKEditor);
                            document.addEventListener('livewire:navigate', destroyCKEditor);
                            window.ckeditorInstances['ckeditor-<?php echo e($name); ?>'].eventListenerAdded = true;
                        }

                        Livewire.on('contentUpdated', (payload) => {
                            this.state = payload.content;
                        });
                    }
                }"
                x-load-js="[<?php echo \Illuminate\Support\Js::from(\Filament\Support\Facades\FilamentAsset::getScriptSrc('filament-ckeditor-field', package: 'kahusoftware/filament-ckeditor-field'))->toHtml() ?>]"
                x-load-css="[<?php echo \Illuminate\Support\Js::from(\Filament\Support\Facades\FilamentAsset::getStyleHref('filament-ckeditor-field', package: 'kahusoftware/filament-ckeditor-field'))->toHtml() ?>]"
            >
                <textarea
                    id="ckeditor-<?php echo e($name); ?>"
                    name="<?php echo e($name); ?>"
                    x-model="state"
                ></textarea>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal505efd9768415fdb4543e8c564dad437)): ?>
<?php $attributes = $__attributesOriginal505efd9768415fdb4543e8c564dad437; ?>
<?php unset($__attributesOriginal505efd9768415fdb4543e8c564dad437); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal505efd9768415fdb4543e8c564dad437)): ?>
<?php $component = $__componentOriginal505efd9768415fdb4543e8c564dad437; ?>
<?php unset($__componentOriginal505efd9768415fdb4543e8c564dad437); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\cms-admin\resources\views/vendor/filament-ckeditor-field/ckeditor.blade.php ENDPATH**/ ?>