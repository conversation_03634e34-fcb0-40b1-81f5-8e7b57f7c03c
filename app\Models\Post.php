<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Post extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'title',
        'short_title',
        'slug',
        'excerpt',
        'content',
        'category_id',
        'featured_image',
        'thumb_image',
        'meta_data',
        'is_published',
        'published_at',
        'sort_order',
        'custom_flags',
        'weight',
        'source',
        'author',
        'allow_comment',
        'view_permission',
        'title_color',
        'extra_options',
    ];

    protected $casts = [
        'meta_data' => 'array',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
        'sort_order' => 'integer',
        'custom_flags' => 'array',
        'extra_options' => 'array',
    ];

    // 自动生成 slug
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if (empty($post->slug)) {
                $post->slug = Str::slug($post->title);
            }
        });

        static::updating(function ($post) {
            if ($post->isDirty('title') && !$post->isDirty('slug')) {
                $post->slug = Str::slug($post->title);
            }
        });

        static::saving(function ($post) {
            if (empty($post->slug) && !empty($post->title)) {
                $post->slug = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', substr(pinyin_abbr($post->title), 0, 8)));
            }
        });
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // 添加标签关联
    public function tags()
    {
        return $this->belongsToMany(Tag::class)
            ->withTimestamps();
    }

    // 添加评论关联
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    // 获取已发布的文章
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now());
    }

    // 获取草稿文章
    public function scopeDraft($query)
    {
        return $query->where('is_published', false)
            ->orWhereNull('published_at');
    }

    // 按排序顺序获取文章
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('published_at', 'desc');
    }

    // 获取文章的评论数量
    public function getCommentCountAttribute(): int
    {
        return $this->comments()->approved()->count();
    }

    // 获取文章的待审核评论数量
    public function getPendingCommentCountAttribute(): int
    {
        return $this->comments()->pending()->count();
    }

    // 检查文章是否允许评论
    public function canComment(): bool
    {
        return $this->is_published && $this->published_at <= now();
    }

    public function subCategories()
    {
        return $this->belongsToMany(
            Category::class,
            'post_sub_category',
            'post_id',
            'category_id'
        );
    }
}

if (!function_exists('pinyin_abbr')) {
    function pinyin_abbr($string) {
        try {
            return \Overtrue\Pinyin\Pinyin::abbr($string);
        } catch (\Throwable $e) {
            return substr($string, 0, 8);
        }
    }
} 