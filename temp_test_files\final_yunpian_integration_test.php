<?php

/**
 * 最终集成测试：验证重新设计的云片网字段完整流程
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Activity;
use App\Models\ActivityDetail;
use Illuminate\Support\Facades\DB;

echo "=== 云片网字段重新设计 - 最终集成测试 ===\n\n";

try {
    // 1. 获取真实的活动数据
    echo "1. 获取真实活动数据进行测试...\n";
    
    $activities = Activity::with('activityDetail')
        ->whereHas('activityDetail')
        ->limit(3)
        ->get();
    
    if ($activities->isEmpty()) {
        echo "没有找到包含详情的活动数据\n";
        exit(1);
    }
    
    echo "找到 " . $activities->count() . " 个活动进行测试\n\n";
    
    // 2. 模拟RegistrationController的buildTemplateData方法
    echo "2. 模拟短信模板数据构建...\n";
    
    foreach ($activities as $activity) {
        $activityDetail = $activity->activityDetail;
        
        echo "--- 测试活动: {$activity->title} (ID: {$activity->id}) ---\n";
        
        // 模拟buildTemplateData方法的逻辑（重新设计后的版本）
        $templateData = [
            'topic' => $activityDetail->topic ?: ($activity->title ?: ($activityDetail->theme ?: '未设置主题')),
            'time' => $activityDetail->time ?: (
                $activityDetail->activity_time ? 
                date('Y年m月d日 H:i', strtotime($activityDetail->activity_time)) : 
                '未设置时间'
            ),
            'address' => $activityDetail->address ?: '未设置地址',
            'obj' => $activityDetail->obj ?: ($activityDetail->target_audience ?: ($activityDetail->target ?: '未设置对象'))
        ];
        
        echo "构建的模板数据：\n";
        foreach ($templateData as $key => $value) {
            echo "  #{$key}#: '{$value}'\n";
        }
        
        // 3. 生成短信内容
        $smsTemplate = $activityDetail->sms_template ?: 
            '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。';
        
        $smsContent = $smsTemplate;
        foreach ($templateData as $key => $value) {
            $smsContent = str_replace("#{$key}#", $value, $smsContent);
        }
        
        echo "\n生成的短信内容：\n";
        echo "  {$smsContent}\n";
        
        // 4. 验证结果
        $validation = [
            'has_empty_fields' => false,
            'has_unreplaced_placeholders' => false,
            'empty_fields' => [],
            'unreplaced_placeholders' => []
        ];
        
        // 检查空字段
        foreach ($templateData as $key => $value) {
            if (empty($value) || strpos($value, '未设置') !== false) {
                $validation['has_empty_fields'] = true;
                $validation['empty_fields'][] = $key;
            }
        }
        
        // 检查未替换的占位符
        if (preg_match_all('/#(\w+)#/', $smsContent, $matches)) {
            $validation['has_unreplaced_placeholders'] = true;
            $validation['unreplaced_placeholders'] = $matches[1];
        }
        
        echo "\n验证结果：\n";
        if (!$validation['has_empty_fields'] && !$validation['has_unreplaced_placeholders']) {
            echo "  ✅ 完美！所有字段都已正确填充，短信内容完整\n";
        } else {
            if ($validation['has_empty_fields']) {
                echo "  ❌ 存在空字段: " . implode(', ', $validation['empty_fields']) . "\n";
            }
            if ($validation['has_unreplaced_placeholders']) {
                echo "  ❌ 存在未替换的占位符: " . implode(', ', $validation['unreplaced_placeholders']) . "\n";
            }
        }
        
        echo "\n" . str_repeat('-', 60) . "\n\n";
    }
    
    // 5. 总体统计
    echo "3. 总体数据统计...\n";
    
    $totalActivities = ActivityDetail::count();
    $emptyFieldStats = [];
    
    $yunpianFields = ['topic', 'time', 'address', 'obj'];
    foreach ($yunpianFields as $field) {
        $emptyCount = ActivityDetail::whereNull($field)
            ->orWhere($field, '')
            ->count();
        $emptyFieldStats[$field] = $emptyCount;
        $percentage = $totalActivities > 0 ? round(($emptyCount / $totalActivities) * 100, 2) : 0;
        echo "- {$field}字段为空: {$emptyCount}/{$totalActivities} ({$percentage}%)\n";
    }
    
    $totalEmptyFields = array_sum($emptyFieldStats);
    $totalPossibleFields = $totalActivities * count($yunpianFields);
    $completionRate = $totalPossibleFields > 0 ? 
        round((($totalPossibleFields - $totalEmptyFields) / $totalPossibleFields) * 100, 2) : 0;
    
    echo "\n数据完整性统计：\n";
    echo "- 总活动数: {$totalActivities}\n";
    echo "- 空字段总数: {$totalEmptyFields}/{$totalPossibleFields}\n";
    echo "- 数据完整率: {$completionRate}%\n";
    
    // 6. 最终结论
    echo "\n=== 最终测试结论 ===\n";
    
    if ($totalEmptyFields == 0) {
        echo "🎉 重新设计成功！\n";
        echo "✅ 所有云片网字段 (topic, time, address, obj) 都已正确填充\n";
        echo "✅ 字段名称与云片网模板参数直接对应，简化了映射逻辑\n";
        echo "✅ 短信模板不会再出现空字段问题\n";
        echo "✅ 数据迁移完成，现有数据已正确转换到新字段结构\n";
    } else {
        echo "⚠️ 部分字段仍为空，完整率: {$completionRate}%\n";
        echo "建议检查数据迁移脚本或手动补充缺失数据\n";
    }
    
    echo "\n重新设计的优势：\n";
    echo "1. 字段名称直接对应云片网参数，无需复杂映射逻辑\n";
    echo "2. 减少了字段映射错误的可能性\n";
    echo "3. 代码更简洁，维护更容易\n";
    echo "4. 新增活动时可直接填写对应字段，避免空值问题\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 集成测试完成 ===\n";