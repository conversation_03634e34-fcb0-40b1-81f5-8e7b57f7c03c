<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use App\Models\ActivityDetail;
use App\Models\Activity;
use Carbon\Carbon;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试报名分析活动标题获取修复 ===\n\n";

// 测试活动详情查询（模拟RegistrationAnalytics页面的逻辑）
echo "1. 测试活动选择下拉框数据...\n";
$activityOptions = ActivityDetail::with('activity')->get()->mapWithKeys(function($item) {
    // 格式化活动时间
    $time = $item->activity_time ? (new Carbon($item->activity_time))->format('Y-m-d') : '';
    // 优先使用activity.title，其次使用theme
    $title = $item->activity && $item->activity->title ? $item->activity->title : $item->theme;
    return [
        $item->id => $title . ($time ? ('（' . $time . '）') : '')
    ];
});

echo "活动选择选项数量: " . $activityOptions->count() . "\n";
foreach ($activityOptions->take(5) as $id => $label) {
    echo "  - ID {$id}: {$label}\n";
}

echo "\n2. 测试活动报名统计数据...\n";
$activityData = ActivityDetail::query()
    ->with('activity') // 加载activity关系以获取标题
    ->withCount(['registrations' => function($q) {
        // 只统计有效报名
        $q->where('status', true);
    }])
    ->orderBy('registrations_count', 'desc')
    ->limit(5)
    ->get();

echo "活动报名统计数据数量: " . $activityData->count() . "\n";
foreach ($activityData as $activity) {
    $title = $activity->activity && $activity->activity->title ? $activity->activity->title : $activity->theme;
    echo "  - {$title}: {$activity->registrations_count} 人报名\n";
    echo "    → ActivityDetail ID: {$activity->id}\n";
    echo "    → Activity关系: " . ($activity->activity ? '已加载' : '未加载') . "\n";
    if ($activity->activity) {
        echo "    → Activity标题: {$activity->activity->title}\n";
    }
    echo "    → Theme: {$activity->theme}\n";
    echo "\n";
}

echo "3. 测试图表标签数据生成...\n";
$chartLabels = $activityData->map(function($item) {
    return $item->activity && $item->activity->title ? $item->activity->title : $item->theme;
});

echo "图表标签: " . json_encode($chartLabels->toArray(), JSON_UNESCAPED_UNICODE) . "\n";

echo "\n4. 检查Activity和ActivityDetail关系...\n";
$activityDetailWithActivity = ActivityDetail::with('activity')->whereHas('activity')->count();
$activityDetailTotal = ActivityDetail::count();
echo "有关联Activity的ActivityDetail: {$activityDetailWithActivity}/{$activityDetailTotal}\n";

if ($activityDetailWithActivity < $activityDetailTotal) {
    echo "\n⚠️  发现有ActivityDetail记录没有关联的Activity:\n";
    $orphanDetails = ActivityDetail::with('activity')->whereDoesntHave('activity')->take(3)->get();
    foreach ($orphanDetails as $detail) {
        echo "  - ID {$detail->id}: {$detail->theme} (activity_id: {$detail->activity_id})\n";
    }
}

echo "\n=== 测试完成 ===\n";
echo "修复说明:\n";
echo "1. 在RegistrationAnalytics.php中添加了->with('activity')来预加载关系\n";
echo "2. 在活动选择和图表显示中优先使用activity.title，fallback到theme\n";
echo "3. 修复了registration-analytics.blade.php中的图表标签显示\n";