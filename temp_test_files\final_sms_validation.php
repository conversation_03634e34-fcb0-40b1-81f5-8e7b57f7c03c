<?php

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Models\Activity;

echo "=== 最终SMS验证测试 ===\n\n";

// 获取现有的Registration记录
$registration = Registration::with(['activityDetail.activity'])->first();

if (!$registration) {
    echo "没有找到Registration记录\n";
    exit(1);
}

echo "找到Registration记录，ID: {$registration->id}\n";
echo "用户姓名: {$registration->name}\n";
echo "手机号: {$registration->phone}\n\n";

$activityDetail = $registration->activityDetail;
$activity = $activityDetail->activity;

echo "ActivityDetail信息:\n";
echo "- ID: {$activityDetail->id}\n";
echo "- theme: {$activityDetail->theme}\n";
echo "- topic: {$activityDetail->topic}\n";
echo "- activity_time: {$activityDetail->activity_time}\n";
echo "- address: {$activityDetail->address}\n";
echo "- target: {$activityDetail->target}\n";
echo "- target_audience: {$activityDetail->target_audience}\n\n";

// 模拟buildTemplateData逻辑
function buildTemplateData($registration, $activityDetail, $activity) {
    $templateData = [
        'name' => $registration->name,
        'topic' => $activityDetail->topic ?: ($activity->title ?: '未设置主题'),
        'time' => $activityDetail->activity_time ? $activityDetail->activity_time->format('Y年m月d日 H:i') : '未设置时间',
        'address' => $activityDetail->address ?: '未设置地址',
        'obj' => $activityDetail->target_audience ?: ($activityDetail->target ?: '未设置对象'),
    ];
    
    return $templateData;
}

$templateData = buildTemplateData($registration, $activityDetail, $activity);

echo "构建的模板数据:\n";
foreach ($templateData as $key => $value) {
    echo "- {$key}: {$value}\n";
}

// 模拟SMS模板内容
$smsTemplate = '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。';

// 替换模板占位符
$smsContent = $smsTemplate;
foreach ($templateData as $key => $value) {
    $smsContent = str_replace("#{$key}#", $value, $smsContent);
}

echo "\n最终SMS内容:\n";
echo $smsContent . "\n\n";

// 检查是否还有空字段
$hasEmptyFields = false;
foreach ($templateData as $key => $value) {
    if (empty($value) || $value === '未设置主题' || $value === '未设置时间' || $value === '未设置地址' || $value === '未设置对象') {
        echo "⚠️ 字段 '{$key}' 为空或未设置: {$value}\n";
        $hasEmptyFields = true;
    }
}

if (!$hasEmptyFields) {
    echo "✅ 所有字段都已正确填充\n";
    echo "✅ SMS模板修复成功\n";
} else {
    echo "❌ 仍有字段未正确填充\n";
}

echo "\n=== 验证完成 ===\n";