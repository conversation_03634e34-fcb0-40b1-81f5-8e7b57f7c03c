<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use App\Models\Setting;
use App\Services\DingTalkService;

class BackupSettings extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = '数据备份';
    protected static ?string $navigationLabel = '备份设置';
    protected static ?int $navigationSort = 2;
    protected static ?int $navigationGroupSort = 5;
    protected static string $view = 'filament.pages.backup-settings';

    public array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'backup_retention_days' => Setting::getValue('backup_retention_days', 30),
            'backup_max_files' => Setting::getValue('backup_max_files', 10),
            'backup_storage_path' => Setting::getValue('backup_storage_path', 'private/backups'),
            'backup_notification_enabled' => Setting::getValue('backup_notification_enabled', true),
            'dingtalk_webhook' => Setting::getValue('dingtalk_webhook', ''),
            'backup_compression_enabled' => Setting::getValue('backup_compression_enabled', false),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('手动备份')
                    ->schema([
                        \Filament\Forms\Components\Placeholder::make('manual_backup_info')
                            ->label('')
                            ->content('🚀 **立即创建备份**

如果您需要立即创建一个备份，可以使用下面的快速备份功能：'),

                        \Filament\Forms\Components\Actions::make([
                            \Filament\Forms\Components\Actions\Action::make('create_manual_backup')
                                ->label('创建手动备份')
                                ->icon('heroicon-o-plus-circle')
                                ->color('primary')
                                ->form([
                                    \Filament\Forms\Components\TextInput::make('name')
                                        ->label('备份名称')
                                        ->required()
                                        ->default(fn () => 'manual_backup_' . now()->format('Y-m-d_H-i-s')),
                                    \Filament\Forms\Components\Select::make('backup_type')
                                        ->label('备份类型')
                                        ->options(\App\Models\DatabaseBackup::getBackupTypes())
                                        ->required()
                                        ->default(\App\Models\DatabaseBackup::TYPE_FULL),
                                    \Filament\Forms\Components\Textarea::make('description')
                                        ->label('备份描述')
                                        ->rows(3)
                                        ->placeholder('请输入备份描述信息'),
                                ])
                                ->action(function (array $data) {
                                    $backupService = app(\App\Services\DatabaseBackupService::class);

                                    try {
                                        $backup = $backupService->createBackup([
                                            'name' => $data['name'],
                                            'backup_type' => $data['backup_type'],
                                            'description' => $data['description'] ?? '',
                                            'created_by' => auth()->id(),
                                        ]);

                                        \Filament\Notifications\Notification::make()
                                            ->title('备份任务已创建')
                                            ->body("备份 '{$backup->name}' 正在后台执行")
                                            ->success()
                                            ->send();
                                    } catch (\Exception $e) {
                                        \Filament\Notifications\Notification::make()
                                            ->title('创建备份失败')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                })
                                ->modalHeading('创建手动备份')
                                ->modalSubmitActionLabel('开始备份'),
                        ]),

                        \Filament\Forms\Components\Placeholder::make('schedule_info')
                            ->label('')
                            ->content('⏰ **自动备份计划**

如需设置定时自动备份，请前往 **备份计划** 页面：
- 支持每小时、每天、每周、每月等多种频率
- 可设置具体的执行时间
- 支持启用/禁用状态管理
- 每个计划可独立配置备份类型'),
                    ])->collapsible(),

                Section::make('备份保留策略')
                    ->schema([
                        TextInput::make('backup_retention_days')
                            ->label('保留天数')
                            ->numeric()
                            ->default(30)
                            ->helperText('超过指定天数的备份将被自动删除'),
                        TextInput::make('backup_max_files')
                            ->label('最大备份文件数')
                            ->numeric()
                            ->default(10)
                            ->helperText('超过此数量的旧备份文件将被删除'),
                        TextInput::make('backup_storage_path')
                            ->label('存储路径')
                            ->default('backups')
                            ->helperText('备份文件存储的相对路径'),
                    ])->columns(3),

                Section::make('钉钉通知设置')
                    ->schema([
                        Toggle::make('backup_notification_enabled')
                            ->label('启用钉钉通知')
                            ->helperText('备份完成或失败时发送钉钉消息'),
                        TextInput::make('dingtalk_webhook')
                            ->label('钉钉机器人Webhook')
                            ->url()
                            ->placeholder('https://oapi.dingtalk.com/robot/send?access_token=xxxxxx')
                            ->helperText('钉钉群机器人的Webhook地址')
                            ->visible(fn ($get) => $get('backup_notification_enabled'))
                            ->rules([
                                fn (): \Closure => function (string $attribute, $value, \Closure $fail) {
                                    if ($value && !DingTalkService::validateWebhook($value)) {
                                        $fail('请输入有效的钉钉机器人Webhook地址');
                                    }
                                },
                            ]),
                        \Filament\Forms\Components\Placeholder::make('dingtalk_help')
                            ->label('配置说明')
                            ->content(DingTalkService::getWebhookInstructions())
                            ->visible(fn ($get) => $get('backup_notification_enabled')),
                    ])->columns(1),

                Section::make('高级设置')
                    ->schema([
                        Toggle::make('backup_compression_enabled')
                            ->label('启用压缩')
                            ->helperText('压缩备份文件以节省存储空间（需要系统支持）'),
                        Textarea::make('backup_exclude_tables')
                            ->label('排除表')
                            ->placeholder('table1,table2,table3')
                            ->helperText('备份时排除的表名，用逗号分隔')
                            ->rows(3),
                    ])->columns(1)
                    ->collapsible(),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        foreach ($this->data as $key => $value) {
            Setting::setValue($key, $value);
        }

        Notification::make()
            ->title('备份设置已保存')
            ->success()
            ->send();
    }

    public function testDingTalkNotification(): void
    {
        try {
            $dingTalkService = app(DingTalkService::class);
            $result = $dingTalkService->testConnection();

            if ($result['success']) {
                Notification::make()
                    ->title('测试成功')
                    ->body($result['message'])
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('测试失败')
                    ->body($result['message'])
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('测试异常')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }



    public function getTitle(): string
    {
        return '备份设置';
    }
}
