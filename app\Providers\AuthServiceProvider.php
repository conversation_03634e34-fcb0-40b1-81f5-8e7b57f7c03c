<?php

namespace App\Providers;

use App\Models\Post;
use App\Models\Category;
use App\Models\Article;
use App\Models\Comment;
use App\Models\FriendshipLink;
use App\Models\FriendshipLinkCategory;
use App\Policies\PostPolicy;
use App\Policies\CategoryPolicy;
use App\Policies\ArticlePolicy;
use App\Policies\CommentPolicy;
use App\Policies\FriendshipLinkPolicy;
use App\Policies\FriendshipLinkCategoryPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Post::class => PostPolicy::class,
        Category::class => CategoryPolicy::class,
        Article::class => ArticlePolicy::class,
        Comment::class => CommentPolicy::class,
        FriendshipLink::class => FriendshipLinkPolicy::class,
        FriendshipLinkCategory::class => FriendshipLinkCategoryPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}
