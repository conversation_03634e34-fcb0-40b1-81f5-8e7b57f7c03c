<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== 检查registrations表结构 ===\n";

try {
    // 获取表结构
    $columns = Schema::getColumnListing('registrations');
    
    echo "registrations表的字段:\n";
    foreach ($columns as $column) {
        echo "- {$column}\n";
    }
    
    // 查看实际数据
    echo "\n=== 查看registrations数据样例 ===\n";
    $registrations = DB::table('registrations')->limit(3)->get();
    
    foreach ($registrations as $index => $registration) {
        echo "\n记录 " . ($index + 1) . ":\n";
        foreach ($registration as $key => $value) {
            echo "  {$key}: '" . ($value ?? 'NULL') . "'\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
