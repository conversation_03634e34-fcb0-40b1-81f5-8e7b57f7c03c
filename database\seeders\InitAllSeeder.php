<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;

class InitAllSeeder extends Seeder
{
    public function run(): void
    {
        // 1. 角色定义
        $roles = [
            'super-admin' => '超级管理员',
            'admin' => '管理员',
            'system-auditor' => '系统审计员',
            'auditor' => '审计员',
            'user' => '普通用户',
        ];

        // 2. 权限定义（可根据实际需求扩展）
        $permissions = [
            'system.view', 'system.manage', 'system.audit',
            'users.view', 'users.create', 'users.edit', 'users.delete',
            'roles.view', 'roles.create', 'roles.edit', 'roles.delete',
            'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
        ];
        foreach ($permissions as $name) {
            Permission::firstOrCreate(['name' => $name, 'guard_name' => 'web']);
        }

        // 3. 创建角色并分配权限
        $rolePermissions = [
            'super-admin' => $permissions,
            'admin' => [
                'system.view', 'system.manage',
                'users.view', 'users.create', 'users.edit', 'users.delete',
                'roles.view', 'roles.create', 'roles.edit', 'roles.delete',
                'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
            ],
            'system-auditor' => ['system.view', 'system.audit', 'users.view', 'roles.view', 'categories.view'],
            'auditor' => ['system.view', 'users.view', 'roles.view', 'categories.view'],
            'user' => ['categories.view'],
        ];
        foreach ($roles as $role => $label) {
            $r = Role::firstOrCreate(['name' => $role, 'guard_name' => 'web']);
            $r->syncPermissions($rolePermissions[$role]);
        }

        // 4. 创建用户并分配角色
        $users = [
            [
                'name' => '超级管理员',
                'email' => '<EMAIL>',
                'password' => Hash::make('superadmin123'),
                'is_active' => true,
                'role' => 'super-admin',
            ],
            [
                'name' => '管理员',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'is_active' => true,
                'role' => 'admin',
            ],
            [
                'name' => '系统审计员',
                'email' => '<EMAIL>',
                'password' => Hash::make('auditor123'),
                'is_active' => true,
                'role' => 'system-auditor',
            ],
            [
                'name' => '审计员',
                'email' => '<EMAIL>',
                'password' => Hash::make('auditor123'),
                'is_active' => true,
                'role' => 'auditor',
            ],
            [
                'name' => '普通用户',
                'email' => '<EMAIL>',
                'password' => Hash::make('user123'),
                'is_active' => true,
                'role' => 'user',
            ],
        ];
        foreach ($users as $u) {
            $user = User::firstOrCreate([
                'email' => $u['email'],
            ], [
                'name' => $u['name'],
                'password' => $u['password'],
                'is_active' => $u['is_active'],
                'email_verified_at' => now(),
            ]);
            $user->assignRole($u['role']);
        }

        // 5. 批量插入栏目
        if (\Schema::hasTable('categories')) {
            \DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            \DB::table('categories')->truncate();
            \DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            // 定义多级栏目结构
            $categoriesTree = [
                [
                    'name' => '心理特色', 'slug' => 'psychology', 'children' => [
                        ['name' => '区心理特色校', 'slug' => 'district-psychology'],
                        ['name' => '市心理教育示范校', 'slug' => 'city-psychology-demo'],
                    ]
                ],
                [
                    'name' => '课程资源', 'slug' => 'course-resources', 'children' => [
                        [
                            'name' => '红色教育', 'slug' => 'red-education', 'children' => [
                                ['name' => '党史故事', 'slug' => 'party-history'],
                                ['name' => '开国将帅', 'slug' => 'founding-generals'],
                                ['name' => '伟人故里', 'slug' => 'great-hometowns'],
                                ['name' => '革命圣地', 'slug' => 'revolutionary-sites'],
                                ['name' => '时代楷模', 'slug' => 'role-models'],
                            ]
                        ],
                        [
                            'name' => '优秀传统文化教育', 'slug' => 'traditional-culture', 'children' => [
                                ['name' => '诸子百家', 'slug' => 'philosophers'],
                                ['name' => '漫画课堂', 'slug' => 'comic-class'],
                                ['name' => '美育课堂', 'slug' => 'aesthetic-class'],
                            ]
                        ],
                        ['name' => '科学教育', 'slug' => 'science-education'],
                        ['name' => '生态文明教育', 'slug' => 'eco-civilization'],
                    ]
                ],
                [
                    'name' => '精品课程', 'slug' => 'premium-courses', 'children' => [
                        [
                            'name' => '我晒嘉幸福', 'slug' => 'wo-shai-jia-xing-fu', 'children' => [
                                ['name' => '视频课程', 'slug' => 'video'],
                                ['name' => '图文精华', 'slug' => 'article'],
                                ['name' => '特色展示', 'slug' => 'feature'],
                                ['name' => '资讯动态', 'slug' => 'news'],
                            ]
                        ],
                        [
                            'name' => '嘉师有约', 'slug' => 'jia-shi-you-yue', 'children' => [
                                ['name' => '视频课程', 'slug' => 'video'],
                                ['name' => '图文精华', 'slug' => 'article'],
                                ['name' => '特色展示', 'slug' => 'feature'],
                                ['name' => '资讯动态', 'slug' => 'news'],
                            ]
                        ],
                        [
                            'name' => '嘉师悦谈', 'slug' => 'jia-shi-yue-tan', 'children' => [
                                ['name' => '视频课程', 'slug' => 'video'],
                                ['name' => '图文精华', 'slug' => 'article'],
                                ['name' => '特色展示', 'slug' => 'feature'],
                                ['name' => '资讯动态', 'slug' => 'news'],
                            ]
                        ],
                        [
                            'name' => '点亮心灯', 'slug' => 'dian-liang-xin-deng', 'children' => [
                                ['name' => '视频课程', 'slug' => 'video'],
                                ['name' => '图文精华', 'slug' => 'article'],
                                ['name' => '特色展示', 'slug' => 'feature'],
                                ['name' => '资讯动态', 'slug' => 'news'],
                            ]
                        ],
                    ]
                ],
            ];

            // 递归插入多级栏目
            $insertCategory = function($cat, $parentId = null, $parentSlug = null) use (&$insertCategory) {
                $slug = $parentSlug ? ($parentSlug . '-' . $cat['slug']) : $cat['slug'];
                $data = [
                    'name' => $cat['name'],
                    'slug' => $slug,
                    'parent_id' => $parentId,
                    '_lft' => 0,
                    '_rgt' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                $id = \DB::table('categories')->insertGetId($data);
                if (!empty($cat['children'])) {
                    foreach ($cat['children'] as $child) {
                        $insertCategory($child, $id, $slug);
                    }
                }
            };
            foreach ($categoriesTree as $cat) {
                $insertCategory($cat);
            }
        }
    }
} 