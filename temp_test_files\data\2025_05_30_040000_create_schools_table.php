<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schools', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('学校名称');
            
            // 地区信息 - 复用行政机构的地区字段
            $table->string('province_code', 10)->nullable()->comment('省份代码');
            $table->string('province_name', 100)->nullable()->comment('省份名称');
            $table->string('city_code', 10)->nullable()->comment('城市代码');
            $table->string('city_name', 100)->nullable()->comment('城市名称');
            $table->string('district_code', 10)->nullable()->comment('区县代码');
            $table->string('district_name', 100)->nullable()->comment('区县名称');
            $table->string('town_code', 10)->nullable()->comment('乡镇代码');
            $table->string('town_name', 100)->nullable()->comment('乡镇名称');
            $table->string('full_region_name')->nullable()->comment('完整地区名称');
            
            // 学校特有字段
            $table->enum('school_type', ['kindergarten', 'primary_secondary'])
                  ->comment('学校类型：kindergarten=幼儿园, primary_secondary=中小学');
            
            $table->enum('grade_type', [
                'kindergarten',
                'primary_nine_year', 
                'junior_middle',
                'complete_middle',
                'senior_middle',
                'twelve_year',
                'all_stages'
            ])->comment('年级类型：kindergarten=幼儿园, primary_nine_year=小学九年一贯制, junior_middle=初级中学, complete_middle=完全中学, senior_middle=高级中学, twelve_year=十二年一贯制, all_stages=全学段');
            
            $table->enum('education_system', ['five_four', 'six_three'])
                  ->comment('学制：five_four=五四制, six_three=六三制');
            
            $table->enum('school_nature', ['public', 'private'])
                  ->comment('学校性质：public=公办, private=民办');
            
            $table->enum('binding_status', ['bound', 'unbound'])
                  ->default('unbound')
                  ->comment('组织绑定状态：bound=已绑定, unbound=未绑定');
            
            // 关联行政机构
            $table->foreignId('administrative_institution_id')
                  ->nullable()
                  ->constrained('administrative_institutions')
                  ->onDelete('set null')
                  ->comment('关联的行政机构ID');
            
            // 联系信息
            $table->string('contact_person', 100)->nullable()->comment('联系人');
            $table->string('contact_phone', 20)->nullable()->comment('联系电话');
            $table->string('contact_email', 100)->nullable()->comment('联系邮箱');
            $table->text('address')->nullable()->comment('详细地址');
            $table->text('description')->nullable()->comment('备注说明');
            
            // 系统字段
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['province_code', 'city_code', 'district_code', 'town_code'], 'idx_schools_region');
            $table->index(['school_type', 'grade_type'], 'idx_schools_type_grade');
            $table->index(['binding_status', 'is_active'], 'idx_schools_status');
            $table->index('name', 'idx_schools_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schools');
    }
};