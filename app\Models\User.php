<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;

class User extends Authenticatable implements FilamentUser
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'is_active',
        'email_verified_at',
        'phone',
        'real_name',
        'region',
        'school',
        'is_verified',
        'organization_id',
        'position',
        'employee_id',
        'join_date',
        'employment_status',
    ];

    /**
     * 虚拟属性，用于积分中心显示
     */
    protected $appends = [
        'rank',
        'user_count',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'is_verified' => 'boolean',
            'join_date' => 'date',
        ];
    }

    /**
     * 获取 Filament 后台头像 URL
     */
    public function getFilamentAvatarUrl(): ?string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name ?? 'User') . '&background=random';
    }

    /**
     * 检查用户是否可以访问 Filament 后台
     */
    public function canAccessPanel(\Filament\Panel $panel): bool
    {
        // 只允许激活且有 admin 或 super_admin 角色的用户访问后台
        return $this->is_active && ($this->hasRole('admin') || $this->hasRole('super_admin'));
    }

    /**
     * 用户积分记录关联
     */
    public function points()
    {
        return $this->hasMany(\App\Models\UserPoint::class);
    }

    /**
     * 用户行为记录关联
     */
    public function behaviors()
    {
        return $this->hasMany(\App\Models\UserBehavior::class);
    }

    /**
     * 所属组织关联
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * 获取在职状态选项
     */
    public static function getEmploymentStatusOptions(): array
    {
        return [
            'active' => '在职',
            'inactive' => '待岗',
            'resigned' => '离职',
            'suspended' => '停职',
        ];
    }

    /**
     * 获取在职状态标签
     */
    public function getEmploymentStatusLabel(): string
    {
        return self::getEmploymentStatusOptions()[$this->employment_status] ?? $this->employment_status;
    }

    /**
     * 检查是否在职
     */
    public function isActive(): bool
    {
        return $this->employment_status === 'active' && $this->is_active;
    }

    /**
     * 获取用户总积分
     */
    public function getTotalPointsAttribute()
    {
        // 如果是积分中心的聚合数据，直接返回设置的值
        if (isset($this->attributes['total_points'])) {
            return $this->attributes['total_points'];
        }
        return $this->points()->sum('points');
    }

    /**
     * 获取用户月积分
     */
    public function getMonthPointsAttribute()
    {
        // 如果是积分中心的聚合数据，直接返回设置的值
        if (isset($this->attributes['month_points'])) {
            return $this->attributes['month_points'];
        }
        return $this->points()
            ->whereYear('earned_date', now()->year)
            ->whereMonth('earned_date', now()->month)
            ->sum('points');
    }

    /**
     * 获取用户季度积分
     */
    public function getQuarterPointsAttribute()
    {
        // 如果是积分中心的聚合数据，直接返回设置的值
        if (isset($this->attributes['quarter_points'])) {
            return $this->attributes['quarter_points'];
        }
        $quarter = ceil(now()->month / 3);
        $startMonth = ($quarter - 1) * 3 + 1;
        $endMonth = $quarter * 3;

        return $this->points()
            ->whereYear('earned_date', now()->year)
            ->whereMonth('earned_date', '>=', $startMonth)
            ->whereMonth('earned_date', '<=', $endMonth)
            ->sum('points');
    }

    /**
     * 获取用户年积分
     */
    public function getYearPointsAttribute()
    {
        // 如果是积分中心的聚合数据，直接返回设置的值
        if (isset($this->attributes['year_points'])) {
            return $this->attributes['year_points'];
        }
        return $this->points()
            ->whereYear('earned_date', now()->year)
            ->sum('points');
    }

    /**
     * 检查是否为实名用户
     */
    public function isVerified()
    {
        return $this->is_verified && !empty($this->real_name) && !empty($this->region);
    }

    /**
     * 获取排名（虚拟属性）
     */
    public function getRankAttribute()
    {
        return $this->attributes['rank'] ?? null;
    }

    /**
     * 获取用户数量（虚拟属性）
     */
    public function getUserCountAttribute()
    {
        return $this->attributes['user_count'] ?? null;
    }
}
