<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SmsTemplateResource\Pages;
use App\Filament\Resources\SmsTemplateResource\RelationManagers;
use App\Models\SmsTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SmsTemplateResource extends Resource
{
    protected static ?string $model = SmsTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '系统管理';
    protected static ?string $navigationLabel = '短信模板';
    protected static ?int $navigationSort = 5;
    protected static ?int $navigationGroupSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('模板名称')
                            ->required()
                            ->maxLength(255)
                            ->helperText('短信模板的显示名称'),
                        Forms\Components\TextInput::make('code')
                            ->label('模板代码')
                            ->required()
                            ->maxLength(255)
                            ->helperText('模板的唯一标识代码'),
                        Forms\Components\TextInput::make('yunpian_template_id')
                            ->label('云片模板ID')
                            ->required()
                            ->maxLength(255)
                            ->helperText('云片网平台的模板ID'),
                        Forms\Components\Select::make('template_category')
                            ->label('模板分类')
                            ->options([
                                'notification' => '通知类',
                                'verification' => '验证类',
                                'marketing' => '营销类',
                                'other' => '其他',
                            ])
                            ->placeholder('选择模板分类'),
                        Forms\Components\Textarea::make('description')
                            ->label('描述')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('模板的详细描述'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('模板参数')
                    ->schema([
                        Forms\Components\Textarea::make('template_params')
                            ->label('模板参数（JSON格式）')
                            ->rows(4)
                            ->helperText('JSON格式的模板参数，如：["name", "topic", "time", "address"]')
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('param_description')
                            ->label('参数说明')
                            ->rows(4)
                            ->helperText('详细说明每个参数的含义和用法')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('模板名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('code')
                    ->label('模板代码')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('yunpian_template_id')
                    ->label('云片模板ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('template_category')
                    ->label('分类')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'notification' => 'success',
                        'verification' => 'warning',
                        'marketing' => 'info',
                        'other' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'notification' => '通知类',
                        'verification' => '验证类',
                        'marketing' => '营销类',
                        'other' => '其他',
                        default => '未分类',
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label('描述')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSmsTemplates::route('/'),
            'create' => Pages\CreateSmsTemplate::route('/create'),
            'view' => Pages\ViewSmsTemplate::route('/{record}'),
            'edit' => Pages\EditSmsTemplate::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return '短信模板';
    }

    public static function getPluralModelLabel(): string
    {
        return '短信模板';
    }

    public static function getNavigationLabel(): string
    {
        return '短信模板';
    }
}
