<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AdministrativeInstitution;
use Illuminate\Support\Facades\Cache;

class UpdateFullRegionName extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:update-full-region-name';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update full_region_name field for all administrative institutions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始更新行政机构的完整地区名称...');
        
        $totalCount = AdministrativeInstitution::count();
        $this->info("总共需要更新 {$totalCount} 条记录");
        
        $bar = $this->output->createProgressBar($totalCount);
        $bar->start();
        
        $updatedCount = 0;
        
        AdministrativeInstitution::chunk(1000, function ($institutions) use ($bar, &$updatedCount) {
            foreach ($institutions as $institution) {
                // 构建完整地区名称
                $parts = array_filter([
                    $institution->province_name,
                    $institution->city_name,
                    $institution->district_name,
                    $institution->town_name
                ]);
                
                $fullRegionName = implode('', $parts);
                
                // 只有当 full_region_name 为空或与计算出的值不同时才更新
                if ($institution->full_region_name !== $fullRegionName) {
                    $institution->update(['full_region_name' => $fullRegionName]);
                    $updatedCount++;
                }
                
                $bar->advance();
            }
        });
        
        $bar->finish();
        $this->newLine();
        
        $this->info("更新完成！共更新了 {$updatedCount} 条记录");
        
        // 清除相关缓存
        $this->info('清除相关缓存...');
        Cache::forget('admin_institutions_province_count');
        Cache::forget('admin_institutions_city_count');
        Cache::forget('admin_institutions_district_count');
        Cache::forget('admin_institutions_town_count');
        Cache::forget('admin_institutions_bound_count');
        Cache::forget('admin_institutions_unbound_count');
        Cache::forget('province_options');
        Cache::forget('city_options');
        
        $this->info('缓存清除完成！');
        
        return Command::SUCCESS;
    }
}