<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 更新RegistrationController的buildTemplateData方法 ===\n\n";

$controllerPath = 'app/Http/Controllers/RegistrationController.php';

if (!file_exists($controllerPath)) {
    echo "❌ 找不到RegistrationController.php文件\n";
    exit(1);
}

// 读取当前文件内容
$content = file_get_contents($controllerPath);

// 查找buildTemplateData方法
if (strpos($content, 'buildTemplateData') === false) {
    echo "❌ 找不到buildTemplateData方法\n";
    exit(1);
}

// 新的buildTemplateData方法实现
$newBuildTemplateDataMethod = '
    /**
     * 构建短信模板数据
     * @param $registration
     * @return array
     */
    private function buildTemplateData($registration)
    {
        $activityDetail = $registration->activityDetail;
        $activity = $activityDetail->activity;
        
        // 获取用户姓名，确保不为空
        $name = !empty($registration->name) ? $registration->name : "用户";
        
        // 获取活动标题，优先使用activity.title，其次使用activity_detail.theme
        $topic = "";
        if (!empty($activity->title)) {
            $topic = $activity->title;
        } elseif (!empty($activityDetail->theme)) {
            $topic = $activityDetail->theme;
        } else {
            $topic = "活动通知";
        }
        
        // 获取活动时间，确保格式正确
        $time = "";
        if (!empty($activityDetail->activity_time)) {
            try {
                $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
            } catch (Exception $e) {
                $time = "待定时间";
            }
        } else {
            $time = "待定时间";
        }
        
        // 获取活动地点
        $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
        
        // 获取目标对象
        $obj = !empty($activityDetail->target) ? $activityDetail->target : "全体人员";
        
        $templateData = [
            "name" => $name,
            "topic" => $topic,
            "time" => $time,
            "address" => $address,
            "obj" => $obj
        ];
        
        // 记录日志以便调试
        \Log::info("构建短信模板数据", [
            "registration_id" => $registration->id,
            "activity_detail_id" => $activityDetail->id,
            "activity_id" => $activity->id,
            "template_data" => $templateData
        ]);
        
        return $templateData;
    }';

// 使用正则表达式替换buildTemplateData方法
$pattern = '/private\s+function\s+buildTemplateData\s*\([^}]*\}(?:\s*\})?/s';

if (preg_match($pattern, $content)) {
    $newContent = preg_replace($pattern, trim($newBuildTemplateDataMethod), $content);
    
    // 备份原文件
    $backupPath = $controllerPath . '.backup.' . date('YmdHis');
    copy($controllerPath, $backupPath);
    echo "✅ 已备份原文件到: {$backupPath}\n";
    
    // 写入新内容
    file_put_contents($controllerPath, $newContent);
    echo "✅ 已更新RegistrationController.php中的buildTemplateData方法\n";
    
    echo "\n更新内容包括:\n";
    echo "- 添加了所有参数的空值检查\n";
    echo "- 为每个参数设置了合理的默认值\n";
    echo "- 改进了时间格式化处理\n";
    echo "- 添加了调试日志记录\n";
    echo "- 确保topic优先使用activity.title，其次使用activity_detail.theme\n";
    
} else {
    echo "❌ 无法找到buildTemplateData方法的完整定义\n";
    echo "请手动更新该方法\n";
}

echo "\n=== 更新完成 ===\n";