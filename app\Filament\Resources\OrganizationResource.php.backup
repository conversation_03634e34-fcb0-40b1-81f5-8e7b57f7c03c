<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrganizationResource\Pages;
use App\Models\Organization;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use CodeWithDennis\FilamentSelectTree\SelectTree;

class OrganizationResource extends Resource
{
    protected static ?string $model = Organization::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = '组织架构管理';

    protected static ?string $navigationLabel = '组织架构';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('组织名称')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('code')
                            ->label('组织代码')
                            ->required()
                            ->maxLength(100)
                            ->unique(ignoreRecord: true),
                        SelectTree::make('parent_id')
                            ->label('上级组织')
                            ->relationship('parent', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('选择上级组织（留空为根组织）'),
                        Forms\Components\Select::make('type')
                            ->label('组织类型')
                            ->options([
                                'department' => '部门',
                                'division' => '科室',
                                'team' => '小组',
                                'company' => '公司',
                                'branch' => '分支机构',
                            ])
                            ->required()
                            ->default('department'),
                        Forms\Components\Textarea::make('description')
                            ->label('组织描述')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('联系信息')
                    ->schema([
                        Forms\Components\TextInput::make('contact_person')
                            ->label('联系人')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('contact_phone')
                            ->label('联系电话')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('contact_email')
                            ->label('联系邮箱')
                            ->email()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('address')
                            ->label('办公地址')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('其他设置')
                    ->schema([
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('组织名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('code')
                    ->label('组织代码')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('上级组织')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->label('组织类型')
                    ->colors([
                        'primary' => 'department',
                        'success' => 'division',
                        'warning' => 'team',
                        'danger' => 'company',
                        'secondary' => 'branch',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'department' => '部门',
                        'division' => '科室',
                        'team' => '小组',
                        'company' => '公司',
                        'branch' => '分支机构',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('contact_person')
                    ->label('联系人')
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact_phone')
                    ->label('联系电话')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('组织类型')
                    ->options([
                        'department' => '部门',
                        'division' => '科室',
                        'team' => '小组',
                        'company' => '公司',
                        'branch' => '分支机构',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('启用')
                    ->falseLabel('禁用')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizations::route('/'),
            'create' => Pages\CreateOrganization::route('/create'),
            'view' => Pages\ViewOrganization::route('/{record}'),
            'edit' => Pages\EditOrganization::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return '组织架构';
    }

    public static function getPluralModelLabel(): string
    {
        return '组织架构';
    }
}