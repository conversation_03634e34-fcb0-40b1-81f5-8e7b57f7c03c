<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== 优化数据库字段映射以匹配云片网字段 ===\n\n";

// 1. 检查当前字段
echo "1. 检查当前字段结构:\n";
if (Schema::hasColumn('activity_details', 'theme')) {
    echo "✓ activity_details.theme 字段存在\n";
} else {
    echo "✗ activity_details.theme 字段不存在\n";
}

if (Schema::hasColumn('activity_details', 'target')) {
    echo "✓ activity_details.target 字段存在\n";
} else {
    echo "✗ activity_details.target 字段不存在\n";
}

// 2. 创建优化的字段映射
echo "\n\n2. 执行字段优化:\n";

try {
    DB::beginTransaction();
    
    // 添加新的更直观的字段
    if (!Schema::hasColumn('activity_details', 'topic')) {
        Schema::table('activity_details', function ($table) {
            $table->string('topic')->nullable()->comment('活动主题 - 对应云片网#topic#参数');
        });
        echo "✓ 添加 activity_details.topic 字段\n";
    }
    
    if (!Schema::hasColumn('activity_details', 'target_audience')) {
        Schema::table('activity_details', function ($table) {
            $table->string('target_audience')->nullable()->comment('目标受众 - 对应云片网#obj#参数');
        });
        echo "✓ 添加 activity_details.target_audience 字段\n";
    }
    
    // 迁移现有数据
    echo "\n3. 迁移现有数据:\n";
    
    // 将theme数据复制到topic
    $themeCount = DB::table('activity_details')
        ->whereNotNull('theme')
        ->where('theme', '!=', '')
        ->update(['topic' => DB::raw('theme')]);
    echo "✓ 迁移了 {$themeCount} 条theme数据到topic字段\n";
    
    // 将target数据复制到target_audience  
    $targetCount = DB::table('activity_details')
        ->whereNotNull('target')
        ->where('target', '!=', '')
        ->update(['target_audience' => DB::raw('target')]);
    echo "✓ 迁移了 {$targetCount} 条target数据到target_audience字段\n";
    
    DB::commit();
    echo "\n✓ 数据库字段优化完成\n";
    
} catch (Exception $e) {
    DB::rollback();
    echo "\n✗ 数据库操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 优化完成 ===\n";
