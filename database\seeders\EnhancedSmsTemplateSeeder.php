<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SmsTemplate;

class EnhancedSmsTemplateSeeder extends Seeder
{
    public function run()
    {
        $templates = [
            [
                'name' => '报名确认通知',
                'code' => 'registration_confirm',
                'yunpian_template_id' => '2875692',
                'description' => '用户报名成功后的确认短信',
                'template_params' => json_encode([
                    'topic' => '活动主题',
                    'time' => '活动时间', 
                    'address' => '活动地址',
                    'obj' => '活动对象'
                ]),
                'param_description' => '参数说明：#topic#=活动主题，#time#=活动时间，#address#=活动地址，#obj#=活动对象',
                'template_category' => 'registration'
            ],
            [
                'name' => '验证码短信',
                'code' => 'verification_code',
                'yunpian_template_id' => '2875693',
                'description' => '发送验证码',
                'template_params' => json_encode([
                    'code' => '验证码',
                    'time' => '有效时间'
                ]),
                'param_description' => '参数说明：#code#=验证码，#time#=有效时间',
                'template_category' => 'verification'
            ],
            [
                'name' => '活动提醒',
                'code' => 'activity_reminder',
                'yunpian_template_id' => '2875694',
                'description' => '活动开始前提醒',
                'template_params' => json_encode([
                    'topic' => '活动主题',
                    'time' => '活动时间',
                    'address' => '活动地址'
                ]),
                'param_description' => '参数说明：#topic#=活动主题，#time#=活动时间，#address#=活动地址',
                'template_category' => 'reminder'
            ],
            [
                'name' => '活动取消通知',
                'code' => 'activity_cancel',
                'yunpian_template_id' => '2875695',
                'description' => '活动取消通知',
                'template_params' => json_encode([
                    'topic' => '活动主题',
                    'time' => '原定时间',
                    'reason' => '取消原因'
                ]),
                'param_description' => '参数说明：#topic#=活动主题，#time#=原定时间，#reason#=取消原因',
                'template_category' => 'notification'
            ],
            [
                'name' => '密码重置',
                'code' => 'password_reset',
                'yunpian_template_id' => '2875696',
                'description' => '密码重置验证码',
                'template_params' => json_encode([
                    'code' => '验证码',
                    'time' => '有效时间'
                ]),
                'param_description' => '参数说明：#code#=验证码，#time#=有效时间',
                'template_category' => 'security'
            ]
        ];

        foreach ($templates as $template) {
            SmsTemplate::updateOrCreate(
                ['code' => $template['code']],
                $template
            );
        }
    }
}