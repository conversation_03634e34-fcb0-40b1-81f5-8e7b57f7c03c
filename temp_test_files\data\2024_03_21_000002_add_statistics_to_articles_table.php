<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->unsignedInteger('view_count')->default(0)->comment('阅读量');
            $table->unsignedInteger('comment_count')->default(0)->comment('评论数');
            $table->unsignedInteger('like_count')->default(0)->comment('点赞数');
            $table->decimal('hot_score', 10, 2)->default(0)->comment('热度分数');
            $table->timestamp('last_viewed_at')->nullable()->comment('最后查看时间');
            $table->timestamp('last_commented_at')->nullable()->comment('最后评论时间');
            
            // 添加索引
            $table->index('view_count');
            $table->index('hot_score');
            $table->index('last_viewed_at');
        });
    }

    public function down(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->dropColumn([
                'view_count',
                'comment_count',
                'like_count',
                'hot_score',
                'last_viewed_at',
                'last_commented_at',
            ]);
            $table->dropIndex(['view_count']);
            $table->dropIndex(['hot_score']);
            $table->dropIndex(['last_viewed_at']);
        });
    }
}; 