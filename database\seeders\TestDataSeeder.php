<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Tag;
use App\Models\Article;
use App\Models\Comment;
use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use Carbon\Carbon;

class TestDataSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('开始创建测试数据...');

        // 创建管理员用户（如果不存在）
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'password' => bcrypt('password'),
                'real_name' => '系统管理员',
                'region' => '北京市',
                'school' => '测试学校',
                'is_verified' => true,
                'is_active' => true,
            ]
        );
        $this->command->info('✓ 创建/更新管理员用户');

        // 创建普通用户
        $users = User::factory(15)->create([
            'is_verified' => true,
        ]);
        $this->command->info('✓ 创建 15 个普通用户');

        // 创建分类
        $categories = Category::factory(8)->create();
        $this->command->info('✓ 创建 8 个分类');

        // 创建标签
        $tags = Tag::factory(15)->create();
        $this->command->info('✓ 创建 15 个标签');

        // 创建文章
        $articles = Article::factory(30)->create();
        $this->command->info('✓ 创建 30 篇文章');

        // 为每篇文章添加标签
        $articles->each(function ($article) use ($tags) {
            $article->tags()->attach(
                $tags->random(rand(1, 4))->pluck('id')->toArray()
            );
        });
        $this->command->info('✓ 为文章添加标签关联');

        // 为每篇文章添加评论
        $articles->each(function ($article) {
            Comment::factory(rand(2, 8))->create([
                'article_id' => $article->id,
            ]);
        });
        $this->command->info('✓ 为文章添加评论');

        // 创建活动并测试报名系统
        $this->createRegistrationTestData();

        $this->command->info('🎉 测试数据创建完成！');
        $this->printStatistics();
    }

    /**
     * 创建报名系统测试数据
     */
    private function createRegistrationTestData()
    {
        $this->command->info('开始创建报名系统测试数据...');

        // 创建不同状态的活动
        $activities = [
            [
                'title' => '2024年春季教学研讨会',
                'status' => 'published',
                'publish_time' => now()->subDays(10),
                'qrcode_url' => 'https://example.com/qr/spring-seminar',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example1',
                'views_count' => 156,
            ],
            [
                'title' => '学生科技创新大赛',
                'status' => 'published',
                'publish_time' => now()->subDays(5),
                'qrcode_url' => 'https://example.com/qr/tech-competition',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example2',
                'views_count' => 89,
            ],
            [
                'title' => '家长开放日活动',
                'status' => 'published',
                'publish_time' => now()->subDays(3),
                'qrcode_url' => 'https://example.com/qr/parent-day',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example3',
                'views_count' => 234,
            ],
            [
                'title' => '暑期夏令营报名',
                'status' => 'published',
                'publish_time' => now()->subDays(1),
                'qrcode_url' => 'https://example.com/qr/summer-camp',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example4',
                'views_count' => 45,
            ],
            [
                'title' => '教师培训班',
                'status' => 'published',
                'publish_time' => now()->subDays(7),
                'qrcode_url' => 'https://example.com/qr/teacher-training',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example5',
                'views_count' => 123,
            ],
        ];

        foreach ($activities as $activityData) {
            $activity = Activity::create($activityData);
            $this->createActivityDetailsWithRegistrations($activity);
        }

        $this->command->info('✓ 创建了 5 个活动及其详情和报名记录');
    }

    /**
     * 为活动创建详情和报名记录
     */
    private function createActivityDetailsWithRegistrations($activity)
    {
        // 为每个活动创建1-3个场次
        $detailsCount = rand(1, 3);

        for ($i = 1; $i <= $detailsCount; $i++) {
            $quota = rand(20, 100);
            $currentCount = rand(5, min($quota, 50)); // 确保报名人数不超过名额

            $detail = ActivityDetail::create([
                'activity_id' => $activity->id,
                'theme' => $activity->title . " - 第{$i}场",
                'deadline' => Carbon::now()->addDays(rand(15, 45)),
                'registration_deadline' => Carbon::now()->addDays(rand(10, 40)),
                'status' => 1,
                'quota' => $quota,
                'target' => $this->getRandomTarget(),
                'target_audience' => $this->getRandomAudience(),
                'current_count' => $currentCount,
                'activity_time' => Carbon::now()->addDays(rand(20, 60)),
                'fee' => $this->getRandomFee(),
                'reminder' => '请准时参加，携带相关材料。如有疑问请联系主办方。',
                'registration_method' => $this->getRandomRegistrationMethod(),
                'address' => $this->getRandomAddress(),
                'sms_template' => '您好{name}，您已成功报名参加{activity}，时间：{time}，地点：{location}。请准时参加！',
            ]);

            // 创建报名记录，确保数量与current_count一致
            for ($j = 1; $j <= $currentCount; $j++) {
                Registration::create([
                    'activity_detail_id' => $detail->id,
                    'name' => $this->getRandomName(),
                    'phone' => $this->getRandomPhone(),
                    'organization' => $this->getRandomOrganization(),
                    'grade' => $this->getRandomGrade(),
                    'gender' => rand(0, 1) ? 'male' : 'female',
                    'source' => $this->getRandomSource(),
                    'status' => rand(0, 10) > 1, // 90%的报名有效
                    'created_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }

    /**
     * 打印统计信息
     */
    private function printStatistics()
    {
        $this->command->info('');
        $this->command->info('📊 数据统计：');
        $this->command->info('用户总数：' . User::count());
        $this->command->info('文章总数：' . Article::count());
        $this->command->info('分类总数：' . Category::count());
        $this->command->info('标签总数：' . Tag::count());
        $this->command->info('评论总数：' . Comment::count());
        $this->command->info('活动总数：' . Activity::count());
        $this->command->info('活动场次总数：' . ActivityDetail::count());
        $this->command->info('报名记录总数：' . Registration::count());
        $this->command->info('有效报名总数：' . Registration::where('status', true)->count());

        // 详细的报名统计
        $this->command->info('');
        $this->command->info('📋 报名详细统计：');
        ActivityDetail::with(['activity', 'registrations'])->get()->each(function ($detail) {
            $validRegistrations = $detail->registrations->where('status', true)->count();
            $this->command->info("- {$detail->activity->title} - {$detail->theme}：{$validRegistrations}/{$detail->quota} 人");
        });
    }

    // 辅助方法
    private function getRandomTarget()
    {
        return collect(['全体教师', '骨干教师', '新入职教师', '学科带头人', '班主任'])->random();
    }

    private function getRandomAudience()
    {
        return collect(['小学教师', '中学教师', '高中教师', '幼儿园教师', '大学教师'])->random();
    }

    private function getRandomFee()
    {
        return collect(['免费', '50元', '100元', '150元', '200元'])->random();
    }

    private function getRandomRegistrationMethod()
    {
        return collect(['在线报名', '电话报名', '现场报名', '微信报名'])->random();
    }

    private function getRandomAddress()
    {
        return collect([
            '学校多功能厅', '学校会议室', '教学楼201', '图书馆报告厅',
            '体育馆', '实验楼会议室', '行政楼会议室'
        ])->random();
    }

    private function getRandomName()
    {
        $surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴'];
        $names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛'];
        return $surnames[array_rand($surnames)] . $names[array_rand($names)] . $names[array_rand($names)];
    }

    private function getRandomPhone()
    {
        return '1' . rand(3, 9) . rand(0, 9) . str_pad(rand(0, 99999999), 8, '0', STR_PAD_LEFT);
    }

    private function getRandomOrganization()
    {
        return collect([
            '第一小学', '第二中学', '实验小学', '育才中学', '希望小学',
            '明德中学', '阳光小学', '文华中学', '启明小学', '博雅中学'
        ])->random();
    }

    private function getRandomGrade()
    {
        return collect(['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '高一', '高二', '高三'])->random();
    }

    private function getRandomSource()
    {
        return collect(['微信', '网站', '电话', '现场', '朋友推荐', '学校通知'])->random();
    }
}