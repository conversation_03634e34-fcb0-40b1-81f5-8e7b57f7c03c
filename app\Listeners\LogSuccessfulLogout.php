<?php

namespace App\Listeners;

use App\Models\AuditLog;
use Illuminate\Auth\Events\Logout;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Request;

class LogSuccessfulLogout
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Logout $event): void
    {
        try {
            AuditLog::create([
                'user_id' => $event->user ? $event->user->id : null,
                'operated_at' => now(),
                'operation_type' => 'LOGOUT',
                'module' => '系统登出',
                'target_id' => $event->user ? (string)$event->user->id : null,
                'request_ip' => Request::ip(),
                'request_url' => Request::fullUrl(),
                'request_data' => [
                    'user_agent' => Request::userAgent(),
                    'logout_time' => now()->toDateTimeString(),
                ],
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => '用户成功登出系统',
            ]);
        } catch (\Exception $e) {
            \Log::error('记录登出日志失败: ' . $e->getMessage());
        }
    }
}
