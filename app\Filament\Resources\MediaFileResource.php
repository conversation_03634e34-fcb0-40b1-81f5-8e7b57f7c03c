<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MediaFileResource\Pages;
use App\Models\MediaFile;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Storage;
use CodeWithDennis\FilamentSelectTree\SelectTree;

class MediaFileResource extends Resource
{
    protected static ?string $model = MediaFile::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?int $navigationSort = 3;

    protected static ?int $navigationGroupSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('文件信息')
                    ->schema([
                        Forms\Components\FileUpload::make('path')
                            ->label('文件')
                            ->required()
                            ->disk('public')
                            ->directory('media')
                            ->preserveFilenames()
                            ->acceptedFileTypes([
                                'image/*',
                                'video/*',
                                'audio/*',
                                'application/pdf',
                                'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            ])
                            ->maxSize(102400)
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $set('name', pathinfo($state, PATHINFO_FILENAME));
                                    $set('file_name', basename($state));
                                    $disk = \Illuminate\Support\Facades\Storage::disk('public');
                                    if ($disk->exists($state)) {
                                        $set('size', $disk->size($state));
                                        $set('mime_type', $disk->mimeType($state) ?: 'application/octet-stream');
                                    } else {
                                        $set('size', 0);
                                        $set('mime_type', 'application/octet-stream');
                                    }
                                }
                            }),
                        Forms\Components\TextInput::make('name')
                            ->label('名称')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Hidden::make('file_name'),
                        Forms\Components\Hidden::make('mime_type'),
                        Forms\Components\Hidden::make('size')->default(0),
                        Forms\Components\TextInput::make('alt')
                            ->label('替代文本')
                            ->maxLength(255),
                        SelectTree::make('category_id')
                            ->label('分类')
                            ->relationship('category', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('请选择栏目')
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label('描述')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\TagsInput::make('tags')
                            ->label('标签')
                            ->columnSpanFull(),
                        Forms\Components\Toggle::make('is_public')
                            ->label('是否公开')
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('path')
                    ->label('预览')
                    ->disk('public')
                    ->circular()
                    ->defaultImageUrl(url('/images/placeholder.png'))
                    ->visible(fn (?MediaFile $record): bool => $record?->isImage() ?? false),
                Tables\Columns\TextColumn::make('name')
                    ->label('名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('file_name')
                    ->label('文件名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('mime_type')
                    ->label('类型')
                    ->sortable(),
                Tables\Columns\TextColumn::make('size')
                    ->label('大小')
                    ->formatStateUsing(fn (int $state): string => number_format($state / 1024, 2) . ' KB')
                    ->sortable(),
                Tables\Columns\TextColumn::make('category.name')
                    ->label('分类')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_public')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('download_count')
                    ->label('下载次数')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('分类')
                    ->relationship('category', 'name'),
                Tables\Filters\TernaryFilter::make('is_public')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('公开')
                    ->falseLabel('私有')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\Action::make('download')
                    ->label('下载')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn (MediaFile $record): string => $record->url)
                    ->openUrlInNewTab()
                    ->action(fn (MediaFile $record) => $record->incrementDownloadCount()),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMediaFiles::route('/'),
            'create' => Pages\CreateMediaFile::route('/create'),
            'edit' => Pages\EditMediaFile::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '流媒体管理';
    }

    public static function beforeSave($record, $data)
    {
        $disk = \Illuminate\Support\Facades\Storage::disk('public');
        if (empty($data['size']) && !empty($data['path']) && $disk->exists($data['path'])) {
            $record->size = $disk->size($data['path']);
        } elseif (empty($data['size'])) {
            $record->size = 0;
        }
        if (empty($data['mime_type']) && !empty($data['path']) && $disk->exists($data['path'])) {
            $record->mime_type = $disk->mimeType($data['path']) ?: 'application/octet-stream';
        } elseif (empty($data['mime_type'])) {
            $record->mime_type = 'application/octet-stream';
        }
        if (empty($data['file_name']) && !empty($data['path'])) {
            $record->file_name = basename($data['path']);
        }
    }

    public static function getModelLabel(): string
    {
        return '流媒体文件';
    }

    public static function getPluralModelLabel(): string
    {
        return '流媒体文件';
    }
} 