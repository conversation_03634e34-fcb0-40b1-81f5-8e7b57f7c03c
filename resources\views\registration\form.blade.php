<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $activityDetail->theme }} - 报名</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
</head>
<body class="antialiased">
    <div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
        <div class="relative py-3 sm:max-w-xl sm:mx-auto">
            <div class="relative px-4 py-10 bg-white mx-8 md:mx-0 shadow rounded-3xl sm:p-10">
                <div class="max-w-md mx-auto">
                    <div class="divide-y divide-gray-200">
                        <div class="py-8 text-base leading-6 space-y-4 text-gray-700 sm:text-lg sm:leading-7">
                            <h2 class="text-2xl font-bold mb-8 text-center">{{ $activityDetail->theme }}</h2>
                            
                            <div class="mb-4">
                                <p class="font-semibold">活动时间：</p>
                                <p>{{ $activityDetail->start_time->format('Y-m-d H:i') }} 至 {{ $activityDetail->end_time->format('Y-m-d H:i') }}</p>
                            </div>
                            
                            <div class="mb-4">
                                <p class="font-semibold">活动地点：</p>
                                <p>{{ $activityDetail->address }}</p>
                            </div>
                            
                            <div class="mb-4">
                                <p class="font-semibold">报名截止：</p>
                                <p>{{ $activityDetail->registration_deadline->format('Y-m-d H:i') }}</p>
                            </div>
                            
                            <div class="mb-4">
                                <p class="font-semibold">报名人数：</p>
                                <p>{{ $activityDetail->current_count }}/{{ $activityDetail->quota }}</p>
                            </div>

                            @if($activityDetail->current_count >= $activityDetail->quota)
                                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                    <strong class="font-bold">报名已满！</strong>
                                    <span class="block sm:inline">该活动报名人数已达到上限。</span>
                                </div>
                            @elseif($activityDetail->registration_deadline->isPast())
                                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                    <strong class="font-bold">报名已截止！</strong>
                                    <span class="block sm:inline">该活动报名时间已结束。</span>
                                </div>
                            @else
                                <form action="{{ route('registration.register', ['id' => $activityDetail->id]) }}" method="POST" class="mt-8">
                                    @csrf
                                    <input type="hidden" name="openid" id="openid">
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="name">
                                            姓名
                                        </label>
                                        <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('name') border-red-500 @enderror"
                                               id="name" type="text" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="phone">
                                            手机号
                                        </label>
                                        <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('phone') border-red-500 @enderror"
                                               id="phone" type="tel" name="phone" value="{{ old('phone') }}" required>
                                        @error('phone')
                                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="organization">
                                            单位
                                        </label>
                                        <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('organization') border-red-500 @enderror"
                                               id="organization" type="text" name="organization" value="{{ old('organization') }}">
                                        @error('organization')
                                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="grade">
                                            年级
                                        </label>
                                        <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('grade') border-red-500 @enderror"
                                               id="grade" type="text" name="grade" value="{{ old('grade') }}">
                                        @error('grade')
                                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">
                                            性别
                                        </label>
                                        <div class="mt-2">
                                            <label class="inline-flex items-center">
                                                <input type="radio" class="form-radio" name="gender" value="male" {{ old('gender') == 'male' ? 'checked' : '' }}>
                                                <span class="ml-2">男</span>
                                            </label>
                                            <label class="inline-flex items-center ml-6">
                                                <input type="radio" class="form-radio" name="gender" value="female" {{ old('gender') == 'female' ? 'checked' : '' }}>
                                                <span class="ml-2">女</span>
                                            </label>
                                        </div>
                                        @error('gender')
                                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="source">
                                            来源
                                        </label>
                                        <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('source') border-red-500 @enderror"
                                               id="source" type="text" name="source" value="{{ old('source') }}" required>
                                        @error('source')
                                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="flex items-center justify-between mt-8">
                                        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" type="submit">
                                            提交报名
                                        </button>
                                    </div>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取微信用户 openid
        wx.config({
            debug: false,
            appId: '{{ config("wechat.official_account.default.app_id") }}',
            timestamp: '{{ $timestamp }}',
            nonceStr: '{{ $nonceStr }}',
            signature: '{{ $signature }}',
            jsApiList: ['getLocation']
        });

        wx.ready(function() {
            // 从 URL 参数中获取 openid
            const urlParams = new URLSearchParams(window.location.search);
            const openid = urlParams.get('openid');
            if (openid) {
                document.getElementById('openid').value = openid;
            }
        });
    </script>
</body>
</html>