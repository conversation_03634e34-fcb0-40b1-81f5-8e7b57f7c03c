<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 分析短信模板参数映射问题 ===\n\n";

// 获取现场咨询模板
$template = DB::table('sms_templates')
    ->where('code', 'consultation_notice')
    ->first();

if (!$template) {
    echo "❌ 未找到现场咨询模板\n";
    exit;
}

echo "现场咨询模板信息:\n";
echo "- 模板ID: {$template->id}\n";
echo "- 云片模板ID: {$template->yunpian_template_id}\n";
echo "- 模板描述: {$template->description}\n";
echo "\n";

// 解析模板参数
echo "模板参数解析:\n";
$paramsJson = $template->template_params;
echo "- 原始参数: {$paramsJson}\n";

$params = json_decode($paramsJson, true);
if (is_array($params)) {
    echo "- 解析后的参数:\n";
    foreach ($params as $key => $value) {
        echo "  * {$key}: {$value}\n";
    }
} else {
    echo "❌ 参数解析失败，原始数据: {$paramsJson}\n";
    // 尝试手动解析
    $params = null;
}

echo "\n";

// 分析buildTemplateData方法的参数映射
echo "buildTemplateData方法参数映射分析:\n";
echo "当前映射关系:\n";
echo "- name → registration->name (姓名)\n";
echo "- topic → activityDetail->activity->title (活动标题)\n";
echo "- time → activityDetail->activity_time (活动时间)\n";
echo "- address → activityDetail->address (活动地点)\n";
echo "- obj → activityDetail->target (目标对象)\n";
echo "\n";

if ($params) {
    echo "云片网模板期望的参数:\n";
    foreach ($params as $key => $description) {
        echo "- {$key} → {$description}\n";
    }
    echo "\n";
    
    // 检查参数是否匹配
    $expectedParams = array_keys($params);
    $actualParams = ['name', 'topic', 'time', 'address', 'obj'];
    
    echo "参数匹配检查:\n";
    $missingParams = array_diff($expectedParams, $actualParams);
    $extraParams = array_diff($actualParams, $expectedParams);
    
    if (empty($missingParams) && empty($extraParams)) {
        echo "✅ 参数完全匹配\n";
    } else {
        if (!empty($missingParams)) {
            echo "❌ 缺少参数: " . implode(', ', $missingParams) . "\n";
        }
        if (!empty($extraParams)) {
            echo "❌ 多余参数: " . implode(', ', $extraParams) . "\n";
        }
    }
}

echo "\n";

// 检查实际数据
echo "检查实际数据问题:\n";

// 查找使用此模板的活动详情
$activityDetails = DB::table('activity_details')
    ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
    ->where('activity_details.sms_template_id', $template->id)
    ->select(
        'activity_details.id',
        'activity_details.theme',
        'activity_details.activity_time',
        'activity_details.address',
        'activity_details.target',
        'activities.title as activity_title'
    )
    ->get();

echo "使用现场咨询模板的活动详情 (" . count($activityDetails) . "个):\n";
foreach ($activityDetails as $detail) {
    echo "\n活动详情ID: {$detail->id}\n";
    
    // 检查每个参数的值
    $paramValues = [
        'topic' => $detail->activity_title,
        'time' => $detail->activity_time,
        'address' => $detail->address,
        'obj' => $detail->target
    ];
    
    $hasEmptyValues = false;
    foreach ($paramValues as $param => $value) {
        $status = empty($value) ? '❌ 空值' : '✅ 有值';
        if (empty($value)) $hasEmptyValues = true;
        echo "  {$param}: '{$value}' {$status}\n";
    }
    
    if ($hasEmptyValues) {
        echo "  ⚠️ 此活动详情存在空值参数\n";
    }
}

echo "\n";

// 检查短信日志表是否存在
echo "检查短信日志表:\n";
try {
    $tableExists = DB::select("SHOW TABLES LIKE 'sms_logs'");
    if (empty($tableExists)) {
        echo "❌ sms_logs表不存在，无法查看历史发送记录\n";
    } else {
        echo "✅ sms_logs表存在\n";
        $recentSms = DB::table('sms_logs')
            ->where('template_code', 'consultation_notice')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();
        
        foreach ($recentSms as $sms) {
            echo "\n短信记录ID: {$sms->id}\n";
            echo "- 发送时间: {$sms->created_at}\n";
            echo "- 模板参数: {$sms->tpl_value}\n";
            echo "- 发送状态: {$sms->status}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 查询短信日志表时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";
echo "\n问题总结:\n";
echo "1. 检查云片网模板参数是否与buildTemplateData方法匹配\n";
echo "2. 检查数据库中的活动数据是否完整\n";
echo "3. 检查短信发送时的参数构建是否正确\n";