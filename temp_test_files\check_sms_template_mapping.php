<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\SmsTemplate;
use App\Models\ActivityDetail;
use App\Models\Registration;

echo "=== 检查短信模板映射问题 ===\n\n";

// 1. 查看现场咨询模板配置
echo "1. 现场咨询模板配置:\n";
$consultationTemplate = DB::table('sms_templates')
    ->where('code', 'consultation_notice')
    ->first();

if ($consultationTemplate) {
    echo "- ID: {$consultationTemplate->id}\n";
    echo "- 名称: {$consultationTemplate->name}\n";
    echo "- 云片模板ID: {$consultationTemplate->yunpian_template_id}\n";
    echo "- 描述: {$consultationTemplate->description}\n";
    echo "- 模板参数: {$consultationTemplate->template_params}\n";
    echo "- 参数说明: {$consultationTemplate->param_description}\n";
} else {
    echo "❌ 未找到现场咨询模板\n";
}

echo "\n";

// 2. 查看所有短信模板
echo "2. 所有短信模板列表:\n";
$allTemplates = DB::table('sms_templates')->get();
foreach ($allTemplates as $template) {
    echo "- [{$template->id}] {$template->name} (code: {$template->code})\n";
    echo "  云片模板ID: {$template->yunpian_template_id}\n";
    echo "  参数: {$template->template_params}\n";
    echo "\n";
}

// 3. 检查使用现场咨询模板的活动详情
echo "3. 使用现场咨询模板的活动详情:\n";
if ($consultationTemplate) {
    $activities = DB::table('activity_details')
        ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
        ->where('activity_details.sms_template_id', $consultationTemplate->id)
        ->select('activity_details.*', 'activities.title as activity_title')
        ->get();
    
    foreach ($activities as $activity) {
        echo "- 活动详情ID: {$activity->id}\n";
        echo "  活动标题: {$activity->activity_title}\n";
        echo "  主题: {$activity->theme}\n";
        echo "  时间: {$activity->activity_time}\n";
        echo "  地点: {$activity->address}\n";
        echo "  对象: {$activity->target}\n";
        echo "\n";
    }
}

// 4. 分析buildTemplateData方法的参数映射
echo "4. 分析参数映射问题:\n";
echo "buildTemplateData方法使用的参数:\n";
echo "- name: 来自 registration->name\n";
echo "- topic: 来自 activityDetail->activity->title\n";
echo "- time: 来自 activityDetail->activity_time\n";
echo "- address: 来自 activityDetail->address\n";
echo "- obj: 来自 activityDetail->target\n";
echo "\n";

if ($consultationTemplate && $consultationTemplate->template_params) {
    echo "云片网模板期望的参数:\n";
    $params = json_decode($consultationTemplate->template_params, true);
    if (is_array($params)) {
        foreach ($params as $param) {
            echo "- {$param}\n";
        }
    } else {
        echo "- 参数格式: {$consultationTemplate->template_params}\n";
        // 如果不是JSON格式，尝试按逗号分割
        $paramsList = explode(',', $consultationTemplate->template_params);
        foreach ($paramsList as $param) {
            $param = trim($param);
            if (!empty($param)) {
                echo "- {$param}\n";
            }
        }
    }
}

echo "\n";

// 5. 测试一个具体的报名记录
echo "5. 测试具体报名记录的参数构建:\n";
$registration = DB::table('registrations')
    ->join('activity_details', 'registrations.activity_detail_id', '=', 'activity_details.id')
    ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
    ->where('registrations.id', 1)
    ->select(
        'registrations.*',
        'activity_details.theme',
        'activity_details.activity_time',
        'activity_details.address',
        'activity_details.target',
        'activity_details.sms_template_id',
        'activities.title as activity_title'
    )
    ->first();

if ($registration) {
    echo "报名记录 ID: {$registration->id}\n";
    echo "构建的参数:\n";
    echo "- name: '{$registration->name}'\n";
    echo "- topic: '{$registration->activity_title}'\n";
    echo "- time: '{$registration->activity_time}'\n";
    echo "- address: '{$registration->address}'\n";
    echo "- obj: '{$registration->target}'\n";
    
    // 检查空值
    $emptyParams = [];
    if (empty($registration->name)) $emptyParams[] = 'name';
    if (empty($registration->activity_title)) $emptyParams[] = 'topic';
    if (empty($registration->activity_time)) $emptyParams[] = 'time';
    if (empty($registration->address)) $emptyParams[] = 'address';
    if (empty($registration->target)) $emptyParams[] = 'obj';
    
    if (!empty($emptyParams)) {
        echo "\n❌ 发现空值参数: " . implode(', ', $emptyParams) . "\n";
    } else {
        echo "\n✅ 所有参数都有值\n";
    }
}

echo "\n=== 分析完成 ===\n";