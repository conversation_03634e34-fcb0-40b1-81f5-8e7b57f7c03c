<?php

namespace App\Filament\Resources\AdministrativeInstitutionResource\Pages;

use App\Filament\Resources\AdministrativeInstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class ListAdministrativeInstitutions extends ListRecords
{
    protected static string $resource = AdministrativeInstitutionResource::class;

    // 设置默认分页大小，避免一次性加载过多数据
    protected static int $defaultPaginationPageOption = 25;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('tree_view')
                ->label('树形视图')
                ->icon('heroicon-o-squares-2x2')
                ->color('info')
                ->url(static::getResource()::getUrl('tree'))
                ->tooltip('切换到树形视图管理'),
            Actions\CreateAction::make()
                ->label('新增行政机构'),
        ];
    }

    public function getTabs(): array
    {
        // 使用缓存来优化count查询，避免每次都重新计算
        $cacheKey = 'admin_institutions_counts';
        $counts = Cache::remember($cacheKey, 300, function () { // 缓存5分钟
            return [
                'total' => $this->getModel()::count(),
                'province' => $this->getModel()::where('administrative_level', 'province')->count(),
                'city' => $this->getModel()::where('administrative_level', 'city')->count(),
                'district' => $this->getModel()::where('administrative_level', 'district')->count(),
                'town' => $this->getModel()::where('administrative_level', 'town')->count(),
                'bound' => $this->getModel()::where('binding_status', 'bound')->count(),
                'unbound' => $this->getModel()::where('binding_status', 'unbound')->count(),
            ];
        });

        return [
            '全部' => Tab::make()
                ->badge($counts['total']),
            '省级' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('administrative_level', 'province'))
                ->badge($counts['province']),
            '市级' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('administrative_level', 'city'))
                ->badge($counts['city']),
            '区县级' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('administrative_level', 'district'))
                ->badge($counts['district']),
            '乡镇级' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('administrative_level', 'town'))
                ->badge($counts['town']),
            '已绑定' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('binding_status', 'bound'))
                ->badge($counts['bound']),
            '未绑定' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('binding_status', 'unbound'))
                ->badge($counts['unbound']),
        ];
    }

    // 优化默认查询，添加分层显示逻辑
    protected function getTableQuery(): Builder
    {
        $query = parent::getTableQuery();
        
        // 默认按层级和排序显示，优先显示上级机构
        $query->orderByRaw("CASE 
            WHEN administrative_level = 'province' THEN 1
            WHEN administrative_level = 'city' THEN 2  
            WHEN administrative_level = 'district' THEN 3
            WHEN administrative_level = 'town' THEN 4
            ELSE 5
        END")
        ->orderBy('sort_order')
        ->orderBy('name');
        
        return $query;
    }

    // 清除缓存的方法，在数据更新时调用
    public function clearCountsCache(): void
    {
        Cache::forget('admin_institutions_counts');
        
        // 清除级联筛选器相关缓存
        $this->clearCascadingFilterCaches();
    }
    
    // 清除级联筛选器缓存
    private function clearCascadingFilterCaches(): void
    {
        // 清除省份缓存
        Cache::forget('admin_institutions_provinces');
        
        // 清除所有城市缓存（通过模式匹配）
        $cacheKeys = Cache::getRedis()->keys('*admin_institutions_cities_*');
        foreach ($cacheKeys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
        
        // 清除所有区县缓存
        $cacheKeys = Cache::getRedis()->keys('*admin_institutions_districts_*');
        foreach ($cacheKeys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
        
        // 清除所有乡镇缓存
        $cacheKeys = Cache::getRedis()->keys('*admin_institutions_towns_*');
        foreach ($cacheKeys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
    }
}