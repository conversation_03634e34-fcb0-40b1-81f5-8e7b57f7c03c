<?php

namespace App\Exports;

use App\Models\Registration;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RegistrationGradeExport implements FromCollection, WithHeadings, WithTitle
{
    public function collection()
    {
        return Registration::select('grade', DB::raw('COUNT(*) as count'))
            ->whereNotNull('grade')
            ->where('status', true) // 只统计有效报名
            ->groupBy('grade')
            ->orderBy('count', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            '年级',
            '报名人数'
        ];
    }

    public function title(): string
    {
        return '年级分布';
    }
}