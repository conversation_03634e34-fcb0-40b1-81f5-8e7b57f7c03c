<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BackupScheduleResource\Pages;
use App\Models\BackupSchedule;
use App\Models\DatabaseBackup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;

class BackupScheduleResource extends Resource
{
    protected static ?string $model = BackupSchedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationGroup = '数据备份';

    protected static ?string $navigationLabel = '备份计划';

    protected static ?int $navigationSort = 3;

    protected static ?int $navigationGroupSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('计划名称')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('例如：每日自动备份'),
                        Forms\Components\Select::make('backup_type')
                            ->label('备份类型')
                            ->options(DatabaseBackup::getBackupTypes())
                            ->required()
                            ->default(DatabaseBackup::TYPE_FULL),
                        Forms\Components\Textarea::make('description')
                            ->label('计划描述')
                            ->maxLength(500)
                            ->rows(3)
                            ->placeholder('请输入备份计划的详细描述'),
                        Forms\Components\Toggle::make('is_enabled')
                            ->label('启用计划')
                            ->default(true)
                            ->helperText('关闭后计划将不会自动执行'),
                    ])->columns(2),

                Forms\Components\Section::make('执行时间设置')
                    ->schema([
                        Forms\Components\Select::make('frequency')
                            ->label('执行频率')
                            ->options(BackupSchedule::getFrequencyOptions())
                            ->required()
                            ->default(BackupSchedule::FREQUENCY_DAILY)
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // 根据频率设置默认值
                                match ($state) {
                                    BackupSchedule::FREQUENCY_HOURLY => $set('frequency_value', 0),
                                    BackupSchedule::FREQUENCY_DAILY => $set('execute_time', '02:00'),
                                    BackupSchedule::FREQUENCY_WEEKLY => [
                                        $set('execute_day', 1),
                                        $set('execute_time', '02:00')
                                    ],
                                    BackupSchedule::FREQUENCY_MONTHLY => [
                                        $set('execute_date', 1),
                                        $set('execute_time', '02:00')
                                    ],
                                    default => null
                                };
                            }),

                        Forms\Components\TextInput::make('frequency_value')
                            ->label('执行分钟')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(59)
                            ->default(0)
                            ->helperText('每小时的第几分钟执行（0-59）')
                            ->visible(fn (Forms\Get $get): bool => $get('frequency') === BackupSchedule::FREQUENCY_HOURLY),

                        Forms\Components\TimePicker::make('execute_time')
                            ->label('执行时间')
                            ->default('02:00')
                            ->helperText('建议选择系统负载较低的时间段')
                            ->visible(fn (Forms\Get $get): bool => in_array($get('frequency'), [
                                BackupSchedule::FREQUENCY_DAILY,
                                BackupSchedule::FREQUENCY_WEEKLY,
                                BackupSchedule::FREQUENCY_MONTHLY
                            ])),

                        Forms\Components\Select::make('execute_day')
                            ->label('执行星期')
                            ->options(BackupSchedule::getWeekdayOptions())
                            ->default(1)
                            ->helperText('每周的哪一天执行备份')
                            ->visible(fn (Forms\Get $get): bool => $get('frequency') === BackupSchedule::FREQUENCY_WEEKLY),

                        Forms\Components\TextInput::make('execute_date')
                            ->label('执行日期')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(31)
                            ->default(1)
                            ->helperText('每月的第几号执行（1-31，如果当月没有该日期则使用月末）')
                            ->visible(fn (Forms\Get $get): bool => $get('frequency') === BackupSchedule::FREQUENCY_MONTHLY),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('计划名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('backup_type')
                    ->label('备份类型')
                    ->badge()
                    ->color(fn (BackupSchedule $record): string => match ($record->backup_type) {
                        DatabaseBackup::TYPE_FULL => 'success',
                        DatabaseBackup::TYPE_STRUCTURE => 'info',
                        DatabaseBackup::TYPE_DATA => 'warning',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn (string $state): string => DatabaseBackup::getBackupTypes()[$state] ?? $state),
                Tables\Columns\TextColumn::make('frequency_description')
                    ->label('执行频率')
                    ->wrap(),
                Tables\Columns\IconColumn::make('is_enabled')
                    ->label('状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('last_run_at')
                    ->label('上次执行')
                    ->dateTime('Y-m-d H:i:s')
                    ->placeholder('未执行')
                    ->sortable(),
                Tables\Columns\TextColumn::make('next_run_at')
                    ->label('下次执行')
                    ->dateTime('Y-m-d H:i:s')
                    ->placeholder('未计划')
                    ->sortable(),
                Tables\Columns\TextColumn::make('backups_count')
                    ->label('备份次数')
                    ->counts('backups')
                    ->sortable(),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('创建者')
                    ->default('系统'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('backup_type')
                    ->label('备份类型')
                    ->options(DatabaseBackup::getBackupTypes()),
                Tables\Filters\SelectFilter::make('frequency')
                    ->label('执行频率')
                    ->options(BackupSchedule::getFrequencyOptions()),
                Tables\Filters\TernaryFilter::make('is_enabled')
                    ->label('状态')
                    ->placeholder('全部')
                    ->trueLabel('已启用')
                    ->falseLabel('已禁用'),
            ])
            ->actions([
                Action::make('toggle_status')
                    ->label(fn (BackupSchedule $record): string => $record->is_enabled ? '禁用' : '启用')
                    ->icon(fn (BackupSchedule $record): string => $record->is_enabled ? 'heroicon-o-pause' : 'heroicon-o-play')
                    ->color(fn (BackupSchedule $record): string => $record->is_enabled ? 'warning' : 'success')
                    ->action(function (BackupSchedule $record) {
                        $record->is_enabled = !$record->is_enabled;
                        if ($record->is_enabled) {
                            $record->updateNextRunTime();
                        }
                        $record->save();
                        
                        Notification::make()
                            ->title($record->is_enabled ? '计划已启用' : '计划已禁用')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->modalHeading(fn (BackupSchedule $record): string => 
                        $record->is_enabled ? '禁用备份计划' : '启用备份计划'
                    )
                    ->modalDescription(fn (BackupSchedule $record): string => 
                        $record->is_enabled ? 
                        '确定要禁用此备份计划吗？禁用后将不会自动执行备份。' : 
                        '确定要启用此备份计划吗？启用后将按照设定的时间自动执行备份。'
                    ),
                Action::make('run_now')
                    ->label('立即执行')
                    ->icon('heroicon-o-play')
                    ->color('primary')
                    ->action(function (BackupSchedule $record) {
                        try {
                            $backupService = app(\App\Services\DatabaseBackupService::class);
                            
                            $backup = $backupService->createBackup([
                                'name' => $record->name . '_manual_' . now()->format('Y-m-d_H-i-s'),
                                'backup_type' => $record->backup_type,
                                'description' => "手动执行计划：{$record->name}",
                                'created_by' => auth()->id(),
                                'schedule_id' => $record->id,
                            ]);
                            
                            Notification::make()
                                ->title('备份任务已创建')
                                ->body("备份 '{$backup->name}' 正在后台执行")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('执行失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('立即执行备份')
                    ->modalDescription('确定要立即执行此备份计划吗？')
                    ->modalSubmitActionLabel('确认执行'),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBackupSchedules::route('/'),
            'create' => Pages\CreateBackupSchedule::route('/create'),
            'view' => Pages\ViewBackupSchedule::route('/{record}'),
            'edit' => Pages\EditBackupSchedule::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '备份计划';
    }

    public static function getModelLabel(): string
    {
        return '备份计划';
    }

    public static function getPluralModelLabel(): string
    {
        return '备份计划';
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::enabled()->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
