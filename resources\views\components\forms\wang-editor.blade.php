<div
    x-data="{
        value: @entangle('content'),
        editor: null,
        init() {
            const _this = this;
            this.editor = window.wangEditor.createEditor({
                selector: this.$refs.editor,
                html: this.value,
                config: {
                    MENU_CONF: {
                        uploadImage: {
                            server: '/wangeditor/upload/image',
                            fieldName: 'file',
                            // 可根据需要添加 headers、credentials 等
                        },
                        uploadVideo: {
                            server: '/wangeditor/upload/video',
                            fieldName: 'file',
                        },
                    },
                    onChange(editor) {
                        _this.value = editor.getHtml();
                    },
                    // 工具栏配置，包含源码按钮
                    toolbarKeys: [
                        'headerSelect', 'bold', 'italic', 'underline', 'color', 'bgColor', 'link',
                        'bulletedList', 'numberedList', 'todo', 'insertImage', 'insertVideo',
                        'codeBlock', 'blockquote', 'divider', 'undo', 'redo', 'fullScreen', 'code',
                        'clearStyle', 'insertTable', 'emotion', 'insertFormula', 'group-more',
                        {
                            key: 'code',
                            title: '源码',
                        },
                    ],
                },
            });
        },
    }"
    x-init="init()"
>
    <div x-ref="editor"></div>
</div>

<!-- wangEditor 5 CDN -->
<link rel="stylesheet" href="https://unpkg.com/@wangeditor/editor@5.1.23/dist/css/style.css">
<script src="https://unpkg.com/@wangeditor/editor@5.1.23/dist/index.js"></script> 