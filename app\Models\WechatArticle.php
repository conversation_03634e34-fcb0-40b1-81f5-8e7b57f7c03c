<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class WechatArticle extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'wechat_account_id',
        'media_id',
        'title',
        'digest',
        'content',
        'content_source_url',
        'url',
        'thumb_media_id',
        'thumb_url',
        'local_thumb_path',
        'author',
        'show_cover_pic',
        'read_count',
        'like_count',
        'comment_count',
        'collect_type',
        'collect_source',
        'published_at',
        'collected_at',
        'is_processed',
        'is_published',
        'article_id',
        'meta_data',
        'status',
        'error_message',
    ];

    protected $casts = [
        'show_cover_pic' => 'boolean',
        'read_count' => 'integer',
        'like_count' => 'integer',
        'comment_count' => 'integer',
        'published_at' => 'datetime',
        'collected_at' => 'datetime',
        'is_processed' => 'boolean',
        'is_published' => 'boolean',
        'meta_data' => 'array',
    ];

    /**
     * 关联的微信公众号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAccount::class);
    }

    /**
     * 关联的本站文章
     */
    public function article(): BelongsTo
    {
        return $this->belongsTo(Article::class);
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'pending' => '待处理',
            'processed' => '已处理',
            'published' => '已发布',
            'failed' => '失败',
            default => '未知状态'
        };
    }

    /**
     * 获取采集类型标签
     */
    public function getCollectTypeLabel(): string
    {
        return match($this->collect_type) {
            'api' => 'API接口',
            'crawler' => '爬虫采集',
            default => '未知方式'
        };
    }

    /**
     * 获取本地封面图片URL
     */
    public function getLocalThumbUrl(): ?string
    {
        if (!$this->local_thumb_path) {
            return null;
        }

        return Storage::disk('public')->url($this->local_thumb_path);
    }

    /**
     * 标记为已处理
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'is_processed' => true,
            'status' => 'processed',
        ]);
    }

    /**
     * 标记为已发布
     */
    public function markAsPublished(int $articleId): void
    {
        $this->update([
            'is_published' => true,
            'article_id' => $articleId,
            'status' => 'published',
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * 作用域：待处理的文章
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * 作用域：已处理的文章
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * 作用域：已发布的文章
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * 作用域：失败的文章
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * 作用域：按采集时间排序
     */
    public function scopeLatestCollected($query)
    {
        return $query->orderBy('collected_at', 'desc');
    }

    /**
     * 作用域：按发布时间排序
     */
    public function scopeLatestPublished($query)
    {
        return $query->orderBy('published_at', 'desc');
    }
}