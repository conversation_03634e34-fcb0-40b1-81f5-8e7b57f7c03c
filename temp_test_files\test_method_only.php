<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;

echo "=== 测试修复后的buildTemplateData方法逻辑 ===\n";

// 复制修复后的buildTemplateData方法逻辑
function buildTemplateData($registration, $activityDetail = null, $templateCode = 'activity_registration_confirm')
{
    // 如果没有传入activityDetail，从registration中获取
    if ($activityDetail === null) {
        $activityDetail = $registration->activityDetail;
    }
    $activity = $activityDetail->activity;
    
    // 云片网字段映射 - 使用更直观的字段名
    // #name# - 用户姓名
    $name = !empty($registration->name) ? $registration->name : "用户";
    
    // #topic# - 活动主题 (优先使用新的topic字段，然后是activity.title，最后是theme)
    $topic = "";
    if (!empty($activityDetail->topic)) {
        $topic = $activityDetail->topic;  // 新增的直观字段
    } elseif (!empty($activity->title)) {
        $topic = $activity->title;
    } elseif (!empty($activityDetail->theme)) {
        $topic = $activityDetail->theme;  // 兼容旧字段
    } else {
        $topic = "活动通知";
    }
    
    // #time# - 活动时间
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        try {
            $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
        } catch (Exception $e) {
            $time = "待定时间";
        }
    } else {
        $time = "待定时间";
    }
    
    // #address# - 活动地址
    $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
    
    // #obj# - 目标对象 (优先使用新的target_audience字段，然后是target)
    $obj = "";
    if (!empty($activityDetail->target_audience)) {
        $obj = $activityDetail->target_audience;  // 新增的直观字段
    } elseif (!empty($activityDetail->target)) {
        $obj = $activityDetail->target;  // 兼容旧字段
    } else {
        $obj = "全体人员";
    }
    
    $templateData = [
        "name" => $name,      // 对应云片网 #name#
        "topic" => $topic,    // 对应云片网 #topic#
        "time" => $time,      // 对应云片网 #time#
        "address" => $address, // 对应云片网 #address#
        "obj" => $obj         // 对应云片网 #obj#
    ];
    
    return $templateData;
}

try {
    // 获取测试数据
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到注册记录\n";
        exit;
    }
    
    echo "注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    
    // 测试1：只传registration参数
    echo "\n=== 测试1：只传registration参数 ===\n";
    $templateData1 = buildTemplateData($registration);
    foreach ($templateData1 as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 测试2：传递所有参数
    echo "\n=== 测试2：传递所有参数 ===\n";
    $templateData2 = buildTemplateData($registration, $registration->activityDetail, 'activity_registration_confirm');
    foreach ($templateData2 as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 检查关键字段
    echo "\n=== 字段检查 ===\n";
    if (empty($templateData2['time'])) {
        echo "❌ time字段为空\n";
    } else {
        echo "✅ time字段有值: '{$templateData2['time']}'\n";
    }
    
    if (empty($templateData2['obj'])) {
        echo "❌ obj字段为空\n";
    } else {
        echo "✅ obj字段有值: '{$templateData2['obj']}'\n";
    }
    
    // 验证两种调用方式结果一致
    if ($templateData1 === $templateData2) {
        echo "✅ 两种调用方式结果一致\n";
    } else {
        echo "❌ 两种调用方式结果不一致\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
