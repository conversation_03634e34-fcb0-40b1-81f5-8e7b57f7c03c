<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class BackupSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'backup_type',
        'frequency',
        'frequency_value',
        'execute_time',
        'execute_day',
        'execute_date',
        'is_enabled',
        'last_run_at',
        'next_run_at',
        'created_by',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
        'execute_time' => 'datetime:H:i',
    ];

    /**
     * 频率常量
     */
    const FREQUENCY_HOURLY = 'hourly';
    const FREQUENCY_DAILY = 'daily';
    const FREQUENCY_WEEKLY = 'weekly';
    const FREQUENCY_MONTHLY = 'monthly';
    const FREQUENCY_CUSTOM = 'custom';

    /**
     * 获取频率选项
     */
    public static function getFrequencyOptions(): array
    {
        return [
            self::FREQUENCY_HOURLY => '每小时',
            self::FREQUENCY_DAILY => '每天',
            self::FREQUENCY_WEEKLY => '每周',
            self::FREQUENCY_MONTHLY => '每月',
            self::FREQUENCY_CUSTOM => '自定义',
        ];
    }

    /**
     * 获取星期选项
     */
    public static function getWeekdayOptions(): array
    {
        return [
            1 => '星期一',
            2 => '星期二',
            3 => '星期三',
            4 => '星期四',
            5 => '星期五',
            6 => '星期六',
            0 => '星期日',
        ];
    }

    /**
     * 获取创建者关系
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取相关的备份记录
     */
    public function backups()
    {
        return $this->hasMany(DatabaseBackup::class, 'schedule_id');
    }

    /**
     * 计算下次执行时间
     */
    public function calculateNextRunTime(): Carbon
    {
        $now = Carbon::now();
        $executeTime = $this->execute_time ? Carbon::parse($this->execute_time) : Carbon::parse('02:00');

        switch ($this->frequency) {
            case self::FREQUENCY_HOURLY:
                // 每小时执行，取当前小时的指定分钟
                $minutes = $this->frequency_value ?? 0;
                $next = $now->copy()->minute($minutes)->second(0);
                if ($next->lte($now)) {
                    $next->addHour();
                }
                return $next;

            case self::FREQUENCY_DAILY:
                // 每天执行
                $next = $now->copy()
                    ->hour($executeTime->hour)
                    ->minute($executeTime->minute)
                    ->second(0);
                if ($next->lte($now)) {
                    $next->addDay();
                }
                return $next;

            case self::FREQUENCY_WEEKLY:
                // 每周执行
                $targetDay = $this->execute_day ?? 1; // 默认星期一
                $next = $now->copy()
                    ->hour($executeTime->hour)
                    ->minute($executeTime->minute)
                    ->second(0);

                // 找到下一个目标星期几
                while ($next->dayOfWeek !== $targetDay || $next->lte($now)) {
                    $next->addDay();
                }
                return $next;

            case self::FREQUENCY_MONTHLY:
                // 每月执行
                $targetDate = $this->execute_date ?? 1; // 默认每月1号
                $next = $now->copy()
                    ->day(min($targetDate, $now->daysInMonth))
                    ->hour($executeTime->hour)
                    ->minute($executeTime->minute)
                    ->second(0);

                if ($next->lte($now)) {
                    $next->addMonth();
                    $next->day(min($targetDate, $next->daysInMonth));
                }
                return $next;

            default:
                return $now->addDay();
        }
    }

    /**
     * 更新下次执行时间
     */
    public function updateNextRunTime(): void
    {
        $this->next_run_at = $this->calculateNextRunTime();
        $this->save();
    }

    /**
     * 检查是否应该执行
     */
    public function shouldRun(): bool
    {
        if (!$this->is_enabled) {
            return false;
        }

        if (!$this->next_run_at) {
            return false;
        }

        return Carbon::now()->gte($this->next_run_at);
    }

    /**
     * 标记为已执行
     */
    public function markAsRun(): void
    {
        $this->last_run_at = Carbon::now();
        $this->updateNextRunTime();
    }

    /**
     * 获取频率描述
     */
    public function getFrequencyDescriptionAttribute(): string
    {
        $frequency = self::getFrequencyOptions()[$this->frequency] ?? $this->frequency;

        switch ($this->frequency) {
            case self::FREQUENCY_HOURLY:
                $minutes = $this->frequency_value ?? 0;
                return "{$frequency} (第{$minutes}分钟)";

            case self::FREQUENCY_DAILY:
                $time = $this->execute_time ? Carbon::parse($this->execute_time)->format('H:i') : '02:00';
                return "{$frequency} ({$time})";

            case self::FREQUENCY_WEEKLY:
                $day = self::getWeekdayOptions()[$this->execute_day] ?? '星期一';
                $time = $this->execute_time ? Carbon::parse($this->execute_time)->format('H:i') : '02:00';
                return "{$frequency} ({$day} {$time})";

            case self::FREQUENCY_MONTHLY:
                $date = $this->execute_date ?? 1;
                $time = $this->execute_time ? Carbon::parse($this->execute_time)->format('H:i') : '02:00';
                return "{$frequency} (每月{$date}号 {$time})";

            default:
                return $frequency;
        }
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute(): string
    {
        return $this->is_enabled ? 'success' : 'gray';
    }

    /**
     * 作用域：启用的计划
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：应该执行的计划
     */
    public function scopeShouldRun($query)
    {
        return $query->enabled()
            ->where('next_run_at', '<=', Carbon::now())
            ->whereNotNull('next_run_at');
    }

    /**
     * 获取应该执行的计划（静态方法）
     */
    public static function getSchedulesToRun()
    {
        return static::query()->shouldRun()->get();
    }
}
