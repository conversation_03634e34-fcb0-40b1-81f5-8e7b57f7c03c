<?php

namespace App\Console\Commands;

use App\Jobs\SyncWechatMenuJob;
use App\Models\WechatAccount;
use App\Services\WechatMenuService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncWechatMenuCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wechat:sync-menu 
                            {account? : 微信账号ID}
                            {--action=create : 操作类型 (create|delete|get)}
                            {--all : 同步所有活跃账号的菜单}
                            {--queue : 使用队列处理}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步微信自定义菜单';

    /**
     * Execute the console command.
     */
    public function handle(WechatMenuService $menuService): int
    {
        $accountId = $this->argument('account');
        $action = $this->option('action');
        $all = $this->option('all');
        $useQueue = $this->option('queue');

        // 验证操作类型
        if (!in_array($action, ['create', 'delete', 'get'])) {
            $this->error('无效的操作类型，支持的操作: create, delete, get');
            return self::FAILURE;
        }

        $this->info("开始执行微信菜单{$this->getActionLabel($action)}操作...");

        if ($all) {
            // 处理所有活跃账号
            $accounts = WechatAccount::active()->get();
            
            if ($accounts->isEmpty()) {
                $this->info('没有找到活跃的微信账号');
                return self::SUCCESS;
            }

            $this->info("找到 {$accounts->count()} 个活跃账号");
            
            $progressBar = $this->output->createProgressBar($accounts->count());
            $progressBar->start();

            $success = 0;
            $failed = 0;

            foreach ($accounts as $account) {
                try {
                    if ($useQueue) {
                        SyncWechatMenuJob::dispatch($account, $action);
                        $this->info("账号 {$account->name} 的菜单{$this->getActionLabel($action)}任务已加入队列");
                    } else {
                        $result = $this->executeAction($menuService, $account->id, $action);
                        if ($result) {
                            $success++;
                        } else {
                            $failed++;
                        }
                    }
                } catch (\Exception $e) {
                    $failed++;
                    $this->error("账号 {$account->name} 处理失败: {$e->getMessage()}");
                }
                
                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine();
            
            if (!$useQueue) {
                $this->info("处理完成！成功: {$success}, 失败: {$failed}");
            }
        } else {
            // 处理单个账号
            if (!$accountId) {
                $this->error('请指定微信账号ID或使用 --all 选项处理所有账号');
                return self::FAILURE;
            }

            $account = WechatAccount::find($accountId);
            if (!$account) {
                $this->error("未找到ID为 {$accountId} 的微信账号");
                return self::FAILURE;
            }

            if (!$account->is_active) {
                $this->error("微信账号 {$account->name} 未启用");
                return self::FAILURE;
            }

            try {
                if ($useQueue) {
                    SyncWechatMenuJob::dispatch($account, $action);
                    $this->info("账号 {$account->name} 的菜单{$this->getActionLabel($action)}任务已加入队列");
                } else {
                    $result = $this->executeAction($menuService, $accountId, $action);
                    if ($result) {
                        $this->info("账号 {$account->name} 菜单{$this->getActionLabel($action)}成功");
                    } else {
                        $this->error("账号 {$account->name} 菜单{$this->getActionLabel($action)}失败");
                        return self::FAILURE;
                    }
                }
            } catch (\Exception $e) {
                $this->error("处理失败: {$e->getMessage()}");
                return self::FAILURE;
            }
        }

        return self::SUCCESS;
    }

    /**
     * 执行具体的菜单操作
     */
    private function executeAction(WechatMenuService $menuService, int $accountId, string $action): bool
    {
        switch ($action) {
            case 'create':
                return $menuService->createMenu($accountId);
            case 'delete':
                return $menuService->deleteMenu($accountId);
            case 'get':
                return $menuService->getMenu($accountId);
            default:
                return false;
        }
    }

    /**
     * 获取操作标签
     */
    private function getActionLabel(string $action): string
    {
        return match ($action) {
            'create' => '创建',
            'delete' => '删除',
            'get' => '获取',
            default => $action,
        };
    }
}