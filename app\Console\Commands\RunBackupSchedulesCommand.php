<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BackupSchedule;
use App\Services\DatabaseBackupService;
use App\Services\DingTalkService;

class RunBackupSchedulesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:run-schedules';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '执行到期的备份计划';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始检查备份计划...');

        // 获取应该执行的计划
        $schedules = BackupSchedule::getSchedulesToRun();

        if ($schedules->isEmpty()) {
            $this->info('没有需要执行的备份计划');
            return 0;
        }

        $this->info("找到 {$schedules->count()} 个需要执行的备份计划");

        $backupService = app(DatabaseBackupService::class);
        $dingTalkService = app(DingTalkService::class);
        $successCount = 0;
        $failCount = 0;

        foreach ($schedules as $schedule) {
            $this->info("执行计划: {$schedule->name}");

            try {
                // 创建备份
                $backup = $backupService->createBackup([
                    'name' => $schedule->name . '_' . now()->format('Y-m-d_H-i-s'),
                    'backup_type' => $schedule->backup_type,
                    'description' => "计划任务执行：{$schedule->name}",
                    'created_by' => null,
                    'schedule_id' => $schedule->id,
                ]);

                // 标记计划为已执行
                $schedule->markAsRun();

                $this->info("✅ 计划 '{$schedule->name}' 执行成功，备份ID: {$backup->id}");
                $successCount++;

                // 发送成功通知
                $dingTalkService->sendScheduleStatusNotification(
                    $schedule->name,
                    'success',
                    "备份任务已创建，备份ID: {$backup->id}"
                );

            } catch (\Exception $e) {
                $this->error("❌ 计划 '{$schedule->name}' 执行失败: " . $e->getMessage());
                $failCount++;

                // 发送失败通知
                $dingTalkService->sendScheduleStatusNotification(
                    $schedule->name,
                    'failed',
                    $e->getMessage()
                );

                // 记录错误日志
                \Log::error("备份计划执行失败", [
                    'schedule_id' => $schedule->id,
                    'schedule_name' => $schedule->name,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        $this->newLine();
        $this->info("备份计划执行完成:");
        $this->info("- 成功: {$successCount}");
        $this->info("- 失败: {$failCount}");

        return $failCount > 0 ? 1 : 0;
    }
}
