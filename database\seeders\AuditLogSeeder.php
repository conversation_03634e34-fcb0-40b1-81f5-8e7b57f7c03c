<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AuditLog;
use App\Models\User;
use Carbon\Carbon;

class AuditLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            $admin = User::first();
        }

        // 创建审计日志
        $logs = [
            [
                'user_id' => $admin->id,
                'operated_at' => now()->subHours(2),
                'operation_type' => 'CREATE',
                'module' => '用户管理',
                'target_id' => '1',
                'request_ip' => '*************',
                'request_url' => '/admin/users/create',
                'request_data' => json_encode(['name' => '新用户', 'email' => '<EMAIL>']),
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => '创建新用户成功',
            ],
            [
                'user_id' => $admin->id,
                'operated_at' => now()->subHours(1),
                'operation_type' => 'UPDATE',
                'module' => '文章管理',
                'target_id' => '2',
                'request_ip' => '*************',
                'request_url' => '/admin/posts/2/edit',
                'request_data' => json_encode(['title' => '更新文章标题']),
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => '更新文章信息',
            ],
            [
                'user_id' => $admin->id,
                'operated_at' => now()->subMinutes(30),
                'operation_type' => 'DELETE',
                'module' => '分类管理',
                'target_id' => '5',
                'request_ip' => '*************',
                'request_url' => '/admin/categories/5/delete',
                'request_data' => json_encode(['id' => 5]),
                'status' => 'FAILED',
                'error_message' => '该分类下还有文章，无法删除',
                'remark' => '删除分类失败',
            ],
            [
                'user_id' => $admin->id,
                'operated_at' => now()->subMinutes(15),
                'operation_type' => 'LOGIN',
                'module' => '系统登录',
                'target_id' => $admin->id,
                'request_ip' => '*************',
                'request_url' => '/admin/login',
                'request_data' => json_encode(['email' => $admin->email]),
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => '管理员登录系统',
            ],
            [
                'user_id' => $admin->id,
                'operated_at' => now()->subMinutes(5),
                'operation_type' => 'VIEW',
                'module' => '成就中心',
                'target_id' => null,
                'request_ip' => '*************',
                'request_url' => '/admin/achievement-center',
                'request_data' => json_encode([]),
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => '查看成就中心数据',
            ],
        ];

        foreach ($logs as $logData) {
            AuditLog::create($logData);
        }

        $this->command->info('审计日志数据创建成功！');
        $this->command->info('- 创建了 5 条审计日志记录');
    }
}
