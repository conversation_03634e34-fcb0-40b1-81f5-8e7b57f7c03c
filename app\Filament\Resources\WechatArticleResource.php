<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WechatArticleResource\Pages;
use App\Models\WechatAccount;
use App\Models\WechatArticle;
use App\Services\WechatArticleCollectService;
use Kahusoftware\FilamentCkeditorField\CKEditor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WechatArticleResource extends Resource
{
    protected static ?string $model = WechatArticle::class;

    protected static ?string $navigationGroup = '微信推文管理';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    
    protected static ?string $navigationLabel = '微信文章';
    
    protected static ?string $slug = 'wechat-articles';
    
    protected static ?int $navigationSort = 20;
    
    protected static ?int $navigationGroupSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\Select::make('wechat_account_id')
                            ->label('公众号')
                            ->relationship('wechatAccount', 'name')
                            ->searchable()
                            ->preload(),
                        
                        Forms\Components\TextInput::make('title')
                            ->label('文章标题')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),
                        
                        Forms\Components\Textarea::make('digest')
                            ->label('文章摘要')
                            ->rows(3)
                            ->columnSpanFull(),
                        
                        Forms\Components\TextInput::make('author')
                            ->label('作者')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('url')
                            ->label('原文链接')
                            ->url()
                            ->maxLength(500),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('封面图片')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('thumb_url')
                                    ->label('原始封面链接')
                                    ->url()
                                    ->maxLength(500)
                                    ->columnSpan(1),
                                
                                Forms\Components\Toggle::make('show_cover_pic')
                                    ->label('显示封面')
                                    ->default(true)
                                    ->columnSpan(1),
                            ]),
                        
                        Forms\Components\FileUpload::make('local_thumb_path')
                            ->label('本地封面图片')
                            ->image()
                            ->directory('wechat/thumbs')
                            ->visibility('public')
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9')
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('450')
                            ->maxSize(5120) // 5MB
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->helperText('建议尺寸：800x450像素，支持JPG、PNG、WebP格式，最大5MB')
                            ->columnSpanFull(),
                    ]),
                
                Forms\Components\Section::make('文章内容')
                    ->schema([
                        CKEditor::make('content')
                            ->label('文章内容')
                            ->required()
                            ->columnSpanFull()
                            ->uploadUrl('/admin/ckeditor/upload'),
                    ]),
                
                Forms\Components\Section::make('采集信息')
                    ->schema([
                        Forms\Components\Select::make('collect_type')
                            ->label('采集方式')
                            ->options([
                                'api' => 'API接口',
                                'crawler' => '爬虫采集',
                            ])
                            ->default('api'),
                        
                        Forms\Components\TextInput::make('collect_source')
                            ->label('采集来源')
                            ->maxLength(255),
                        
                        Forms\Components\Select::make('status')
                            ->label('状态')
                            ->options([
                                'pending' => '待处理',
                                'processed' => '已处理',
                                'published' => '已发布',
                                'failed' => '失败',
                            ])
                            ->default('pending'),
                        
                        Forms\Components\DateTimePicker::make('published_at')
                            ->label('发布时间'),
                        
                        Forms\Components\DateTimePicker::make('collected_at')
                            ->label('采集时间')
                            ->default(now()),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('统计数据')
                    ->schema([
                        Forms\Components\TextInput::make('read_count')
                            ->label('阅读数')
                            ->numeric()
                            ->default(0),
                        
                        Forms\Components\TextInput::make('like_count')
                            ->label('点赞数')
                            ->numeric()
                            ->default(0),
                        
                        Forms\Components\TextInput::make('comment_count')
                            ->label('评论数')
                            ->numeric()
                            ->default(0),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('local_thumb_path')
                    ->label('封面')
                    ->disk('public')
                    ->size(60),
                
                Tables\Columns\TextColumn::make('title')
                    ->label('标题')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                
                Tables\Columns\TextColumn::make('wechatAccount.name')
                    ->label('公众号')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('author')
                    ->label('作者')
                    ->searchable()
                    ->toggleable(),
                
                Tables\Columns\TextColumn::make('collect_type')
                    ->label('采集方式')
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'api' => 'API接口',
                        'crawler' => '爬虫采集',
                        default => '未知方式'
                    })
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'api' => 'success',
                        'crawler' => 'info',
                        default => 'gray'
                    }),
                
                Tables\Columns\TextColumn::make('status')
                    ->label('状态')
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'pending' => '待处理',
                        'processed' => '已处理',
                        'published' => '已发布',
                        'failed' => '失败',
                        default => '未知状态'
                    })
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'pending' => 'warning',
                        'processed' => 'info',
                        'published' => 'success',
                        'failed' => 'danger',
                        default => 'gray'
                    }),
                
                Tables\Columns\TextColumn::make('read_count')
                    ->label('阅读')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),
                
                Tables\Columns\TextColumn::make('like_count')
                    ->label('点赞')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),
                
                Tables\Columns\TextColumn::make('published_at')
                    ->label('发布时间')
                    ->dateTime('m-d H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('collected_at')
                    ->label('采集时间')
                    ->dateTime('m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('wechat_account_id')
                    ->label('公众号')
                    ->relationship('wechatAccount', 'name')
                    ->searchable()
                    ->preload(),
                
                Tables\Filters\SelectFilter::make('status')
                    ->label('状态')
                    ->options([
                        'pending' => '待处理',
                        'processed' => '已处理',
                        'published' => '已发布',
                        'failed' => '失败',
                    ]),
                
                Tables\Filters\SelectFilter::make('collect_type')
                    ->label('采集方式')
                    ->options([
                        'api' => 'API接口',
                        'crawler' => '爬虫采集',
                    ]),
                
                Tables\Filters\Filter::make('published_at')
                    ->form([
                        Forms\Components\DatePicker::make('published_from')
                            ->label('发布开始日期'),
                        Forms\Components\DatePicker::make('published_until')
                            ->label('发布结束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['published_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('published_at', '>=', $date),
                            )
                            ->when(
                                $data['published_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('published_at', '<=', $date),
                            );
                    }),
                
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                Tables\Actions\Action::make('preview')
                    ->label('预览')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(fn (WechatArticle $record): string => $record->url ?: '#')
                    ->openUrlInNewTab()
                    ->visible(fn (WechatArticle $record): bool => !empty($record->url)),
                
                Tables\Actions\Action::make('convert')
                    ->label('转为文章')
                    ->icon('heroicon-o-arrow-right')
                    ->color('success')
                    ->form([
                        Forms\Components\Toggle::make('auto_publish')
                            ->label('自动发布')
                            ->default(false),
                    ])
                    ->action(function (WechatArticle $record, array $data) {
                        try {
                            $service = app(WechatArticleCollectService::class);
                            $article = $service->convertToArticle($record, $data);
                            
                            \Filament\Notifications\Notification::make()
                                ->title('转换成功')
                                ->body('文章已成功转换为本站文章')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('转换失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (WechatArticle $record): bool => !$record->is_published),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('batchConvert')
                        ->label('批量转换')
                        ->icon('heroicon-o-arrow-right')
                        ->color('success')
                        ->form([
                            Forms\Components\Toggle::make('auto_publish')
                                ->label('自动发布')
                                ->default(false),
                        ])
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records, array $data) {
                            $service = app(WechatArticleCollectService::class);
                            $successCount = 0;
                            $failCount = 0;
                            
                            foreach ($records as $record) {
                                if ($record->is_published) {
                                    continue;
                                }
                                
                                try {
                                    $service->convertToArticle($record, $data);
                                    $successCount++;
                                } catch (\Exception $e) {
                                    $failCount++;
                                }
                            }
                            
                            \Filament\Notifications\Notification::make()
                                ->title('批量转换完成')
                                ->body("成功转换 {$successCount} 篇文章，失败 {$failCount} 篇")
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('collected_at', 'desc');
    }
    
    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        TextEntry::make('title')
                            ->label('标题')
                            ->columnSpanFull(),
                        
                        TextEntry::make('wechatAccount.name')
                            ->label('公众号'),
                        
                        TextEntry::make('author')
                            ->label('作者'),
                        
                        TextEntry::make('digest')
                            ->label('摘要')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                
                Section::make('封面图片')
                    ->schema([
                        ImageEntry::make('local_thumb_path')
                            ->label('本地封面')
                            ->disk('public')
                            ->size(200),
                    ])
                    ->visible(fn (WechatArticle $record): bool => !empty($record->local_thumb_path)),
                
                Section::make('采集信息')
                    ->schema([
                        TextEntry::make('collect_type')
                            ->label('采集方式')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'api' => 'API接口',
                                'crawler' => '爬虫采集',
                                default => '未知方式'
                            }),
                        
                        TextEntry::make('status')
                            ->label('状态')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'pending' => '待处理',
                                'processed' => '已处理',
                                'published' => '已发布',
                                'failed' => '失败',
                                default => '未知状态'
                            })
                            ->badge()
                            ->color(fn (string $state): string => match($state) {
                                'pending' => 'warning',
                                'processed' => 'info',
                                'published' => 'success',
                                'failed' => 'danger',
                                default => 'gray'
                            }),
                        
                        TextEntry::make('published_at')
                            ->label('发布时间')
                            ->dateTime('Y-m-d H:i:s'),
                        
                        TextEntry::make('collected_at')
                            ->label('采集时间')
                            ->dateTime('Y-m-d H:i:s'),
                    ])
                    ->columns(2),
                
                Section::make('统计数据')
                    ->schema([
                        TextEntry::make('read_count')
                            ->label('阅读数')
                            ->numeric(),
                        
                        TextEntry::make('like_count')
                            ->label('点赞数')
                            ->numeric(),
                        
                        TextEntry::make('comment_count')
                            ->label('评论数')
                            ->numeric(),
                    ])
                    ->columns(3),
                
                Section::make('文章内容')
                    ->schema([
                        TextEntry::make('content')
                            ->label('内容')
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWechatArticles::route('/'),
            'create' => Pages\CreateWechatArticle::route('/create'),
            'view' => Pages\ViewWechatArticle::route('/{record}'),
            'edit' => Pages\EditWechatArticle::route('/{record}/edit'),
        ];
    }
    
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}