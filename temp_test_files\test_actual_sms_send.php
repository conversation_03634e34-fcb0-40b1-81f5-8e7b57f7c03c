<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;

echo "=== 实际SMS发送测试 ===\n";

try {
    // 获取测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到测试注册记录\n";
        exit(1);
    }
    
    $activityDetail = $registration->activityDetail;
    
    echo "测试注册记录: {$registration->name} ({$registration->phone})\n";
    echo "活动详情ID: {$activityDetail->id}\n";
    echo "SMS模板ID: {$activityDetail->sms_template_id}\n";
    
    // 手动构建模板数据（模拟RegistrationController的逻辑）
    $activity = $activityDetail->activity;
    
    $templateData = [
        "name" => !empty($registration->name) ? $registration->name : "用户",
        "topic" => !empty($activityDetail->topic) ? $activityDetail->topic : 
                   (!empty($activity->title) ? $activity->title : 
                   (!empty($activityDetail->theme) ? $activityDetail->theme : "活动通知")),
        "time" => !empty($activityDetail->activity_time) ? 
                  date("Y年m月d日 H:i", strtotime($activityDetail->activity_time)) : "待定时间",
        "address" => !empty($activityDetail->address) ? $activityDetail->address : "待定地点",
        "obj" => !empty($activityDetail->target_audience) ? $activityDetail->target_audience : 
                 (!empty($activityDetail->target) ? $activityDetail->target : "全体人员")
    ];
    
    echo "\n=== 构建的模板数据 ===\n";
    foreach ($templateData as $key => $value) {
        echo "#{$key}#: '{$value}'\n";
    }
    
    // 获取SMS服务实例
    $smsService = app(SmsService::class);
    
    // 模拟发送SMS（不实际发送，只构建消息）
    echo "\n=== SMS服务测试 ===\n";
    
    // 检查SMS模板
    if ($activityDetail->sms_template_id) {
        $smsTemplate = \App\Models\SmsTemplate::find($activityDetail->sms_template_id);
        if ($smsTemplate) {
            echo "使用SMS模板: {$smsTemplate->name}\n";
            echo "模板代码: {$smsTemplate->template_code}\n";
            echo "模板内容: {$smsTemplate->content}\n";
            
            // 替换模板参数
            $message = $smsTemplate->content;
            foreach ($templateData as $key => $value) {
                $placeholder = "#{$key}#";
                $message = str_replace($placeholder, $value, $message);
                echo "替换 {$placeholder} -> '{$value}'\n";
            }
            
            echo "\n最终SMS内容:\n";
            echo "'{$message}'\n";
            
            // 检查是否还有未替换的占位符
            if (preg_match_all('/#([^#]+)#/', $message, $matches)) {
                echo "\n⚠ 警告：发现未替换的占位符: " . implode(', ', $matches[0]) . "\n";
            } else {
                echo "\n✓ 所有占位符都已正确替换\n";
            }
        } else {
            echo "SMS模板不存在\n";
        }
    } else {
        echo "未设置SMS模板ID\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
