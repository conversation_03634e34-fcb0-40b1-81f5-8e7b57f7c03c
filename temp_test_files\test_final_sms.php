<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\SmsTemplate;
use App\Services\SmsService;
use Illuminate\Support\Facades\DB;

echo "=== 测试修复后的SMS模板 ===\n";

try {
    // 获取一个测试报名记录
    $registration = Registration::with(['activity', 'activityDetail'])->first();
    
    if (!$registration) {
        echo "未找到报名记录\n";
        exit(1);
    }
    
    echo "测试报名记录 ID: {$registration->id}\n";
    echo "活动名称: {$registration->activity->title}\n";
    
    // 获取现场咨询通知模板
    $template = SmsTemplate::where('name', '现场咨询通知')->first();
    
    if (!$template) {
        echo "未找到现场咨询通知模板\n";
        exit(1);
    }
    
    echo "\n模板信息:\n";
    echo "模板名称: {$template->name}\n";
    echo "模板内容: {$template->description}\n";
    
    // 手动构建模板数据（模拟RegistrationController的逻辑）
    $activityDetail = $registration->activityDetail;
    $activity = $registration->activity;
    
    $templateData = [
        'name' => $registration->name,
        'topic' => $activityDetail->topic ?: ($activityDetail->theme ?: $activity->title),
        'time' => $activity->activity_time ? date('Y年m月d日 H:i', strtotime($activity->activity_time)) : '',
        'address' => $activityDetail->address ?: '',
        'obj' => $activityDetail->target_audience ?: ($activityDetail->target ?: '')
    ];
    
    echo "\n模板数据:\n";
    foreach ($templateData as $key => $value) {
        echo "#{$key}#: '{$value}'\n";
    }
    
    // 使用SmsService构建最终短信内容
    $smsService = app(SmsService::class);
    $finalContent = $smsService->buildSmsContent($template->description, $templateData);
    
    echo "\n=== 最终短信内容 ===\n";
    echo $finalContent . "\n";
    
    // 检查是否还有未替换的占位符
    if (preg_match('/#\w+#/', $finalContent)) {
        echo "\n⚠️ 警告：仍有未替换的占位符\n";
    } else {
        echo "\n✓ 所有占位符已正确替换\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
