<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\SmsTemplate;

echo "=== SMS模板选择功能测试 ===\n\n";

// 1. 检查可用的SMS模板
echo "1. 可用的SMS模板列表：\n";
$templates = SmsTemplate::select('id', 'name', 'code', 'template_category')->get();
foreach ($templates as $template) {
    echo "   ID: {$template->id} | 名称: {$template->name} | 代码: {$template->code} | 分类: {$template->template_category}\n";
}
echo "\n";

// 2. 检查活动场次的SMS模板关联
echo "2. 活动场次SMS模板关联情况：\n";
$activities = ActivityDetail::with('smsTemplate')->take(5)->get();
foreach ($activities as $activity) {
    $templateInfo = $activity->smsTemplate ? 
        "{$activity->smsTemplate->name} ({$activity->smsTemplate->code})" : 
        "未设置";
    echo "   活动: {$activity->theme} | SMS模板: {$templateInfo}\n";
}
echo "\n";

// 3. 测试为活动场次设置SMS模板
echo "3. 测试为活动场次设置SMS模板：\n";
$firstActivity = ActivityDetail::first();
if ($firstActivity) {
    $consultationTemplate = SmsTemplate::where('code', 'consultation_notice')->first();
    if ($consultationTemplate) {
        $firstActivity->sms_template_id = $consultationTemplate->id;
        $firstActivity->save();
        
        // 重新加载以验证
        $firstActivity->load('smsTemplate');
        echo "   成功为活动 '{$firstActivity->theme}' 设置SMS模板: {$firstActivity->smsTemplate->name}\n";
    } else {
        echo "   未找到 'consultation_notice' 模板\n";
    }
} else {
    echo "   未找到活动场次数据\n";
}
echo "\n";

// 4. 验证SMS模板关联查询
echo "4. 验证SMS模板关联查询：\n";
$activitiesWithTemplate = ActivityDetail::whereHas('smsTemplate')->with('smsTemplate')->get();
echo "   有SMS模板的活动场次数量: {$activitiesWithTemplate->count()}\n";
foreach ($activitiesWithTemplate->take(3) as $activity) {
    echo "   - {$activity->theme}: {$activity->smsTemplate->name}\n";
}
echo "\n";

// 5. 测试SMS模板选项生成（模拟Filament表单选项）
echo "5. SMS模板选项生成测试：\n";
$options = SmsTemplate::all()->mapWithKeys(function ($template) {
    return [$template->id => $template->name . ' (' . $template->code . ')'];
});
echo "   生成的选项数量: {$options->count()}\n";
foreach ($options->take(5) as $id => $label) {
    echo "   ID: {$id} => {$label}\n";
}

echo "\n=== 测试完成 ===\n";