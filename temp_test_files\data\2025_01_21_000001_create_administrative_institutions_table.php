<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('administrative_institutions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('单位名称');
            $table->string('code')->unique()->nullable()->comment('机构代码');
            
            // 地区信息 - 省市区级联
            $table->string('province_code', 10)->nullable()->comment('省份代码');
            $table->string('province_name')->nullable()->comment('省份名称');
            $table->string('city_code', 10)->nullable()->comment('城市代码');
            $table->string('city_name')->nullable()->comment('城市名称');
            $table->string('district_code', 10)->nullable()->comment('区县代码');
            $table->string('district_name')->nullable()->comment('区县名称');
            $table->string('town_code', 10)->nullable()->comment('乡镇代码');
            $table->string('town_name')->nullable()->comment('乡镇名称');
            
            // 行政级别
            $table->enum('administrative_level', ['province', 'city', 'district', 'town'])
                  ->comment('行政级别：省级、市级、区县级、乡镇级');
            
            // 组织信息
            $table->string('host_unit')->nullable()->comment('主办单位');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('组织归属（上级机构ID）');
            
            // 绑定状态
            $table->enum('binding_status', ['bound', 'unbound'])
                  ->default('unbound')
                  ->comment('组织绑定状态：已绑定、未绑定');
            
            // 联系信息
            $table->string('contact_person')->nullable()->comment('联系人');
            $table->string('contact_phone')->nullable()->comment('联系电话');
            $table->string('contact_email')->nullable()->comment('联系邮箱');
            $table->text('address')->nullable()->comment('详细地址');
            
            // 其他字段
            $table->text('description')->nullable()->comment('机构描述');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['province_code', 'city_code', 'district_code', 'town_code'], 'admin_inst_region_idx');
            $table->index(['administrative_level'], 'admin_inst_level_idx');
            $table->index(['binding_status'], 'admin_inst_binding_idx');
            $table->index(['parent_id'], 'admin_inst_parent_idx');
            $table->index(['is_active'], 'admin_inst_active_idx');
            $table->index(['sort_order'], 'admin_inst_sort_idx');
            
            // 外键约束
            $table->foreign('parent_id')->references('id')->on('administrative_institutions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('administrative_institutions');
    }
};