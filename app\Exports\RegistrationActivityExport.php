<?php

namespace App\Exports;

use App\Models\ActivityDetail;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RegistrationActivityExport implements FromCollection, WithHeadings, WithTitle
{
    public function collection()
    {
        return ActivityDetail::withCount(['registrations' => function ($query) {
                $query->where('status', true); // 只统计有效报名
            }])
            ->orderBy('registrations_count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'theme' => $item->theme,
                    'count' => $item->registrations_count,
                    'quota' => $item->quota,
                    'percentage' => round(($item->registrations_count / $item->quota) * 100, 2) . '%'
                ];
            });
    }

    public function headings(): array
    {
        return [
            '活动主题',
            '报名人数',
            '名额',
            '报名率'
        ];
    }

    public function title(): string
    {
        return '活动报名情况';
    }
}