<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "=== 检查activity_details表结构 ===\n";

try {
    // 检查字段是否存在
    $hasTopicField = Schema::hasColumn('activity_details', 'topic');
    $hasTargetAudienceField = Schema::hasColumn('activity_details', 'target_audience');
    
    echo "topic字段存在: " . ($hasTopicField ? '是' : '否') . "\n";
    echo "target_audience字段存在: " . ($hasTargetAudienceField ? '是' : '否') . "\n";
    
    // 获取表结构
    $columns = DB::select('DESCRIBE activity_details');
    echo "\n=== activity_details表所有字段 ===\n";
    foreach ($columns as $column) {
        echo "字段: {$column->Field}, 类型: {$column->Type}, 允许NULL: {$column->Null}, 默认值: {$column->Default}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
