<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use SolutionForest\FilamentTree\Components\Tree;

class ListCategories extends ListRecords
{
    protected static string $resource = CategoryResource::class;

    public function tree(): Tree
    {
        return Tree::make($this)
            ->primaryColumn('name')
            ->parentColumn('parent_id')
            ->columns([
                \Filament\Tables\Columns\TextColumn::make('name')->label('名称'),
                \Filament\Tables\Columns\TextColumn::make('slug')->label('别名'),
                \Filament\Tables\Columns\TextColumn::make('parent.name')->label('父级分类'),
                \Filament\Tables\Columns\IconColumn::make('is_active')->label('状态')->boolean(),
                \Filament\Tables\Columns\TextColumn::make('sort_order')->label('排序'),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return '栏目';
    }
} 