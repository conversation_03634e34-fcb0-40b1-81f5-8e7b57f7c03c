<?php

namespace App\Exports;

use App\Models\Registration;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RegistrationGenderExport implements FromCollection, WithHeadings, WithTitle
{
    public function collection()
    {
        return Registration::select('gender', DB::raw('COUNT(*) as count'))
            ->where('status', true) // 只统计有效报名
            ->groupBy('gender')
            ->get()
            ->map(function ($item) {
                $item->gender = $item->gender === 'male' ? '男' : '女';
                return $item;
            });
    }

    public function headings(): array
    {
        return [
            '性别',
            '报名人数'
        ];
    }

    public function title(): string
    {
        return '性别比例';
    }
}