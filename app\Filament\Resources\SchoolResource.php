<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SchoolResource\Pages;
use App\Models\School;
use App\Models\AdministrativeInstitution;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Cache;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use App\Filament\Resources\SchoolResource\Filters\CascadingRegionFilter;

class SchoolResource extends Resource
{
    protected static ?string $model = School::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = '组织架构管理';

    protected static ?string $navigationLabel = '学校管理';

    protected static ?int $navigationSort = 2;

    protected static ?int $navigationGroupSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        TextInput::make('name')
                            ->label('学校名称')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('请输入学校名称'),
                        
                        Select::make('school_type')
                            ->label('学校类型')
                            ->options(School::getSchoolTypeOptions())
                            ->required()
                            ->reactive(),
                        
                        Select::make('grade_type')
                            ->label('年级类型')
                            ->options(School::getGradeTypeOptions())
                            ->required(),
                        
                        Select::make('education_system')
                            ->label('学制')
                            ->options(School::getEducationSystemOptions())
                            ->required(),
                        
                        Select::make('school_nature')
                            ->label('学校性质')
                            ->options(School::getSchoolNatureOptions())
                            ->required(),
                        
                        Select::make('binding_status')
                            ->label('组织绑定状态')
                            ->options(School::getBindingStatusOptions())
                            ->default('unbound')
                            ->required(),
                    ])
                    ->columns(2),
                    
                Section::make('地区信息')
                    ->schema([
                        Select::make('province_code')
                            ->label('省份')
                            ->options(self::getProvinceOptions())
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('city_code', null);
                                $set('district_code', null);
                                $set('town_code', null);
                                if ($state) {
                                    $province = self::getProvinceByCode($state);
                                    $set('province_name', $province['name'] ?? null);
                                }
                            }),
                        
                        Select::make('city_code')
                            ->label('城市')
                            ->options(function (callable $get) {
                                $provinceCode = $get('province_code');
                                return $provinceCode ? self::getCityOptions($provinceCode) : [];
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $set('district_code', null);
                                $set('town_code', null);
                                if ($state) {
                                    $city = self::getCityByCode($get('province_code'), $state);
                                    $set('city_name', $city['name'] ?? null);
                                }
                            }),
                        
                        Select::make('district_code')
                            ->label('区县')
                            ->options(function (callable $get) {
                                $cityCode = $get('city_code');
                                return $cityCode ? self::getDistrictOptions($cityCode) : [];
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $set('town_code', null);
                                if ($state) {
                                    $district = self::getDistrictByCode($get('city_code'), $state);
                                    $set('district_name', $district['name'] ?? null);
                                }
                            }),
                        
                        Select::make('town_code')
                            ->label('乡镇')
                            ->options(function (callable $get) {
                                $districtCode = $get('district_code');
                                return $districtCode ? self::getTownOptions($districtCode) : [];
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                if ($state) {
                                    $town = self::getTownByCode($get('district_code'), $state);
                                    $set('town_name', $town['name'] ?? null);
                                }
                            }),
                    ])
                    ->columns(2),
                
                Section::make('组织绑定')
                    ->schema([
                        SelectTree::make('administrative_institution_id')
                            ->label('关联行政机构')
                            ->relationship(
                                'administrativeInstitution', 
                                'name', 
                                'parent_id',
                                fn ($query) => $query->whereIn('administrative_level', ['province', 'city', 'district'])->limit(1000)
                            )
                            ->searchable()
                            ->placeholder('选择关联的行政机构')
                            ->helperText('选择与此学校关联的行政机构，支持层级选择（限制到区县级）')
                            ->enableBranchNode()
                            ->defaultOpenLevel(1)
                            ->withCount(false)
                    ])
                    ->visible(fn (callable $get) => $get('binding_status') === 'bound'),
                
                Section::make('联系信息')
                    ->schema([
                        TextInput::make('contact_person')
                            ->label('联系人')
                            ->maxLength(100),
                        
                        TextInput::make('contact_phone')
                            ->label('联系电话')
                            ->tel()
                            ->maxLength(20),
                        
                        TextInput::make('contact_email')
                            ->label('联系邮箱')
                            ->email()
                            ->maxLength(100),
                        
                        Textarea::make('address')
                            ->label('详细地址')
                            ->rows(2)
                            ->columnSpanFull(),
                        
                        Textarea::make('description')
                            ->label('备注说明')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                
                Section::make('系统设置')
                    ->schema([
                        TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        
                        Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('学校名称')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('medium'),
                
                TextColumn::make('full_region_name')
                    ->label('所在地区')
                    ->searchable(['province_name', 'city_name', 'district_name', 'town_name'])
                    ->toggleable()
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 30 ? $state : null;
                    }),
                
                BadgeColumn::make('school_type')
                    ->label('学校类型')
                    ->formatStateUsing(fn (string $state): string => School::getSchoolTypeOptions()[$state] ?? $state)
                    ->colors([
                        'primary' => 'kindergarten',
                        'success' => 'primary_secondary',
                    ])
                    ->sortable(),
                
                BadgeColumn::make('grade_type')
                    ->label('年级类型')
                    ->formatStateUsing(fn (string $state): string => School::getGradeTypeOptions()[$state] ?? $state)
                    ->colors([
                        'warning' => 'kindergarten',
                        'info' => ['primary_nine_year', 'junior_middle'],
                        'success' => ['complete_middle', 'senior_middle'],
                        'primary' => ['twelve_year', 'all_stages'],
                    ])
                    ->toggleable(),
                
                BadgeColumn::make('school_nature')
                    ->label('学校性质')
                    ->formatStateUsing(fn (string $state): string => School::getSchoolNatureOptions()[$state] ?? $state)
                    ->colors([
                        'success' => 'public',
                        'warning' => 'private',
                    ])
                    ->sortable(),
                
                BadgeColumn::make('binding_status')
                    ->label('绑定状态')
                    ->formatStateUsing(fn (string $state): string => School::getBindingStatusOptions()[$state] ?? $state)
                    ->colors([
                        'success' => 'bound',
                        'danger' => 'unbound',
                    ])
                    ->sortable(),
                
                TextColumn::make('administrativeInstitution.name')
                    ->label('关联机构')
                    ->toggleable()
                    ->placeholder('未绑定')
                    ->limit(20),
                
                TextColumn::make('contact_person')
                    ->label('联系人')
                    ->toggleable()
                    ->placeholder('未设置'),
                
                BadgeColumn::make('is_active')
                    ->label('状态')
                    ->formatStateUsing(fn (bool $state): string => $state ? '启用' : '禁用')
                    ->colors([
                        'success' => true,
                        'danger' => false,
                    ]),
                
                TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                CascadingRegionFilter::make('school_region')
                    ->label('地区筛选'),
                
                SelectFilter::make('school_type')
                    ->label('学校类型')
                    ->options(School::getSchoolTypeOptions()),
                
                SelectFilter::make('grade_type')
                    ->label('年级类型')
                    ->options(School::getGradeTypeOptions()),
                
                SelectFilter::make('school_nature')
                    ->label('学校性质')
                    ->options(School::getSchoolNatureOptions()),
                
                SelectFilter::make('binding_status')
                    ->label('绑定状态')
                    ->options(School::getBindingStatusOptions()),
                
                SelectFilter::make('is_active')
                    ->label('启用状态')
                    ->options([
                        1 => '启用',
                        0 => '禁用',
                    ]),
                
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->striped()
            ->poll('60s')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSchools::route('/'),
            'create' => Pages\CreateSchool::route('/create'),
            'view' => Pages\ViewSchool::route('/{record}'),
            'edit' => Pages\EditSchool::route('/{record}/edit'),
        ];
    }

    /**
     * 获取省份选项
     */
    public static function getProvinceOptions(): array
    {
        return AdministrativeInstitution::select(['name', 'province_code'])
            ->where('administrative_level', 'province')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(100)
            ->pluck('name', 'province_code')
            ->toArray();
    }

    /**
     * 获取城市选项
     */
    public static function getCityOptions(string $provinceCode): array
    {
        if (empty($provinceCode)) {
            return [];
        }
        
        return AdministrativeInstitution::select(['name', 'city_code'])
            ->where('administrative_level', 'city')
            ->where('province_code', $provinceCode)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(200)
            ->pluck('name', 'city_code')
            ->toArray();
    }

    /**
     * 获取区县选项
     */
    public static function getDistrictOptions(string $cityCode): array
    {
        if (empty($cityCode)) {
            return [];
        }
        
        return AdministrativeInstitution::select(['name', 'district_code'])
            ->where('administrative_level', 'district')
            ->where('city_code', $cityCode)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(300)
            ->pluck('name', 'district_code')
            ->toArray();
    }

    /**
     * 获取乡镇选项
     */
    public static function getTownOptions(string $districtCode): array
    {
        if (empty($districtCode)) {
            return [];
        }
        
        return AdministrativeInstitution::select(['name', 'town_code'])
            ->where('administrative_level', 'town')
            ->where('district_code', $districtCode)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(1000)
            ->pluck('name', 'town_code')
            ->toArray();
    }

    /**
     * 根据代码获取省份信息
     */
    public static function getProvinceByCode(string $code): ?array
    {
        $province = AdministrativeInstitution::where('province_code', $code)
            ->where('administrative_level', 'province')
            ->whereNotNull('name')
            ->first();
        
        return $province ? ['name' => $province->name, 'code' => $province->province_code] : null;
    }

    /**
     * 根据代码获取城市信息
     */
    public static function getCityByCode(string $provinceCode, string $cityCode): ?array
    {
        $city = AdministrativeInstitution::where('province_code', $provinceCode)
            ->where('city_code', $cityCode)
            ->where('administrative_level', 'city')
            ->whereNotNull('name')
            ->first();
        
        return $city ? ['name' => $city->name, 'code' => $city->city_code] : null;
    }

    /**
     * 根据代码获取区县信息
     */
    public static function getDistrictByCode(string $cityCode, string $districtCode): ?array
    {
        $district = AdministrativeInstitution::where('city_code', $cityCode)
            ->where('district_code', $districtCode)
            ->where('administrative_level', 'district')
            ->whereNotNull('name')
            ->first();
        
        return $district ? ['name' => $district->name, 'code' => $district->district_code] : null;
    }

    /**
     * 根据代码获取乡镇信息
     */
    public static function getTownByCode(string $districtCode, string $townCode): ?array
    {
        $town = AdministrativeInstitution::where('district_code', $districtCode)
            ->where('town_code', $townCode)
            ->where('administrative_level', 'town')
            ->whereNotNull('name')
            ->first();
        
        return $town ? ['name' => $town->name, 'code' => $town->town_code] : null;
    }
}