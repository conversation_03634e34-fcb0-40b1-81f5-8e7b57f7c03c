<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use App\Models\Setting;

class SiteSetting extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $navigationGroup = '系统管理';
    protected static ?string $navigationLabel = '系统设置';
    protected static ?int $navigationSort = 0;
    protected static ?int $navigationGroupSort = 4;
    protected static string $view = 'filament.pages.site-setting';

    public array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'site_name' => Setting::getValue('site_name', ''),
            'site_url' => Setting::getValue('site_url', ''),
            'company_name' => Setting::getValue('company_name', ''),
            'icp' => Setting::getValue('icp', ''),
            'copyright' => Setting::getValue('copyright', ''),
            'seo_keywords' => Setting::getValue('seo_keywords', ''),
            'seo_description' => Setting::getValue('seo_description', ''),
            'upload_path' => Setting::getValue('upload_path', '/uploads'),
            'logo_url' => Setting::getValue('logo_url', ''),
            'theme_default' => Setting::getValue('theme_default', 'default'),
            'ad_enabled' => Setting::getValue('ad_enabled', false),
            'float_window_enabled' => Setting::getValue('float_window_enabled', false),
            'tongji_code' => Setting::getValue('tongji_code', ''),
            'forbidden_words' => Setting::getValue('forbidden_words', ''),
            'replace_words' => Setting::getValue('replace_words', ''),
            'logout_timeout' => Setting::getValue('logout_timeout', 1800),
            'doc_log_enabled' => Setting::getValue('doc_log_enabled', false),
            'user_lock_fail_count' => Setting::getValue('user_lock_fail_count', 5),
            'user_lock_time' => Setting::getValue('user_lock_time', 3600),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('站点信息')
                    ->schema([
                        FileUpload::make('logo_url')
                            ->label('网站LOGO')
                            ->image()
                            ->avatar()
                            ->imagePreviewHeight('80')
                            ->columnSpanFull(),
                        TextInput::make('site_name')->label('站点名称')->required(),
                        TextInput::make('site_url')->label('站点根网址')->required(),
                        TextInput::make('company_name')->label('公司名称'),
                        TextInput::make('icp')->label('备案号'),
                        Textarea::make('copyright')->label('版权信息')->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->extraAttributes(['style' => 'margin-bottom:2.5rem']),
                Section::make('SEO 配置')
                    ->schema([
                        TextInput::make('seo_keywords')->label('站点关键词'),
                        Textarea::make('seo_description')->label('站点描述')->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->extraAttributes(['style' => 'margin-bottom:2.5rem']),
                Section::make('上传/风格/广告')
                    ->schema([
                        TextInput::make('upload_path')->label('图片上传目录')->default('/uploads'),
                        TextInput::make('theme_default')->label('默认模板风格')->default('default'),
                        Toggle::make('ad_enabled')->label('广告开关'),
                        Toggle::make('float_window_enabled')->label('首页飘窗开关'),
                    ])
                    ->columns(2)
                    ->extraAttributes(['style' => 'margin-bottom:2.5rem']),
                Section::make('统计与代码')
                    ->schema([
                        Textarea::make('tongji_code')->label('站长统计代码')->columnSpanFull(),
                    ])
                    ->columns(1)
                    ->extraAttributes(['style' => 'margin-bottom:2.5rem']),
                Section::make('内容安全')
                    ->schema([
                        Textarea::make('forbidden_words')->label('禁用词语')->helperText('用|分开'),
                        Textarea::make('replace_words')->label('替换词语')->helperText('用|分开'),
                    ])
                    ->columns(2)
                    ->extraAttributes(['style' => 'margin-bottom:2.5rem']),
                Section::make('登录与安全')
                    ->schema([
                        TextInput::make('logout_timeout')->label('超时退出登录(秒)')->numeric(),
                        Toggle::make('doc_log_enabled')->label('开启文档日志'),
                        TextInput::make('user_lock_fail_count')->label('用户锁定失败次数')->numeric(),
                        TextInput::make('user_lock_time')->label('用户锁定时间(秒)')->numeric(),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->data;
        if (isset($data['logo_url']) && is_array($data['logo_url'])) {
            $data['logo_url'] = $data['logo_url'][0] ?? '';
        }
        foreach ($data as $key => $value) {
            Setting::setValue($key, $value);
        }
        Notification::make()
            ->title('保存成功')
            ->success()
            ->send();
    }
}