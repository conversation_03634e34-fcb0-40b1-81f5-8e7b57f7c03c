<?php

namespace App\Database\Migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('post_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('parent_id')->nullable()->constrained('comments')->nullOnDelete();
            $table->foreignId('article_id')->nullable()->constrained('articles')->nullOnDelete();
            $table->string('author_name')->nullable(); // 未登录用户显示名称
            $table->string('author_email')->nullable(); // 未登录用户邮箱
            $table->string('author_ip', 45)->nullable(); // 评论者IP
            $table->text('content');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->boolean('is_private')->default(false); // 私密评论
            $table->json('meta_data')->nullable(); // 存储额外信息
            $table->unsignedInteger('_lft')->default(0); // 用于树形结构
            $table->unsignedInteger('_rgt')->default(0);
            $table->unsignedInteger('depth')->default(0);
            $table->timestamps();
            $table->softDeletes();

            // 添加索引
            $table->index(['post_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index('parent_id');
            $table->index(['_lft', '_rgt', 'depth']);
        });

        // 创建评论通知表
        Schema::create('comment_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('comment_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('type'); // reply, mention, approval
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            // 添加索引
            $table->index(['user_id', 'is_read']);
            $table->index('type');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('comment_notifications');
        Schema::dropIfExists('comments');
    }
}; 