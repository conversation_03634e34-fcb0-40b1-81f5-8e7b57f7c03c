<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class InitRolesAndPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:roles-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化角色和权限';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始初始化角色和权限...');

        // 创建权限
        $permissions = [
            // 系统管理权限
            'system.view' => '查看系统信息',
            'system.manage' => '管理系统设置',
            'system.audit' => '系统审计',
            
            // 用户管理权限
            'users.view' => '查看用户',
            'users.create' => '创建用户',
            'users.edit' => '编辑用户',
            'users.delete' => '删除用户',
            
            // 角色管理权限
            'roles.view' => '查看角色',
            'roles.create' => '创建角色',
            'roles.edit' => '编辑角色',
            'roles.delete' => '删除角色',
            
            // 内容管理权限
            'posts.view' => '查看文章',
            'posts.create' => '创建文章',
            'posts.edit' => '编辑文章',
            'posts.delete' => '删除文章',
            
            // 分类管理权限
            'categories.view' => '查看分类',
            'categories.create' => '创建分类',
            'categories.edit' => '编辑分类',
            'categories.delete' => '删除分类',
            
            // 媒体管理权限
            'media.view' => '查看媒体',
            'media.upload' => '上传媒体',
            'media.edit' => '编辑媒体',
            'media.delete' => '删除媒体',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(['name' => $name], [
                'name' => $name,
                'guard_name' => 'web',
            ]);
            $this->info("创建权限: {$name}");
        }

        // 创建角色并分配权限
        $roles = [
            'super-admin' => [
                'name' => '超级管理员',
                'permissions' => array_keys($permissions),
            ],
            'admin' => [
                'name' => '系统管理员',
                'permissions' => [
                    'system.view',
                    'users.view', 'users.create', 'users.edit',
                    'roles.view',
                    'posts.view', 'posts.create', 'posts.edit', 'posts.delete',
                    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
                    'media.view', 'media.upload', 'media.edit', 'media.delete',
                ],
            ],
            'auditor' => [
                'name' => '审计员',
                'permissions' => [
                    'system.view',
                    'system.audit',
                    'users.view',
                    'roles.view',
                    'posts.view',
                    'categories.view',
                    'media.view',
                ],
            ],
            'user' => [
                'name' => '普通用户',
                'permissions' => [
                    'posts.view',
                    'categories.view',
                    'media.view',
                ],
            ],
        ];

        foreach ($roles as $roleName => $roleData) {
            $role = Role::firstOrCreate(['name' => $roleName], [
                'name' => $roleName,
                'guard_name' => 'web',
            ]);
            
            $role->syncPermissions($roleData['permissions']);
            $this->info("创建角色: {$roleData['name']}");
        }

        $this->info('角色和权限初始化完成！');
    }
}
