<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Models\Activity;

echo "=== 测试真实SMS流程 ===\n";

try {
    // 获取一个测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到注册记录\n";
        exit;
    }
    
    echo "注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    
    $activityDetail = $registration->activityDetail;
    $activity = $activityDetail->activity;
    
    echo "\n=== 活动详情信息 ===\n";
    echo "ActivityDetail ID: {$activityDetail->id}\n";
    echo "Activity ID: {$activity->id}\n";
    echo "Activity Title: {$activity->title}\n";
    
    // 测试字段访问
    echo "\n=== 字段访问测试 ===\n";
    
    // 时间字段
    echo "activity_time: ";
    if (isset($activityDetail->activity_time)) {
        echo "'{$activityDetail->activity_time}'\n";
        $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
        echo "格式化时间: '{$time}'\n";
    } else {
        echo "字段不存在或为空\n";
    }
    
    // 目标对象字段
    echo "\ntarget_audience: ";
    if (isset($activityDetail->target_audience)) {
        echo "'{$activityDetail->target_audience}'\n";
    } else {
        echo "字段不存在或为空\n";
    }
    
    echo "target: ";
    if (isset($activityDetail->target)) {
        echo "'{$activityDetail->target}'\n";
    } else {
        echo "字段不存在或为空\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
