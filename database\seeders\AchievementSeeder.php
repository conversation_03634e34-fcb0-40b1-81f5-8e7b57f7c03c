<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AchievementCategory;
use App\Models\User;
use App\Models\UserPoint;
use App\Models\UserBehavior;
use Carbon\Carbon;

class AchievementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建成就分类
        $achievementCenter = AchievementCategory::firstOrCreate(
            ['slug' => 'achievement-center'],
            [
                'name' => '成就中心',
                'description' => '用于展示用户个人成就情况',
                'icon' => 'heroicon-o-star',
                'sort_order' => 1,
                'is_active' => true,
            ]
        );

        $pointCenter = AchievementCategory::firstOrCreate(
            ['slug' => 'point-center'],
            [
                'name' => '积分中心',
                'description' => '用于展示地区维度的积分排行情况',
                'icon' => 'heroicon-o-chart-bar',
                'sort_order' => 2,
                'is_active' => true,
            ]
        );

        // 创建测试用户数据
        $regions = [
            '上海市嘉定区实验小学',
            '上海市嘉定区南苑小学',
            '上海市嘉定区实验中学',
            '上海市浦东新区实验小学',
            '上海市黄浦区第一小学',
        ];

        $schools = [
            '实验小学',
            '南苑小学',
            '实验中学',
            '第一小学',
            '第二小学',
        ];

        // 更新现有用户或创建新用户
        for ($i = 1; $i <= 20; $i++) {
            $user = User::find($i);
            if (!$user) {
                $user = User::create([
                    'name' => "user{$i}",
                    'email' => "user{$i}@example.com",
                    'password' => bcrypt('password'),
                    'is_active' => true,
                ]);
            }

            // 更新用户实名信息
            $region = $regions[array_rand($regions)];
            $school = $schools[array_rand($schools)];

            $user->update([
                'real_name' => "教师{$i}",
                'region' => $region,
                'school' => $school,
                'is_verified' => true,
                'phone' => '138' . str_pad($i, 8, '0', STR_PAD_LEFT),
            ]);

            // 创建积分记录
            $totalPoints = rand(20, 100);
            $pointsDistribution = $this->distributePoints($totalPoints);

            foreach ($pointsDistribution as $date => $dayPoints) {
                foreach ($dayPoints as $pointRecord) {
                    UserPoint::create([
                        'user_id' => $user->id,
                        'points' => $pointRecord['points'],
                        'type' => $pointRecord['type'],
                        'source' => $pointRecord['source'],
                        'source_id' => $pointRecord['source_id'],
                        'earned_date' => $date,
                    ]);

                    // 创建对应的行为记录
                    UserBehavior::create([
                        'user_id' => $user->id,
                        'action' => $this->getActionFromType($pointRecord['type']),
                        'target_type' => $pointRecord['type'] === 'video' ? 'video' : 'post',
                        'target_id' => $pointRecord['source_id'],
                        'duration' => $pointRecord['type'] === 'browse' ? rand(30, 300) : null,
                        'behavior_date' => $date,
                        'ip_address' => '192.168.1.' . rand(1, 254),
                        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    ]);
                }
            }
        }
    }

    /**
     * 分配积分到不同日期
     */
    private function distributePoints($totalPoints)
    {
        $distribution = [];
        $remainingPoints = $totalPoints;
        $daysBack = rand(30, 90); // 30-90天的历史数据

        for ($i = 0; $i < $daysBack && $remainingPoints > 0; $i++) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $dayPoints = [];

            // 随机决定这一天是否有积分
            if (rand(1, 3) === 1) { // 1/3的概率有积分
                $pointsToday = min($remainingPoints, rand(1, 10));
                $remainingPoints -= $pointsToday;

                // 分配积分类型
                while ($pointsToday > 0) {
                    if ($pointsToday >= 5 && rand(1, 3) === 1) {
                        // 视频积分
                        $dayPoints[] = [
                            'points' => 5,
                            'type' => 'video',
                            'source' => '观看视频内容',
                            'source_id' => 'video_' . rand(1, 100),
                        ];
                        $pointsToday -= 5;
                    } else {
                        // 浏览积分
                        $dayPoints[] = [
                            'points' => 1,
                            'type' => 'browse',
                            'source' => '浏览页面超过30秒',
                            'source_id' => 'post_' . rand(1, 100),
                        ];
                        $pointsToday -= 1;
                    }
                }
            }

            if (!empty($dayPoints)) {
                $distribution[$date] = $dayPoints;
            }
        }

        return $distribution;
    }

    /**
     * 根据积分类型获取行为类型
     */
    private function getActionFromType($type)
    {
        switch ($type) {
            case 'video':
                return 'video_view';
            case 'browse':
                return 'browse';
            default:
                return 'browse';
        }
    }
}
