<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserPoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'points',
        'type',
        'source',
        'source_id',
        'earned_date',
    ];

    protected $casts = [
        'earned_date' => 'date',
        'points' => 'integer',
    ];

    /**
     * 用户关联
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 积分类型常量
     */
    const TYPE_LOGIN = 'login';
    const TYPE_BROWSE = 'browse';
    const TYPE_VIDEO = 'video';

    /**
     * 获取积分类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_LOGIN => '登录积分',
            self::TYPE_BROWSE => '浏览积分',
            self::TYPE_VIDEO => '视频积分',
        ];
    }

    /**
     * 获取积分类型标签
     */
    public function getTypeLabel()
    {
        return self::getTypeOptions()[$this->type] ?? $this->type;
    }
}
