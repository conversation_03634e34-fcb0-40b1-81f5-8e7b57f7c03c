<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\CollectWechatArticleJob;
use App\Jobs\ConvertWechatArticleJob;
use App\Models\WechatAccount;
use App\Models\WechatArticle;
use App\Services\WechatArticleCollectService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WechatCollectController extends Controller
{
    public function __construct(
        private WechatArticleCollectService $collectService
    ) {}

    /**
     * 采集指定公众号的文章
     */
    public function collectFromAccount(Request $request, WechatAccount $account): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'count' => 'integer|min:1|max:50',
            'async' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $count = $request->input('count', 20);
        $async = $request->input('async', true);

        try {
            if ($async) {
                // 异步采集
                CollectWechatArticleJob::dispatch($account, $count);
                
                return response()->json([
                    'success' => true,
                    'message' => '采集任务已提交，请稍后查看结果',
                    'data' => [
                        'account_id' => $account->id,
                        'count' => $count,
                        'async' => true
                    ]
                ]);
            } else {
                // 同步采集
                $articles = $this->collectService->collectFromAccount($account, $count);
                
                return response()->json([
                    'success' => true,
                    'message' => '采集完成',
                    'data' => [
                        'account_id' => $account->id,
                        'collected_count' => count($articles),
                        'articles' => $articles->map(fn($article) => [
                            'id' => $article->id,
                            'title' => $article->title,
                            'digest' => $article->digest,
                            'collected_at' => $article->collected_at
                        ])
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('API采集微信文章失败', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '采集失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 从URL采集文章
     */
    public function collectFromUrl(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'url' => 'required|url',
            'account_id' => 'nullable|exists:wechat_accounts,id',
            'async' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $url = $request->input('url');
        $accountId = $request->input('account_id');
        $async = $request->input('async', false);

        try {
            $account = $accountId ? WechatAccount::find($accountId) : null;
            $article = $this->collectService->collectFromUrl($url, $account);
            
            return response()->json([
                'success' => true,
                'message' => '采集完成',
                'data' => [
                    'article_id' => $article->id,
                    'title' => $article->title,
                    'digest' => $article->digest,
                    'url' => $article->url,
                    'collected_at' => $article->collected_at
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('API从URL采集微信文章失败', [
                'url' => $url,
                'account_id' => $accountId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '采集失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 转换微信文章为本地文章
     */
    public function convertToArticle(Request $request, WechatArticle $wechatArticle): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'nullable|exists:categories,id',
            'async' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        if ($wechatArticle->article_id) {
            return response()->json([
                'success' => false,
                'message' => '该微信文章已经转换过了',
                'data' => [
                    'article_id' => $wechatArticle->article_id
                ]
            ], 400);
        }

        $categoryId = $request->input('category_id');
        $async = $request->input('async', true);

        try {
            if ($async) {
                // 异步转换
                ConvertWechatArticleJob::dispatch($wechatArticle, $categoryId);
                
                return response()->json([
                    'success' => true,
                    'message' => '转换任务已提交，请稍后查看结果',
                    'data' => [
                        'wechat_article_id' => $wechatArticle->id,
                        'category_id' => $categoryId,
                        'async' => true
                    ]
                ]);
            } else {
                // 同步转换
                $article = $this->collectService->convertToArticle($wechatArticle, $categoryId);
                
                return response()->json([
                    'success' => true,
                    'message' => '转换完成',
                    'data' => [
                        'wechat_article_id' => $wechatArticle->id,
                        'article_id' => $article->id,
                        'title' => $article->title
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('API转换微信文章失败', [
                'wechat_article_id' => $wechatArticle->id,
                'category_id' => $categoryId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '转换失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取采集统计信息
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = [
                'accounts' => [
                    'total' => WechatAccount::count(),
                    'active' => WechatAccount::where('is_active', true)->count(),
                    'auto_collect' => WechatAccount::where('auto_collect', true)->count()
                ],
                'articles' => [
                    'total' => WechatArticle::count(),
                    'pending' => WechatArticle::where('status', 'pending')->count(),
                    'processed' => WechatArticle::where('status', 'processed')->count(),
                    'published' => WechatArticle::where('status', 'published')->count(),
                    'failed' => WechatArticle::where('status', 'failed')->count(),
                    'converted' => WechatArticle::whereNotNull('article_id')->count()
                ],
                'today' => [
                    'collected' => WechatArticle::whereDate('collected_at', today())->count(),
                    'converted' => WechatArticle::whereDate('updated_at', today())
                        ->whereNotNull('article_id')->count()
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('获取采集统计信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取统计信息失败：' . $e->getMessage()
            ], 500);
        }
    }
}