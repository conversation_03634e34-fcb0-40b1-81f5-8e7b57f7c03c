<?php

namespace App\Console\Commands;

use App\Models\AuditLog;
use App\Models\SystemSetting;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanAuditLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audit:clean {--days= : 保留天数，不指定则使用系统设置}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理过期的审计日志';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        
        // 如果没有指定天数，从系统设置中获取
        if (empty($days)) {
            $days = SystemSetting::getAuditLogRetentionDays();
            $this->info("使用系统设置的保留天数: {$days} 天");
        }
        
        if (!is_numeric($days) || $days < 1) {
            $this->error('保留天数必须是大于0的数字');
            return 1;
        }

        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("开始清理 {$cutoffDate->format('Y-m-d H:i:s')} 之前的审计日志...");
        
        $count = AuditLog::where('operated_at', '<', $cutoffDate)->count();
        
        if ($count === 0) {
            $this->info('没有需要清理的日志记录。');
            return 0;
        }
        
        if ($this->confirm("将删除 {$count} 条日志记录，是否继续？")) {
            $deleted = AuditLog::where('operated_at', '<', $cutoffDate)->delete();
            $this->info("成功删除 {$deleted} 条过期的审计日志。");
            
            // 记录清理操作
            AuditLog::create([
                'user_id' => null,
                'operated_at' => now(),
                'operation_type' => 'CLEANUP',
                'module' => '系统维护',
                'target_id' => null,
                'request_ip' => '127.0.0.1',
                'request_url' => 'console:audit:clean',
                'request_data' => json_encode(['days' => $days, 'deleted_count' => $deleted]),
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => "自动清理了 {$deleted} 条超过 {$days} 天的审计日志",
            ]);
        } else {
            $this->info('操作已取消。');
        }
        
        return 0;
    }
}