<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RestoreFromWorkingBackupSeeder extends Seeder
{
    /**
     * 从工作的备份文件中恢复数据
     */
    public function run(): void
    {
        $this->command->info('🔄 从工作备份中恢复数据...');

        $backupFile = 'storage/app/private/private/backups/每日自动备份_2025-05-27_10-23-17.sql';
        
        if (!file_exists($backupFile)) {
            $this->command->error("❌ 备份文件不存在: {$backupFile}");
            return;
        }

        // 清空现有数据
        $this->clearExistingData();

        // 从备份恢复数据
        $this->restoreFromBackup($backupFile);

        $this->command->info('✅ 数据恢复完成！');
    }

    /**
     * 清空现有数据
     */
    private function clearExistingData()
    {
        $this->command->info('🗑️ 清空现有数据...');

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $tables = [
            'cache',
            'model_has_permissions',
            'model_has_roles', 
            'role_has_permissions',
            'permissions',
            'roles',
            'users'
        ];

        foreach ($tables as $table) {
            DB::table($table)->truncate();
            $this->command->info("  ✅ 清空表: {$table}");
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * 从备份恢复数据
     */
    private function restoreFromBackup($backupFile)
    {
        $content = file_get_contents($backupFile);

        // 恢复cache表 - 这是关键！
        $this->restoreCache($content);
        
        // 恢复用户表
        $this->restoreUsers($content);
        
        // 创建基础权限数据
        $this->createBasicPermissionData();
    }

    /**
     * 恢复cache数据
     */
    private function restoreCache($content)
    {
        $this->command->info('🔑 恢复cache数据...');

        // 查找cache表的INSERT语句
        $pattern = '/INSERT INTO `cache` VALUES \((.*?)\);/s';
        if (preg_match($pattern, $content, $matches)) {
            $values = $matches[1];
            
            try {
                $sql = "INSERT INTO `cache` VALUES ({$values})";
                DB::statement($sql);
                $this->command->info("  ✅ 恢复cache数据成功");
            } catch (\Exception $e) {
                $this->command->error("  ❌ 恢复cache数据失败: " . $e->getMessage());
            }
        } else {
            $this->command->warn("  ⚠️ 未找到cache数据");
        }
    }

    /**
     * 恢复用户数据
     */
    private function restoreUsers($content)
    {
        $this->command->info('👥 恢复用户数据...');

        // 查找users表的INSERT语句
        $pattern = '/INSERT INTO `users` VALUES \((.*?)\);/s';
        if (preg_match($pattern, $content, $matches)) {
            $values = $matches[1];
            
            try {
                $sql = "INSERT INTO `users` VALUES ({$values})";
                DB::statement($sql);
                $this->command->info("  ✅ 恢复用户数据成功");
            } catch (\Exception $e) {
                $this->command->error("  ❌ 恢复用户数据失败: " . $e->getMessage());
                // 如果失败，创建基础管理员
                $this->createBasicAdmin();
            }
        } else {
            $this->command->warn("  ⚠️ 未找到用户数据，创建基础管理员");
            $this->createBasicAdmin();
        }
    }

    /**
     * 创建基础管理员
     */
    private function createBasicAdmin()
    {
        $admins = [
            [
                'id' => 21,
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'is_active' => 1,
                'is_verified' => 1,
                'real_name' => '超级管理员',
                'region' => '系统管理',
                'school' => '管理中心',
                'phone' => '13800000000',
                'avatar' => null,
                'email_verified_at' => null,
                'remember_token' => null,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 22,
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'is_active' => 1,
                'is_verified' => 1,
                'real_name' => '管理员',
                'region' => '系统管理', 
                'school' => '管理中心',
                'phone' => '13800000001',
                'avatar' => null,
                'email_verified_at' => null,
                'remember_token' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($admins as $admin) {
            DB::table('users')->insert($admin);
        }

        $this->command->info("  ✅ 创建基础管理员用户");
    }

    /**
     * 创建基础权限数据
     */
    private function createBasicPermissionData()
    {
        $this->command->info('🔐 创建基础权限数据...');

        // 创建角色
        $roles = [
            ['id' => 1, 'name' => 'admin', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'name' => 'super_admin', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()]
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insert($role);
        }

        // 创建基础权限
        $permissions = [
            'view_any_user', 'view_user', 'create_user', 'update_user', 'delete_user',
            'view_any_role', 'view_role', 'create_role', 'update_role', 'delete_role',
            'view_any_article', 'view_article', 'create_article', 'update_article', 'delete_article'
        ];

        $id = 1;
        foreach ($permissions as $permission) {
            DB::table('permissions')->insert([
                'id' => $id++,
                'name' => $permission,
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        // 给角色分配权限
        for ($i = 1; $i <= count($permissions); $i++) {
            DB::table('role_has_permissions')->insert([
                'permission_id' => $i,
                'role_id' => 1 // admin
            ]);
            DB::table('role_has_permissions')->insert([
                'permission_id' => $i,
                'role_id' => 2 // super_admin
            ]);
        }

        // 给用户分配角色
        DB::table('model_has_roles')->insert([
            ['role_id' => 2, 'model_type' => 'App\\Models\\User', 'model_id' => 21], // super_admin
            ['role_id' => 1, 'model_type' => 'App\\Models\\User', 'model_id' => 22]  // admin
        ]);

        $this->command->info("  ✅ 创建基础权限数据完成");
    }
}
