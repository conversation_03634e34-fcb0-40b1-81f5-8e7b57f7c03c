@if($floatingImages->count() > 0)
<div id="floating-window" class="floating-window">
    @foreach($floatingImages as $image)
    <div class="floating-item" data-url="{{ $image->link_url }}" data-target="{{ $image->link_target }}">
        <img src="{{ $image->getImageUrl() }}" 
             alt="{{ $image->alt_text ?: $image->title }}"
             title="{{ $image->title }}"
             style="width: 128px; height: 95px; object-fit: cover;">
        <button class="close-btn" onclick="closeFloatingWindow()">&times;</button>
    </div>
    @endforeach
</div>

<style>
.floating-window {
    position: fixed;
    top: 20%;
    right: 20px;
    z-index: 9999;
    animation: float 3s ease-in-out infinite;
    cursor: pointer;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    overflow: hidden;
}

.floating-item {
    position: relative;
    display: block;
}

.floating-item img {
    display: block;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.floating-item:hover img {
    transform: scale(1.05);
}

.close-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@media (max-width: 768px) {
    .floating-window {
        right: 10px;
        top: 15%;
    }
    .floating-item img {
        width: 100px;
        height: 74px;
    }
}
</style>

<script>
function closeFloatingWindow() {
    document.getElementById('floating-window').style.display = 'none';
    localStorage.setItem('floating_window_closed', Date.now());
}

// 点击图片跳转
document.addEventListener('DOMContentLoaded', function() {
    const floatingItems = document.querySelectorAll('.floating-item');
    
    floatingItems.forEach(item => {
        item.addEventListener('click', function(e) {
            if (e.target.classList.contains('close-btn')) return;
            
            const url = this.dataset.url;
            const target = this.dataset.target;
            
            if (url) {
                if (target === '_blank') {
                    window.open(url, '_blank');
                } else {
                    window.location.href = url;
                }
            }
        });
    });
    
    // 检查是否已关闭（24小时内不再显示）
    const closedTime = localStorage.getItem('floating_window_closed');
    if (closedTime && (Date.now() - closedTime < 24 * 60 * 60 * 1000)) {
        document.getElementById('floating-window').style.display = 'none';
    }
});
</script>
@endif