<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;

class School extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'province_code',
        'province_name',
        'city_code',
        'city_name',
        'district_code',
        'district_name',
        'town_code',
        'town_name',
        'full_region_name',
        'school_type',
        'grade_type',
        'education_system',
        'school_nature',
        'binding_status',
        'administrative_institution_id',
        'contact_person',
        'contact_phone',
        'contact_email',
        'address',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 关联行政机构
     */
    public function administrativeInstitution(): BelongsTo
    {
        return $this->belongsTo(AdministrativeInstitution::class);
    }

    /**
     * 获取完整地区名称
     */
    public function getFullRegionNameAttribute(): string
    {
        if ($this->attributes['full_region_name']) {
            return $this->attributes['full_region_name'];
        }
        
        $parts = array_filter([
            $this->province_name,
            $this->city_name,
            $this->district_name,
            $this->town_name,
        ]);
        
        return implode(' - ', $parts);
    }

    /**
     * 获取学校类型中文名称
     */
    public function getSchoolTypeNameAttribute(): string
    {
        return match($this->school_type) {
            'kindergarten' => '幼儿园',
            'primary_secondary' => '中小学',
            default => '未知',
        };
    }

    /**
     * 获取年级类型中文名称
     */
    public function getGradeTypeNameAttribute(): string
    {
        return match($this->grade_type) {
            'kindergarten' => '幼儿园',
            'primary_nine_year' => '小学九年一贯制',
            'junior_middle' => '初级中学',
            'complete_middle' => '完全中学',
            'senior_middle' => '高级中学',
            'twelve_year' => '十二年一贯制',
            'all_stages' => '全学段（幼儿园至高中）',
            default => '未知',
        };
    }

    /**
     * 获取学制中文名称
     */
    public function getEducationSystemNameAttribute(): string
    {
        return match($this->education_system) {
            'five_four' => '五四制',
            'six_three' => '六三制',
            default => '未知',
        };
    }

    /**
     * 获取学校性质中文名称
     */
    public function getSchoolNatureNameAttribute(): string
    {
        return match($this->school_nature) {
            'public' => '公办',
            'private' => '民办',
            default => '未知',
        };
    }

    /**
     * 获取绑定状态中文名称
     */
    public function getBindingStatusNameAttribute(): string
    {
        return match($this->binding_status) {
            'bound' => '已绑定',
            'unbound' => '未绑定',
            default => '未绑定',
        };
    }

    /**
     * 作用域：按学校类型筛选
     */
    public function scopeBySchoolType($query, string $type)
    {
        return $query->where('school_type', $type);
    }

    /**
     * 作用域：按年级类型筛选
     */
    public function scopeByGradeType($query, string $type)
    {
        return $query->where('grade_type', $type);
    }

    /**
     * 作用域：按学校性质筛选
     */
    public function scopeBySchoolNature($query, string $nature)
    {
        return $query->where('school_nature', $nature);
    }

    /**
     * 作用域：按绑定状态筛选
     */
    public function scopeByBindingStatus($query, string $status)
    {
        return $query->where('binding_status', $status);
    }

    /**
     * 作用域：按地区筛选
     */
    public function scopeByRegion($query, ?string $provinceCode = null, ?string $cityCode = null, ?string $districtCode = null, ?string $townCode = null)
    {
        if ($provinceCode) {
            $query->where('province_code', $provinceCode);
        }
        if ($cityCode) {
            $query->where('city_code', $cityCode);
        }
        if ($districtCode) {
            $query->where('district_code', $districtCode);
        }
        if ($townCode) {
            $query->where('town_code', $townCode);
        }
        
        return $query;
    }

    /**
     * 作用域：启用状态
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 获取学校类型选项
     */
    public static function getSchoolTypeOptions(): array
    {
        return [
            'kindergarten' => '幼儿园',
            'primary_secondary' => '中小学',
        ];
    }

    /**
     * 获取年级类型选项
     */
    public static function getGradeTypeOptions(): array
    {
        return [
            'kindergarten' => '幼儿园',
            'primary_nine_year' => '小学九年一贯制',
            'junior_middle' => '初级中学',
            'complete_middle' => '完全中学',
            'senior_middle' => '高级中学',
            'twelve_year' => '十二年一贯制',
            'all_stages' => '全学段（幼儿园至高中）',
        ];
    }

    /**
     * 获取学制选项
     */
    public static function getEducationSystemOptions(): array
    {
        return [
            'five_four' => '五四制',
            'six_three' => '六三制',
        ];
    }

    /**
     * 获取学校性质选项
     */
    public static function getSchoolNatureOptions(): array
    {
        return [
            'public' => '公办',
            'private' => '民办',
        ];
    }

    /**
     * 获取绑定状态选项
     */
    public static function getBindingStatusOptions(): array
    {
        return [
            'bound' => '已绑定',
            'unbound' => '未绑定',
        ];
    }
}