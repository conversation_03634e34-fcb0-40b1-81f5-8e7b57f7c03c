<?php

namespace App\Exports;

use App\Models\Registration;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RegistrationOrganizationExport implements FromCollection, WithHeadings, WithTitle
{
    public function collection()
    {
        return Registration::select('organization', DB::raw('COUNT(*) as count'))
            ->whereNotNull('organization')
            ->where('status', true) // 只统计有效报名
            ->groupBy('organization')
            ->orderBy('count', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            '单位',
            '报名人数'
        ];
    }

    public function title(): string
    {
        return '单位分布';
    }
}