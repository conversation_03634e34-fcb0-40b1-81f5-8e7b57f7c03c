<?php

namespace App\Filament\Resources\AdministrativeInstitutionResource\Filters;

use App\Models\AdministrativeInstitution;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class CascadingRegionFilter extends Filter
{
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->form([
            Grid::make(4)
                ->schema([
                    Select::make('province_code')
                        ->label('省份')
                        ->placeholder('选择省份')
                        ->options($this->getProvinceOptions())
                        ->searchable()
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('city_code', null);
                            $set('district_code', null);
                            $set('town_code', null);
                        }),
                    
                    Select::make('city_code')
                        ->label('城市')
                        ->placeholder('选择城市')
                        ->options(function (callable $get) {
                            $provinceCode = $get('province_code');
                            return $provinceCode ? $this->getCityOptions($provinceCode) : [];
                        })
                        ->searchable()
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('district_code', null);
                            $set('town_code', null);
                        }),
                    
                    Select::make('district_code')
                        ->label('区县')
                        ->placeholder('选择区县')
                        ->options(function (callable $get) {
                            $cityCode = $get('city_code');
                            return $cityCode ? $this->getDistrictOptions($cityCode) : [];
                        })
                        ->searchable()
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('town_code', null);
                        }),
                    
                    Select::make('town_code')
                        ->label('乡镇')
                        ->placeholder('选择乡镇')
                        ->options(function (callable $get) {
                            $districtCode = $get('district_code');
                            return $districtCode ? $this->getTownOptions($districtCode) : [];
                        })
                        ->searchable(),
                ])
        ]);
    }
    
    public function apply(Builder $query, array $data = []): Builder
    {
        return $query
            ->when(
                $data['province_code'],
                fn (Builder $query, $value): Builder => $query->where('province_code', $value),
            )
            ->when(
                $data['city_code'],
                fn (Builder $query, $value): Builder => $query->where('city_code', $value),
            )
            ->when(
                $data['district_code'],
                fn (Builder $query, $value): Builder => $query->where('district_code', $value),
            )
            ->when(
                $data['town_code'],
                fn (Builder $query, $value): Builder => $query->where('town_code', $value),
            );
    }
    
    /**
     * 获取省份选项
     */
    private function getProvinceOptions(): array
    {
        return Cache::remember('admin_institutions_provinces', 300, function () {
            return AdministrativeInstitution::select(['name', 'province_code'])
                ->where('administrative_level', 'province')
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->pluck('name', 'province_code')
                ->toArray();
        });
    }
    
    /**
     * 获取城市选项
     */
    private function getCityOptions(string $provinceCode): array
    {
        return Cache::remember("admin_institutions_cities_{$provinceCode}", 300, function () use ($provinceCode) {
            return AdministrativeInstitution::select(['name', 'city_code'])
                ->where('administrative_level', 'city')
                ->where('province_code', $provinceCode)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->pluck('name', 'city_code')
                ->toArray();
        });
    }
    
    /**
     * 获取区县选项
     */
    private function getDistrictOptions(string $cityCode): array
    {
        return Cache::remember("admin_institutions_districts_{$cityCode}", 300, function () use ($cityCode) {
            return AdministrativeInstitution::select(['name', 'district_code'])
                ->where('administrative_level', 'district')
                ->where('city_code', $cityCode)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->pluck('name', 'district_code')
                ->toArray();
        });
    }
    
    /**
     * 获取乡镇选项
     */
    private function getTownOptions(string $districtCode): array
    {
        return Cache::remember("admin_institutions_towns_{$districtCode}", 300, function () use ($districtCode) {
            return AdministrativeInstitution::select(['name', 'town_code'])
                ->where('administrative_level', 'town')
                ->where('district_code', $districtCode)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->pluck('name', 'town_code')
                ->toArray();
        });
    }
}