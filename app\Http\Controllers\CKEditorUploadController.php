<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CKEditorUploadController extends Controller
{
    /**
     * Handle unified file upload for CKEditor
     */
    public function upload(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'upload' => [
                'required',
                'file',
                'max:' . config('filament-ckeditor-field.max_file_size', 51200),
                function ($attribute, $value, $fail) {
                    $acceptedTypes = config('filament-ckeditor-field.accepted_file_types', []);
                    if (!empty($acceptedTypes) && !in_array($value->getMimeType(), $acceptedTypes)) {
                        $fail('文件类型不被支持。支持的类型: ' . implode(', ', $acceptedTypes));
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => [
                    'message' => $validator->errors()->first('upload')
                ]
            ], 400);
        }

        try {
            $file = $request->file('upload');
            $disk = config('filament-ckeditor-field.upload_disk', 'public');
            $directory = config('filament-ckeditor-field.upload_directory', 'uploads/media');
            
            // Generate unique filename
            $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs($directory, $filename, $disk);
            
            if (!$path) {
                throw new \Exception('文件上传失败');
            }
            
            // Get the URL
            $url = Storage::disk($disk)->url($path);
            
            return response()->json([
                'url' => $url,
                'uploaded' => true,
                'fileName' => $file->getClientOriginalName(),
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => [
                    'message' => '上传失败: ' . $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Legacy image upload method
     */
    public function image(Request $request): JsonResponse
    {
        return $this->upload($request);
    }

    /**
     * Legacy file upload method
     */
    public function file(Request $request): JsonResponse
    {
        return $this->upload($request);
    }

    /**
     * Legacy video upload method
     */
    public function video(Request $request): JsonResponse
    {
        return $this->upload($request);
    }

    /**
     * Audio upload method
     */
    public function audio(Request $request): JsonResponse
    {
        return $this->upload($request);
    }

    /**
     * Browse uploaded files
     */
    public function browse(Request $request): JsonResponse
    {
        try {
            $disk = config('filament-ckeditor-field.upload_disk', 'public');
            $directory = config('filament-ckeditor-field.upload_directory', 'uploads/media');
            
            $files = Storage::disk($disk)->files($directory);
            $fileList = [];
            
            foreach ($files as $file) {
                $url = Storage::disk($disk)->url($file);
                $fileInfo = pathinfo($file);
                
                $fileList[] = [
                    'name' => $fileInfo['basename'],
                    'url' => $url,
                    'size' => Storage::disk($disk)->size($file),
                    'type' => $this->getFileType($fileInfo['extension'] ?? ''),
                    'modified' => Storage::disk($disk)->lastModified($file),
                ];
            }
            
            return response()->json([
                'files' => $fileList
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => [
                    'message' => '获取文件列表失败: ' . $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get file type based on extension
     */
    private function getFileType(string $extension): string
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        $videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv'];
        $audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
        
        $extension = strtolower($extension);
        
        if (in_array($extension, $imageExtensions)) {
            return 'image';
        } elseif (in_array($extension, $videoExtensions)) {
            return 'video';
        } elseif (in_array($extension, $audioExtensions)) {
            return 'audio';
        } else {
            return 'document';
        }
    }
}