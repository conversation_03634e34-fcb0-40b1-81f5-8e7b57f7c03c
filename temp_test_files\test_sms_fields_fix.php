<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Models\Activity;

echo "=== 测试SMS字段修复 ===\n";

// 1. 测试创建新的ActivityDetail记录
echo "\n1. 测试创建新的ActivityDetail记录...\n";

try {
    // 获取一个现有的Activity
    $activity = Activity::first();
    if (!$activity) {
        echo "没有找到Activity记录，创建一个测试记录...\n";
        $activity = Activity::create([
            'title' => '测试活动 - SMS字段修复验证',
            'description' => '这是一个测试活动，用于验证SMS字段修复',
            'status' => 1
        ]);
    }
    
    // 创建新的ActivityDetail记录，包含topic和target_audience字段
    $activityDetail = ActivityDetail::create([
            'activity_id' => $activity->id,
            'theme' => '2024年春季教学研讨会 - 第二场',
            'topic' => '2024年春季教学研讨会 - 第二场',
            'activity_time' => '2025-07-01 14:22:00',
            'address' => '学校会议室',
            'target' => '中学教师',
            'target_audience' => '中学教师',
            'fee' => 0,
            'quota' => 50,
            'current_count' => 0,
            'deadline' => '2025-06-30 23:59:59',
            'registration_deadline' => '2025-06-30 23:59:59',
            'status' => 1,
            'registration_method' => '在线报名',
            'sms_template' => '现场咨询通知',
        ]);
    
    echo "成功创建ActivityDetail记录，ID: {$activityDetail->id}\n";
    echo "topic字段: {$activityDetail->topic}\n";
    echo "target_audience字段: {$activityDetail->target_audience}\n";
    
} catch (Exception $e) {
    echo "创建ActivityDetail记录失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 测试Registration记录
echo "\n2. 测试Registration记录...\n";

try {
    // 创建测试Registration记录
    $registration = Registration::create([
            'activity_detail_id' => $activityDetail->id,
            'name' => '张三',
            'phone' => '13800138000',
            'organization' => '测试学校',
            'status' => 1, // 1表示已确认
            'source' => 'web', // 来源
        ]);
    
    echo "成功创建Registration记录，ID: {$registration->id}\n";
    
} catch (Exception $e) {
    echo "创建Registration记录失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 3. 测试buildTemplateData逻辑
echo "\n3. 测试buildTemplateData逻辑...\n";

// 模拟RegistrationController中的buildTemplateData方法
function buildTemplateData($registration, $activityDetail = null) {
    if ($activityDetail === null) {
        $activityDetail = $registration->activityDetail;
    }
    $activity = $activityDetail->activity;
    
    // #name# - 用户姓名
    $name = !empty($registration->name) ? $registration->name : "用户";
    
    // #topic# - 活动主题 (优先使用新的topic字段)
    $topic = "";
    if (!empty($activityDetail->topic)) {
        $topic = $activityDetail->topic;
    } elseif (!empty($activity->title)) {
        $topic = $activity->title;
    } elseif (!empty($activityDetail->theme)) {
        $topic = $activityDetail->theme;
    } else {
        $topic = "活动通知";
    }
    
    // #time# - 活动时间
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        try {
            $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
        } catch (Exception $e) {
            $time = "待定时间";
        }
    } else {
        $time = "待定时间";
    }
    
    // #address# - 活动地址
    $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
    
    // #obj# - 目标对象 (优先使用新的target_audience字段)
    $obj = "";
    if (!empty($activityDetail->target_audience)) {
        $obj = $activityDetail->target_audience;
    } elseif (!empty($activityDetail->target)) {
        $obj = $activityDetail->target;
    } else {
        $obj = "全体人员";
    }
    
    return [
        "name" => $name,
        "topic" => $topic,
        "time" => $time,
        "address" => $address,
        "obj" => $obj
    ];
}

// 测试模板数据构建
$templateData = buildTemplateData($registration, $activityDetail);

echo "构建的模板数据:\n";
foreach ($templateData as $key => $value) {
    echo "  {$key}: {$value}\n";
}

// 4. 生成SMS内容
echo "\n4. 生成SMS内容...\n";

$smsContent = "【上海市嘉定区教育学院】现场咨询，主题为：{$templateData['topic']}；时间为：{$templateData['time']}；地点为：{$templateData['address']}；对象为：{$templateData['obj']}；请准时参加。";

echo "生成的SMS内容:\n";
echo $smsContent . "\n";

// 5. 验证字段不为空
echo "\n5. 验证字段完整性...\n";

$allFieldsValid = true;
foreach ($templateData as $key => $value) {
    if (empty($value) || $value === '待定时间' || $value === '待定地点' || $value === '全体人员') {
        if ($key === 'time' && $value !== '待定时间') continue;
        if ($key === 'address' && $value !== '待定地点') continue;
        if ($key === 'obj' && $value !== '全体人员') continue;
        
        echo "警告: {$key} 字段为空或使用默认值: {$value}\n";
        if (empty($value)) {
            $allFieldsValid = false;
        }
    }
}

if ($allFieldsValid) {
    echo "✓ 所有字段都有有效值\n";
} else {
    echo "✗ 存在空字段\n";
}

// 6. 测试用户提到的具体案例
echo "\n6. 测试用户提到的具体案例...\n";
echo "用户提到的模板: 【上海市嘉定区教育学院】现场咨询，主题为：2024年春季教学研讨会 - 第二场；时间为：；地点为：学校会议室；对象为：；请准时参加。\n";
echo "修复后的模板: {$smsContent}\n";

// 检查是否修复了空字段问题
if (strpos($smsContent, '时间为：；') !== false || strpos($smsContent, '对象为：；') !== false) {
    echo "✗ 仍然存在空字段问题\n";
} else {
    echo "✓ 空字段问题已修复\n";
}

echo "\n=== 测试完成 ===\n";