<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 重新设计activity_details表，使字段名直接与云片网短信模板参数一致
     * 云片网短信模板需要的参数：#topic#, #time#, #address#, #obj#
     */
    public function up(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            // 添加与云片网参数直接对应的字段
            // topic字段已存在，不需要重复添加
            // address字段已存在，不需要重复添加
            $table->string('time')->nullable()->comment('活动时间 - 直接对应云片网#time#参数');
            $table->string('obj')->nullable()->comment('目标对象 - 直接对应云片网#obj#参数');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            // 只删除新添加的字段，保留原有字段
            $table->dropColumn(['time', 'obj']);
            // topic和address是原有字段，不删除
        });
    }
};