<?php

namespace App\Filament\Resources\WechatArticleResource\Pages;

use App\Filament\Resources\WechatArticleResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateWechatArticle extends CreateRecord
{
    protected static string $resource = WechatArticleResource::class;
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['collected_at'] = now();
        $data['status'] = 'pending';
        
        return $data;
    }
}