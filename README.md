# Laravel 11 + Filament 3 CMS

A high-performance, modular enterprise content management system built with Laravel 11 and Filament 3.

## 🚀 Features

- 🔐 Multi-role permission management
- 🌳 Tree-structured content categories
- 📝 Rich text editing
- 📱 WeChat integration
- 📊 Real-time monitoring
- 🔍 Global search
- 🛡️ Enhanced security with captcha
- 📁 Media management

## 🛠️ Tech Stack

- **Core Framework**: Laravel 11, Filament 3
- **Database**: MySQL 8.0+
- **Cache/Queue**: Redis
- **Frontend**: Tailwind CSS, Alpine.js

## 📋 Prerequisites

- PHP 8.2+
- MySQL 8.0+
- Redis 6.2+
- Composer
- Node.js & NPM

## 🚀 Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd [project-name]
```

2. Install PHP dependencies:
```bash
composer install
```

3. Install and configure environment:
```bash
cp .env.example .env
php artisan key:generate
```

4. Configure database in `.env`:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=root
DB_PASSWORD=123456
```

5. Run migrations:
```bash
php artisan migrate
```

6. Install required plugins:
```bash
# Permission management
composer require bezhansalleh/filament-shield

# Tree structure
composer require solutionforest/filament-tree

# Login captcha
composer require bezhansalleh/login-captcha

# Real-time monitoring
composer require laravel/pulse
```

7. Initialize Shield:
```bash
php artisan shield:install --fresh
php artisan shield:super-admin
php artisan shield:generate --all
```

## 🔧 Configuration

### Filament Shield (Permissions)
```php
// In resource classes
protected static ?string $permission = 'posts.manage';
```

### Login Captcha
```php
// config/filament.php
'plugins' => [
    \BezhanSalleh\LoginCaptcha\Filament\LoginCaptchaPlugin::make()
        ->length(5)
        ->charset('123456ABCDEF')
        ->width(120)
        ->height(40),
],
```

## 📁 Core Modules

### Category Management
```php
use SolutionForest\FilamentTree\Concerns\HasTree;

class Category extends Model {
    use HasTree;
    protected $fillable = ['name', 'parent_id'];
}
```

### Rich Text Editor
```php
use Rawilk\FilamentQuill\Filament\Forms\Components\QuillEditor;

QuillEditor::make('content')
    ->toolbar([
        'bold', 'italic', 'underline',
        'blockquote', 'code-block',
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link' => ['url' => true, 'title' => false]],
    ])
    ->required();
```

## 🔒 Security

- Login attempt rate limiting
- Captcha protection
- Role-based access control
- Data encryption for sensitive fields

## 📊 Monitoring

Configure Laravel Pulse for real-time monitoring:
```php
Pulse::measure(LoginAttempt::class)
    ->filter(function ($event) {
        return $event->successful;
    })
    ->threshold(3);
```

## 🚀 Performance Optimization

```bash
php artisan route:cache
php artisan config:cache
php artisan filament:assets
```

## 🔧 Troubleshooting

### Captcha Issues
- Verify GD library installation
- Check session driver configuration
- Run `php artisan route:list` to verify captcha routes

### Permission Sync Issues
```bash
php artisan shield:clear
php artisan shield:generate --all
```

## 📚 Additional Resources

- [Laravel Documentation](https://laravel.com/docs)
- [Filament Documentation](https://filamentphp.com/docs)
- [Filament Shield Documentation](https://github.com/bezhanSalleh/filament-shield)

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details. 