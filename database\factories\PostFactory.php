<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(6, true);
        return [
            'title' => $title,
            'short_title' => $this->faker->words(3, true),
            'slug' => Str::slug($title) . '-' . $this->faker->unique()->numberBetween(1, 9999),
            'excerpt' => $this->faker->paragraph(),
            'content' => $this->faker->paragraphs(3, true),
            'category_id' => 1,
            'featured_image' => null,
            'thumb_image' => null,
            'meta_data' => [],
            'is_published' => $this->faker->boolean(),
            'published_at' => $this->faker->dateTimeThisYear(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'custom_flags' => [],
            'weight' => $this->faker->numberBetween(1, 10),
            'source' => $this->faker->company(),
            'author' => $this->faker->name(),
            'allow_comment' => $this->faker->boolean(),
            'view_permission' => 'public',
            'title_color' => null,
            'extra_options' => [],
        ];
    }
}
