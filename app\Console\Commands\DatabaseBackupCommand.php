<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DatabaseBackupService;
use App\Models\DatabaseBackup;
use App\Models\Setting;

class DatabaseBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:database
                            {--type=full : 备份类型 (full, structure, data)}
                            {--name= : 备份名称}
                            {--description= : 备份描述}
                            {--auto : 自动备份模式}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建数据库备份';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $backupService = app(DatabaseBackupService::class);

        // 检查是否为自动备份模式
        if ($this->option('auto')) {
            // 检查自动备份是否启用
            if (!Setting::getValue('backup_auto_enabled', false)) {
                $this->info('自动备份未启用');
                return 0;
            }

            $backupType = Setting::getValue('backup_auto_type', DatabaseBackup::TYPE_FULL);
            $name = 'auto_backup_' . now()->format('Y-m-d_H-i-s');
            $description = '系统自动备份';
        } else {
            $backupType = $this->option('type') ?? DatabaseBackup::TYPE_FULL;
            $name = $this->option('name') ?? 'manual_backup_' . now()->format('Y-m-d_H-i-s');
            $description = $this->option('description') ?? '手动备份';
        }

        // 验证备份类型
        if (!in_array($backupType, [DatabaseBackup::TYPE_FULL, DatabaseBackup::TYPE_STRUCTURE, DatabaseBackup::TYPE_DATA])) {
            $this->error('无效的备份类型: ' . $backupType);
            return 1;
        }

        $this->info("开始创建数据库备份...");
        $this->info("备份类型: " . DatabaseBackup::getBackupTypes()[$backupType]);
        $this->info("备份名称: " . $name);

        try {
            $backup = $backupService->createBackup([
                'name' => $name,
                'backup_type' => $backupType,
                'description' => $description,
                'created_by' => null, // 命令行执行
            ]);

            $this->info("备份任务已创建，ID: " . $backup->id);

            // 等待备份完成
            $this->info("正在执行备份...");

            while ($backup->fresh()->status === DatabaseBackup::STATUS_RUNNING) {
                sleep(1);
                $this->output->write('.');
            }

            $backup = $backup->fresh();

            if ($backup->status === DatabaseBackup::STATUS_COMPLETED) {
                $this->newLine();
                $this->info("✅ 备份完成!");
                $this->info("文件路径: " . $backup->file_path);
                $this->info("文件大小: " . $backup->formatted_file_size);
                $this->info("耗时: " . $backup->duration);

                // 自动清理过期备份
                if ($this->option('auto')) {
                    $retentionDays = Setting::getValue('backup_retention_days', 30);
                    $deletedCount = $backupService->cleanupOldBackups($retentionDays);
                    if ($deletedCount > 0) {
                        $this->info("已清理 {$deletedCount} 个过期备份");
                    }
                }

                return 0;
            } else {
                $this->newLine();
                $this->error("❌ 备份失败!");
                $this->error("错误信息: " . $backup->error_message);
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("备份过程中发生错误: " . $e->getMessage());
            return 1;
        }
    }
}
