<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SmsTemplate;
use App\Models\ActivityDetail;
use App\Services\SmsService;

echo "=== 调试现场咨询通知模板参数问题 ===\n\n";

// 1. 查看现场咨询通知模板
$template = SmsTemplate::where('code', 'consultation_notice')->first();

if ($template) {
    echo "模板信息:\n";
    echo "- 名称: {$template->name}\n";
    echo "- 代码: {$template->code}\n";
    echo "- 云片网模板ID: {$template->yunpian_template_id}\n";
    echo "- 模板参数 (原始): {$template->template_params}\n";
    
    $paramsList = $template->getTemplateParamsList();
    echo "- 模板参数 (解析后): " . json_encode($paramsList, JSON_UNESCAPED_UNICODE) . "\n";
    echo "- 需要的参数键: " . implode(', ', array_keys($paramsList)) . "\n\n";
    
    // 2. 查看使用此模板的活动详情
    $activities = ActivityDetail::where('sms_template_id', $template->id)
        ->with(['activity', 'smsTemplate'])
        ->get();
    
    echo "使用此模板的活动数量: {$activities->count()}\n\n";
    
    if ($activities->count() > 0) {
        $activity = $activities->first();
        echo "测试活动信息:\n";
        echo "- 活动标题: {$activity->activity->title}\n";
        echo "- 活动时间: {$activity->activity_time}\n";
        echo "- 活动地点: {$activity->address}\n";
        echo "- 目标对象: {$activity->target}\n\n";
        
        // 3. 模拟RegistrationController中的参数构建
        echo "模拟参数构建过程:\n";
        $baseData = [
            'name' => '测试用户',
            'topic' => $activity->activity->title,
            'time' => $activity->activity_time,
            'address' => $activity->address,
            'obj' => $activity->target ?? '全体人员'
        ];
        
        echo "构建的基础数据:\n";
        foreach ($baseData as $key => $value) {
            echo "  {$key}: {$value}\n";
        }
        
        // 4. 验证参数
        echo "\n参数验证:\n";
        $validation = $template->validateParams($baseData);
        if ($validation === true) {
            echo "✓ 参数验证通过\n";
        } else {
            echo "✗ 参数验证失败，缺少参数: " . implode(', ', $validation) . "\n";
        }
        
        // 5. 检查SmsService中的参数处理
        echo "\nSmsService参数处理检查:\n";
        $smsService = new SmsService();
        
        // 模拟SmsService中的tpl_value构建
        $tplValue = '';
        if (!empty($baseData)) {
            $pairs = [];
            foreach ($baseData as $key => $value) {
                // 确保变量名包含#号
                $varName = strpos($key, '#') === false ? "#{$key}#" : $key;
                $pairs[] = urlencode($varName) . '=' . urlencode($value);
            }
            $tplValue = implode('&', $pairs);
        }
        
        echo "构建的tpl_value参数:\n";
        echo $tplValue . "\n\n";
        
        // 6. 解码查看实际发送的参数
        echo "解码后的参数:\n";
        parse_str($tplValue, $decodedParams);
        foreach ($decodedParams as $key => $value) {
            echo "  {$key}: {$value}\n";
        }
        
        // 7. 检查云片网模板要求的参数格式
        echo "\n云片网模板参数要求:\n";
        echo "模板内容应该包含: #topic#、#time#、#address#、#obj# 等变量\n";
        echo "实际发送的参数键名: " . implode(', ', array_keys($decodedParams)) . "\n";
        
    } else {
        echo "没有找到使用此模板的活动\n";
    }
    
} else {
    echo "未找到现场咨询通知模板\n";
}

echo "\n=== 调试完成 ===\n";