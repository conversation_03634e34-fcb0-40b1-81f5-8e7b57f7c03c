<?php
/**
 * 分析权限相关表的差异
 */

echo "🔐 权限系统分析工具\n";
echo "==================\n\n";

$oldDbFile = 'storage/app/private/private/backups/123_178188_xyz8.sql';
$newDbFile = 'storage/app/private/private/backups/123_178188_xyz9.sql';

$oldContent = file_get_contents($oldDbFile);
$newContent = file_get_contents($newDbFile);

// 提取权限相关表的AUTO_INCREMENT值
function extractPermissionTables($content) {
    $tables = ['roles', 'permissions', 'model_has_roles', 'model_has_permissions', 'role_has_permissions'];
    $result = [];
    
    foreach ($tables as $table) {
        $pattern = '/CREATE TABLE `' . preg_quote($table) . '`.*?AUTO_INCREMENT = (\d+)/s';
        if (preg_match($pattern, $content, $matches)) {
            $result[$table] = (int)$matches[1];
        } else {
            // 如果没有AUTO_INCREMENT，检查是否有数据
            $dataPattern = '/Records of ' . preg_quote($table) . '.*?INSERT INTO `' . preg_quote($table) . '`/s';
            if (preg_match($dataPattern, $content)) {
                $result[$table] = '有数据';
            } else {
                $result[$table] = '无数据';
            }
        }
    }
    
    return $result;
}

// 提取具体的权限数据
function extractPermissionData($content) {
    $result = [];
    
    // 提取roles数据
    $pattern = '/-- Records of roles.*?INSERT INTO `roles` VALUES (.*?);/s';
    if (preg_match($pattern, $content, $matches)) {
        $result['roles'] = $matches[1];
    }
    
    // 提取permissions数据
    $pattern = '/-- Records of permissions.*?INSERT INTO `permissions` VALUES (.*?);/s';
    if (preg_match($pattern, $content, $matches)) {
        $result['permissions'] = $matches[1];
    }
    
    // 提取model_has_roles数据
    $pattern = '/-- Records of model_has_roles.*?INSERT INTO `model_has_roles` VALUES (.*?);/s';
    if (preg_match($pattern, $content, $matches)) {
        $result['model_has_roles'] = $matches[1];
    }
    
    return $result;
}

$oldPermissions = extractPermissionTables($oldContent);
$newPermissions = extractPermissionTables($newContent);

echo "📊 权限表比对:\n";
echo "表名\t\t\t旧库\t\t新库\n";
echo "------------------------------------------------\n";

foreach (['roles', 'permissions', 'model_has_roles', 'model_has_permissions', 'role_has_permissions'] as $table) {
    $old = isset($oldPermissions[$table]) ? $oldPermissions[$table] : '不存在';
    $new = isset($newPermissions[$table]) ? $newPermissions[$table] : '不存在';
    echo sprintf("%-20s\t%s\t\t%s\n", $table, $old, $new);
}

echo "\n🔍 详细权限数据分析:\n";

$oldData = extractPermissionData($oldContent);
$newData = extractPermissionData($newContent);

// 分析roles
echo "\n📋 角色 (roles):\n";
if (isset($oldData['roles'])) {
    echo "旧库有角色数据:\n";
    // 简单解析角色数据
    $roles = explode('),(', $oldData['roles']);
    foreach ($roles as $role) {
        $role = trim($role, '()');
        echo "  - $role\n";
    }
} else {
    echo "旧库无角色数据\n";
}

if (isset($newData['roles'])) {
    echo "新库有角色数据:\n";
    $roles = explode('),(', $newData['roles']);
    foreach ($roles as $role) {
        $role = trim($role, '()');
        echo "  - $role\n";
    }
} else {
    echo "新库无角色数据\n";
}

// 分析permissions
echo "\n🔑 权限 (permissions):\n";
if (isset($oldData['permissions'])) {
    $permissions = explode('),(', $oldData['permissions']);
    echo "旧库权限数量: " . count($permissions) . "\n";
} else {
    echo "旧库无权限数据\n";
}

if (isset($newData['permissions'])) {
    $permissions = explode('),(', $newData['permissions']);
    echo "新库权限数量: " . count($permissions) . "\n";
} else {
    echo "新库无权限数据\n";
}

// 分析用户角色关联
echo "\n👥 用户角色关联 (model_has_roles):\n";
if (isset($oldData['model_has_roles'])) {
    echo "旧库有用户角色关联数据\n";
    $relations = explode('),(', $oldData['model_has_roles']);
    echo "关联数量: " . count($relations) . "\n";
} else {
    echo "旧库无用户角色关联数据\n";
}

if (isset($newData['model_has_roles'])) {
    echo "新库有用户角色关联数据\n";
    $relations = explode('),(', $newData['model_has_roles']);
    echo "关联数量: " . count($relations) . "\n";
} else {
    echo "新库无用户角色关联数据\n";
}

// 检查管理员用户
echo "\n👨‍💼 管理员用户分析:\n";

// 从users表中提取管理员
function extractAdminUsers($content) {
    $pattern = '/-- Records of users.*?INSERT INTO `users` VALUES (.*?);/s';
    if (preg_match($pattern, $content, $matches)) {
        $users = $matches[1];
        // 查找包含admin的用户
        if (strpos($users, 'admin@') !== false) {
            return "找到admin用户";
        }
    }
    return "未找到admin用户";
}

echo "旧库: " . extractAdminUsers($oldContent) . "\n";
echo "新库: " . extractAdminUsers($newContent) . "\n";

echo "\n🎯 关键发现:\n";
echo "============\n";

$issues = [];

if (!isset($oldData['roles']) && !isset($newData['roles'])) {
    $issues[] = "两个库都缺少角色数据";
}

if (!isset($oldData['permissions']) && !isset($newData['permissions'])) {
    $issues[] = "两个库都缺少权限数据";
}

if (!isset($oldData['model_has_roles']) && !isset($newData['model_has_roles'])) {
    $issues[] = "两个库都缺少用户角色关联数据";
}

if (empty($issues)) {
    echo "✅ 权限系统数据完整\n";
} else {
    echo "❌ 发现问题:\n";
    foreach ($issues as $issue) {
        echo "  - $issue\n";
    }
    
    echo "\n💡 这解释了403问题的根本原因！\n";
    echo "系统缺少基础的权限数据，需要重新初始化权限系统。\n";
}

echo "\n🔧 建议解决方案:\n";
echo "================\n";
echo "1. 运行权限初始化命令: php artisan permission:create-role admin\n";
echo "2. 运行权限同步命令: php artisan shield:generate\n";
echo "3. 重新分配管理员角色\n";
echo "4. 清除缓存: php artisan cache:clear\n";

echo "\n✨ 分析完成！\n";
?>
