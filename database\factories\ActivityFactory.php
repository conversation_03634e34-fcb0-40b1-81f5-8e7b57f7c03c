<?php

namespace Database\Factories;

use App\Models\Activity;
use Illuminate\Database\Eloquent\Factories\Factory;

class ActivityFactory extends Factory
{
    protected $model = Activity::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(['draft', 'published']),
            'publish_time' => $this->faker->dateTimeThisMonth(),
            'views_count' => $this->faker->numberBetween(0, 1000),
            'qrcode_url' => $this->faker->imageUrl(),
            'wechat_url' => $this->faker->url(),
        ];
    }
} 