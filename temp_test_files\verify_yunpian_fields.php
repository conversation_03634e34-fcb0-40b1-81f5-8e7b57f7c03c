<?php

/**
 * 验证脚本：检查云片网直接对应字段是否正确工作
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 验证云片网直接对应字段 ===\n\n";

try {
    // 1. 检查字段是否存在
    echo "1. 检查数据库字段...\n";
    $columns = DB::select("SHOW COLUMNS FROM activity_details");
    $fieldNames = array_column($columns, 'Field');
    
    $requiredFields = ['topic', 'time', 'address', 'obj'];
    $missingFields = [];
    
    foreach ($requiredFields as $field) {
        if (in_array($field, $fieldNames)) {
            echo "✅ {$field} 字段存在\n";
        } else {
            echo "❌ {$field} 字段不存在\n";
            $missingFields[] = $field;
        }
    }
    
    if (!empty($missingFields)) {
        echo "\n缺少字段: " . implode(', ', $missingFields) . "\n";
        echo "请先运行迁移脚本添加缺少的字段。\n";
        exit(1);
    }
    
    // 2. 检查现有数据
    echo "\n2. 检查现有数据...\n";
    $activityDetails = DB::table('activity_details')
        ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
        ->select(
            'activity_details.id',
            'activities.title as activity_title',
            'activity_details.theme',
            'activity_details.topic',
            'activity_details.time',
            'activity_details.activity_time',
            'activity_details.address',
            'activity_details.obj',
            'activity_details.target',
            'activity_details.target_audience'
        )
        ->limit(3)
        ->get();
    
    if ($activityDetails->isEmpty()) {
        echo "没有找到活动详情数据\n";
        exit(1);
    }
    
    echo "找到 " . $activityDetails->count() . " 条活动详情记录\n\n";
    
    // 3. 模拟字段映射逻辑
    echo "3. 测试字段映射逻辑...\n";
    
    foreach ($activityDetails as $detail) {
        echo "--- 活动详情 ID: {$detail->id} ---\n";
        
        // #topic# - 活动主题 (优先使用直接对应的topic字段)
        $topic = !empty($detail->topic) ? $detail->topic : 
                (!empty($detail->activity_title) ? $detail->activity_title : 
                (!empty($detail->theme) ? $detail->theme : '未设置主题'));
        
        // #time# - 活动时间 (优先使用直接对应的time字段)
        $time = '';
        if (!empty($detail->time)) {
            $time = $detail->time;
        } elseif (!empty($detail->activity_time)) {
            try {
                $time = date('Y年m月d日 H:i', strtotime($detail->activity_time));
            } catch (Exception $e) {
                $time = $detail->activity_time;
            }
        } else {
            $time = '未设置时间';
        }
        
        // #address# - 活动地址
        $address = !empty($detail->address) ? $detail->address : '未设置地址';
        
        // #obj# - 目标对象 (优先使用直接对应的obj字段)
        $obj = !empty($detail->obj) ? $detail->obj : 
               (!empty($detail->target_audience) ? $detail->target_audience : 
               (!empty($detail->target) ? $detail->target : '未设置对象'));
        
        $templateData = [
            "topic" => $topic,
            "time" => $time,
            "address" => $address,
            "obj" => $obj
        ];
        
        echo "字段映射结果：\n";
        foreach ($templateData as $key => $value) {
            echo "  #{$key}#: '{$value}'\n";
        }
        
        // 生成短信内容
        $smsTemplate = '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。';
        $smsContent = $smsTemplate;
        foreach ($templateData as $key => $value) {
            $smsContent = str_replace("#{$key}#", $value, $smsContent);
        }
        
        echo "生成的短信内容：\n";
        echo "  {$smsContent}\n";
        
        // 检查是否有空字段或未替换的占位符
        $hasEmptyFields = false;
        $hasUnreplacedPlaceholders = false;
        
        foreach ($templateData as $key => $value) {
            if (empty($value) || strpos($value, '未设置') !== false) {
                $hasEmptyFields = true;
                break;
            }
        }
        
        if (preg_match('/#\w+#/', $smsContent)) {
            $hasUnreplacedPlaceholders = true;
        }
        
        if (!$hasEmptyFields && !$hasUnreplacedPlaceholders) {
            echo "  ✅ 字段完整，短信内容正常\n";
        } else {
            if ($hasEmptyFields) {
                echo "  ❌ 存在空字段或默认值\n";
            }
            if ($hasUnreplacedPlaceholders) {
                echo "  ❌ 存在未替换的占位符\n";
            }
        }
        
        echo "\n";
    }
    
    // 4. 统计空字段情况
    echo "4. 统计空字段情况...\n";
    $emptyStats = [];
    foreach ($requiredFields as $field) {
        $emptyCount = DB::table('activity_details')
            ->whereNull($field)
            ->orWhere($field, '')
            ->count();
        $emptyStats[$field] = $emptyCount;
        echo "- {$field}字段为空: {$emptyCount} 条\n";
    }
    
    $totalEmpty = array_sum($emptyStats);
    if ($totalEmpty == 0) {
        echo "\n✅ 所有云片网字段都已填充完整！\n";
        echo "重新设计的字段结构工作正常，短信模板不会再出现空字段问题。\n";
    } else {
        echo "\n⚠️ 仍有 {$totalEmpty} 个字段为空，建议检查数据迁移脚本。\n";
    }
    
} catch (Exception $e) {
    echo "❌ 验证过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 验证完成 ===\n";