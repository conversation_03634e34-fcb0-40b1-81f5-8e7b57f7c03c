<?php

namespace App\Listeners;

use App\Models\AuditLog;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Request;

class LogSuccessfulLogin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        try {
            AuditLog::create([
                'user_id' => $event->user->id,
                'operated_at' => now(),
                'operation_type' => 'LOGIN',
                'module' => '系统登录',
                'target_id' => (string)$event->user->id,
                'request_ip' => Request::ip(),
                'request_url' => Request::fullUrl(),
                'request_data' => [
                    'user_agent' => Request::userAgent(),
                    'login_time' => now()->toDateTimeString(),
                ],
                'status' => 'SUCCESS',
                'error_message' => null,
                'remark' => '用户成功登录系统',
            ]);
        } catch (\Exception $e) {
            \Log::error('记录登录日志失败: ' . $e->getMessage());
        }
    }
}
