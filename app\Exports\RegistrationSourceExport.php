<?php

namespace App\Exports;

use App\Models\Registration;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RegistrationSourceExport implements FromCollection, WithHeadings, WithTitle
{
    public function collection()
    {
        return Registration::select('source', DB::raw('COUNT(*) as count'))
            ->whereNotNull('source')
            ->where('status', true) // 只统计有效报名
            ->groupBy('source')
            ->orderBy('count', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            '来源',
            '报名人数'
        ];
    }

    public function title(): string
    {
        return '来源分布';
    }
}