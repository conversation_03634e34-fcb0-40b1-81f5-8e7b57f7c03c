<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SystemSettingResource\Pages;
use App\Models\SystemSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\SelectFilter;

class SystemSettingResource extends Resource
{
    protected static ?string $model = SystemSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    
    protected static ?string $navigationLabel = '系统设置';
    
    protected static ?string $modelLabel = '系统设置';
    
    protected static ?string $pluralModelLabel = '系统设置';
    
    protected static ?string $navigationGroup = '系统管理';
    
    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('key')
                                    ->label('设置键名')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255),
                                Forms\Components\Select::make('type')
                                    ->label('数据类型')
                                    ->options([
                                        'string' => '字符串',
                                        'integer' => '整数',
                                        'float' => '浮点数',
                                        'boolean' => '布尔值',
                                        'array' => '数组',
                                        'json' => 'JSON',
                                    ])
                                    ->required()
                                    ->reactive(),
                            ]),
                        Forms\Components\TextInput::make('description')
                            ->label('设置描述')
                            ->maxLength(255),
                        Forms\Components\Select::make('group')
                    ->label('设置分组')
                    ->options([
                        'site' => '站点设置',
                        'general' => '常规设置',
                        'upload' => '上传设置',
                        'security' => '安全设置',
                        'audit' => '审计设置',
                        'statistics' => '统计设置',
                        'system' => '系统设置',
                    ])
                    ->default('general')
                    ->required(),
                    ]),
                Section::make('设置值')
                    ->schema([
                        Forms\Components\TextInput::make('value')
                            ->label('设置值')
                            ->required()
                            ->visible(fn (Forms\Get $get): bool => in_array($get('type'), ['string', 'integer', 'float']))
                            ->numeric(fn (Forms\Get $get): bool => in_array($get('type'), ['integer', 'float']))
                            ->formatStateUsing(function ($state, $record) {
                                if (!$record) return $state;
                                return match ($record->type) {
                                    'boolean' => $state ? '1' : '0',
                                    'array', 'json' => is_string($state) ? $state : json_encode($state),
                                    default => (string) $state,
                                };
                            }),
                        Forms\Components\Toggle::make('value')
                            ->label('设置值')
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'boolean')
                            ->formatStateUsing(function ($state, $record) {
                                if (!$record) return false;
                                return filter_var($state, FILTER_VALIDATE_BOOLEAN);
                            }),
                        Forms\Components\Textarea::make('value')
                            ->label('设置值')
                            ->rows(5)
                            ->visible(fn (Forms\Get $get): bool => in_array($get('type'), ['array', 'json']))
                            ->helperText('请输入有效的JSON格式数据')
                            ->formatStateUsing(function ($state, $record) {
                                if (!$record) return $state;
                                if (is_string($state)) {
                                    $decoded = json_decode($state, true);
                                    return $decoded ? json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $state;
                                }
                                return json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label('设置键名')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('设置描述')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('type')
                    ->label('数据类型')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'string' => 'gray',
                        'integer' => 'info',
                        'float' => 'warning',
                        'boolean' => 'success',
                        'array', 'json' => 'primary',
                        default => 'secondary',
                    }),
                Tables\Columns\TextColumn::make('group')
                    ->label('设置分组')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'site' => '站点设置',
                        'general' => '常规设置',
                        'upload' => '上传设置',
                        'security' => '安全设置',
                        'audit' => '审计设置',
                        'statistics' => '统计设置',
                        'system' => '系统设置',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('value')
                    ->label('设置值')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) > 30) {
                            return $state;
                        }
                        return null;
                    }),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label('数据类型')
                    ->options([
                        'string' => '字符串',
                        'integer' => '整数',
                        'float' => '浮点数',
                        'boolean' => '布尔值',
                        'array' => '数组',
                        'json' => 'JSON',
                    ]),
                SelectFilter::make('group')
                    ->label('设置分组')
                    ->options([
                        'site' => '站点设置',
                        'general' => '常规设置',
                        'upload' => '上传设置',
                        'security' => '安全设置',
                        'audit' => '审计设置',
                        'statistics' => '统计设置',
                        'system' => '系统设置',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('group')
            ->searchable();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSystemSettings::route('/'),
            'create' => Pages\CreateSystemSetting::route('/create'),
            'edit' => Pages\EditSystemSetting::route('/{record}/edit'),
        ];
    }
}