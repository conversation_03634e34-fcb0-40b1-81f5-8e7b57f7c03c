<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PointCenterResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class PointCenterResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = '成就管理';

    protected static ?int $navigationSort = 2;

    protected static ?int $navigationGroupSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('rank')
                    ->label('排名')
                    ->getStateUsing(function ($record, $rowLoop) {
                        return $rowLoop->iteration;
                    })
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state == 1 => 'warning',
                        $state == 2 => 'gray',
                        $state == 3 => 'orange',
                        default => 'primary'
                    }),
                Tables\Columns\TextColumn::make('region')
                    ->label('地区')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user_count')
                    ->label('用户数')
                    ->suffix(' 人')
                    ->color('info'),
                Tables\Columns\TextColumn::make('total_points')
                    ->label('总积分')
                    ->suffix(' 分')
                    ->color('success'),
                Tables\Columns\TextColumn::make('month_points')
                    ->label('月积分')
                    ->suffix(' 分'),
                Tables\Columns\TextColumn::make('quarter_points')
                    ->label('季度积分')
                    ->suffix(' 分'),
                Tables\Columns\TextColumn::make('year_points')
                    ->label('年积分')
                    ->suffix(' 分'),
            ])
            ->filters([
                // 暂时移除过滤器，因为我们使用的是简化查询
            ])
            ->actions([
                // 暂时禁用操作，因为我们使用的是stdClass而不是Model
            ])
            ->recordAction(null)
            ->recordUrl(null)
            ->defaultSort('region', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPointCenter::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '积分中心';
    }

    public static function getModelLabel(): string
    {
        return '地区积分';
    }

    public static function getPluralModelLabel(): string
    {
        return '地区积分';
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }
}
