<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('avatar')->nullable()->after('email');
            $table->boolean('is_active')->default(true)->after('avatar');
            $table->string('phone')->nullable()->after('is_active');
            $table->string('real_name')->nullable()->after('phone')->comment('真实姓名');
            $table->string('region')->nullable()->after('real_name')->comment('地区归属');
            $table->string('school')->nullable()->after('region')->comment('学校');
            $table->boolean('is_verified')->default(false)->after('school')->comment('是否实名认证');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['avatar', 'is_active', 'phone', 'real_name', 'region', 'school', 'is_verified']);
        });
    }
};