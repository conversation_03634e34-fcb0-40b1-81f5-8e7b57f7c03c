<?php

/**
 * 简化的最终测试：验证重新设计的云片网字段
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 云片网字段重新设计 - 简化最终测试 ===\n\n";

try {
    // 1. 直接查询活动和详情数据
    echo "1. 查询活动数据...\n";
    
    $testData = DB::table('activity_details')
        ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
        ->select(
            'activities.id as activity_id',
            'activities.title as activity_title',
            'activity_details.id as detail_id',
            'activity_details.theme',
            'activity_details.topic',
            'activity_details.time',
            'activity_details.activity_time',
            'activity_details.address',
            'activity_details.obj',
            'activity_details.target',
            'activity_details.target_audience',
            'activity_details.sms_template'
        )
        ->limit(3)
        ->get();
    
    if ($testData->isEmpty()) {
        echo "没有找到测试数据\n";
        exit(1);
    }
    
    echo "找到 " . $testData->count() . " 条测试数据\n\n";
    
    // 2. 测试每条数据的短信生成
    echo "2. 测试短信模板生成...\n";
    
    $successCount = 0;
    $totalCount = $testData->count();
    
    foreach ($testData as $data) {
        echo "--- 测试活动: {$data->activity_title} (ID: {$data->activity_id}) ---\n";
        
        // 重新设计后的字段映射逻辑（优先使用直接对应字段）
        $templateData = [
            'topic' => $data->topic ?: ($data->activity_title ?: ($data->theme ?: '未设置主题')),
            'time' => $data->time ?: (
                $data->activity_time ? 
                date('Y年m月d日 H:i', strtotime($data->activity_time)) : 
                '未设置时间'
            ),
            'address' => $data->address ?: '未设置地址',
            'obj' => $data->obj ?: ($data->target_audience ?: ($data->target ?: '未设置对象'))
        ];
        
        echo "字段映射结果：\n";
        foreach ($templateData as $key => $value) {
            echo "  #{$key}#: '{$value}'\n";
        }
        
        // 生成短信内容
        $smsTemplate = $data->sms_template ?: 
            '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。';
        
        $smsContent = $smsTemplate;
        foreach ($templateData as $key => $value) {
            $smsContent = str_replace("#{$key}#", $value, $smsContent);
        }
        
        echo "\n生成的短信内容：\n";
        echo "  {$smsContent}\n";
        
        // 验证结果
        $hasEmptyFields = false;
        $hasUnreplacedPlaceholders = false;
        
        foreach ($templateData as $key => $value) {
            if (empty($value) || strpos($value, '未设置') !== false) {
                $hasEmptyFields = true;
                break;
            }
        }
        
        if (preg_match('/#\w+#/', $smsContent)) {
            $hasUnreplacedPlaceholders = true;
        }
        
        echo "\n验证结果：\n";
        if (!$hasEmptyFields && !$hasUnreplacedPlaceholders) {
            echo "  ✅ 完美！所有字段都已正确填充\n";
            $successCount++;
        } else {
            if ($hasEmptyFields) {
                echo "  ❌ 存在空字段或默认值\n";
            }
            if ($hasUnreplacedPlaceholders) {
                echo "  ❌ 存在未替换的占位符\n";
            }
        }
        
        echo "\n" . str_repeat('-', 50) . "\n\n";
    }
    
    // 3. 统计结果
    echo "3. 测试结果统计...\n";
    echo "成功率: {$successCount}/{$totalCount} (" . round(($successCount/$totalCount)*100, 2) . "%%)\n\n";
    
    // 4. 数据库字段完整性统计
    echo "4. 数据库字段完整性统计...\n";
    
    $totalRecords = DB::table('activity_details')->count();
    $yunpianFields = ['topic', 'time', 'address', 'obj'];
    $emptyStats = [];
    
    foreach ($yunpianFields as $field) {
        $emptyCount = DB::table('activity_details')
            ->whereNull($field)
            ->orWhere($field, '')
            ->count();
        $emptyStats[$field] = $emptyCount;
        $percentage = $totalRecords > 0 ? round((($totalRecords - $emptyCount) / $totalRecords) * 100, 2) : 0;
        echo "- {$field}字段完整率: {$percentage}% (" . ($totalRecords - $emptyCount) . "/{$totalRecords})\n";
    }
    
    $totalEmptyFields = array_sum($emptyStats);
    $totalPossibleFields = $totalRecords * count($yunpianFields);
    $overallCompletionRate = $totalPossibleFields > 0 ? round((($totalPossibleFields - $totalEmptyFields) / $totalPossibleFields) * 100, 2) : 0;
    
    echo "\n总体数据完整率: {$overallCompletionRate}%\n";
    
    // 5. 最终结论
    echo "\n=== 重新设计效果评估 ===\n";
    
    if ($overallCompletionRate >= 95) {
        echo "🎉 重新设计非常成功！\n";
        echo "✅ 数据完整率达到 {$overallCompletionRate}%\n";
        echo "✅ 字段直接对应云片网参数，映射逻辑简化\n";
        echo "✅ 短信模板空字段问题已彻底解决\n";
    } elseif ($overallCompletionRate >= 80) {
        echo "✅ 重新设计基本成功！\n";
        echo "📊 数据完整率: {$overallCompletionRate}%\n";
        echo "💡 建议补充少量缺失数据以达到最佳效果\n";
    } else {
        echo "⚠️ 重新设计需要进一步优化\n";
        echo "📊 数据完整率: {$overallCompletionRate}%\n";
        echo "🔧 建议检查数据迁移脚本或手动补充数据\n";
    }
    
    echo "\n重新设计的核心改进：\n";
    echo "1. 字段名称与云片网参数一一对应 (topic, time, address, obj)\n";
    echo "2. 消除了复杂的字段映射逻辑\n";
    echo "3. 降低了维护成本和出错概率\n";
    echo "4. 为未来扩展提供了清晰的结构基础\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
