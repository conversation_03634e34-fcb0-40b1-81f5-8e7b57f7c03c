<?php

namespace App\Jobs;

use App\Models\WechatAccount;
use App\Services\WechatArticleCollectService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CollectWechatArticleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5分钟超时
    public $tries = 3; // 最多重试3次

    /**
     * Create a new job instance.
     */
    public function __construct(
        public WechatAccount $account,
        public int $count = 20
    ) {
        $this->onQueue('wechat-collect');
    }

    /**
     * Execute the job.
     */
    public function handle(WechatArticleCollectService $collectService): void
    {
        Log::info('开始采集微信公众号文章', [
            'account_id' => $this->account->id,
            'account_name' => $this->account->name,
            'count' => $this->count
        ]);

        try {
            $articles = $collectService->collectFromAccount($this->account, $this->count);
            
            Log::info('微信公众号文章采集完成', [
                'account_id' => $this->account->id,
                'account_name' => $this->account->name,
                'collected_count' => count($articles)
            ]);
        } catch (\Exception $e) {
            Log::error('微信公众号文章采集失败', [
                'account_id' => $this->account->id,
                'account_name' => $this->account->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('微信公众号文章采集任务失败', [
            'account_id' => $this->account->id,
            'account_name' => $this->account->name,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}