<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class WechatApiRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $accountId = $request->route('account_id') ?? $request->input('account_id');
        
        if (!$accountId) {
            return $next($request);
        }
        
        $cacheKey = "wechat_api_rate_limit:{$accountId}";
        $maxRequests = config('wechat-collect.limits.max_collect_per_day', 24);
        $windowSeconds = 24 * 60 * 60; // 24小时
        
        $currentCount = Cache::get($cacheKey, 0);
        
        if ($currentCount >= $maxRequests) {
            Log::warning('微信API请求频率限制', [
                'account_id' => $accountId,
                'current_count' => $currentCount,
                'max_requests' => $maxRequests
            ]);
            
            return response()->json([
                'error' => '请求频率过高，请稍后再试',
                'code' => 'RATE_LIMIT_EXCEEDED'
            ], 429);
        }
        
        // 增加计数
        Cache::put($cacheKey, $currentCount + 1, $windowSeconds);
        
        return $next($request);
    }
}