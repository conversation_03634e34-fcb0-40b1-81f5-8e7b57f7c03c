<?php

namespace App\Filament\Pages;

use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Exports\RegistrationTrendExport;
use App\Exports\RegistrationSourceExport;
use App\Exports\RegistrationGenderExport;
use App\Exports\RegistrationGradeExport;
use App\Exports\RegistrationOrganizationExport;
use App\Exports\RegistrationActivityExport;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class RegistrationAnalytics extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationGroup = '报名系统';
    protected static ?string $navigationLabel = '报名分析';
    protected static ?int $navigationSort = 3;
    protected static ?int $navigationGroupSort = 2;

    protected static string $view = 'filament.pages.registration-analytics';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => Carbon::now()->subDays(30)->format('Y-m-d'),
            'end_date' => Carbon::now()->format('Y-m-d'),
            'activity_id' => null,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('开始日期')
                    ->required(),
                DatePicker::make('end_date')
                    ->label('结束日期')
                    ->required(),
                Select::make('activity_id')
                    ->label('活动')
                    ->options(
                        ActivityDetail::with('activity')->get()->mapWithKeys(function($item) {
                            // 格式化活动时间
                            $time = $item->activity_time ? (new Carbon($item->activity_time))->format('Y-m-d') : '';
                            // 优先使用activity.title，其次使用theme
                            $title = $item->activity && $item->activity->title ? $item->activity->title : $item->theme;
                            return [
                                $item->id => $title . ($time ? ('（' . $time . '）') : '')
                            ];
                        })
                    )
                    ->searchable()
                    ->placeholder('全部活动'),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('exportAll')
                ->label('导出全部数据')
                ->action(function () {
                    return Excel::download(new RegistrationTrendExport, '报名分析.xlsx');
                }),
        ];
    }

    public function getViewData(): array
    {
        $query = Registration::query();
        
        // 只统计有效报名
        $query->where('status', true);

        // 应用日期筛选
        if (!empty($this->data['start_date'])) {
            $query->where('created_at', '>=', Carbon::parse($this->data['start_date'])->startOfDay());
        }
        if (!empty($this->data['end_date'])) {
            $query->where('created_at', '<=', Carbon::parse($this->data['end_date'])->endOfDay());
        }

        // 应用活动筛选
        if (!empty($this->data['activity_id'])) {
            $query->where('activity_detail_id', $this->data['activity_id']);
        }

        // 获取报名趋势
        $trendData = (clone $query)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // 获取报名来源分布
        $sourceData = (clone $query)
            ->select('source', DB::raw('COUNT(*) as count'))
            ->groupBy('source')
            ->get();

        // 获取性别比例
        $genderData = (clone $query)
            ->select('gender', DB::raw('COUNT(*) as count'))
            ->groupBy('gender')
            ->get();

        // 获取年级分布
        $gradeData = (clone $query)
            ->select('grade', DB::raw('COUNT(*) as count'))
            ->whereNotNull('grade')
            ->groupBy('grade')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        // 获取单位分布
        $organizationData = (clone $query)
            ->select('organization', DB::raw('COUNT(*) as count'))
            ->whereNotNull('organization')
            ->groupBy('organization')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        // 获取活动报名情况
        $activityQuery = ActivityDetail::query();
        if (!empty($this->data['activity_id'])) {
            $activityQuery->where('id', $this->data['activity_id']);
        }
        $activityData = $activityQuery
            ->with('activity') // 加载activity关系以获取标题
            ->withCount(['registrations' => function($q) {
                // 只统计有效报名
                $q->where('status', true);
                
                if (!empty($this->data['start_date'])) {
                    $q->where('created_at', '>=', Carbon::parse($this->data['start_date'])->startOfDay());
                }
                if (!empty($this->data['end_date'])) {
                    $q->where('created_at', '<=', Carbon::parse($this->data['end_date'])->endOfDay());
                }
            }])
            ->orderBy('registrations_count', 'desc')
            ->limit(10)
            ->get();

        return [
            'trendData' => $trendData,
            'sourceData' => $sourceData,
            'genderData' => $genderData,
            'gradeData' => $gradeData,
            'organizationData' => $organizationData,
            'activityData' => $activityData,
        ];
    }

    public function exportTrend()
    {
        return Excel::download(new RegistrationTrendExport, '报名趋势.xlsx');
    }

    public function exportSource()
    {
        return Excel::download(new RegistrationSourceExport, '报名来源.xlsx');
    }

    public function exportGender()
    {
        return Excel::download(new RegistrationGenderExport, '性别比例.xlsx');
    }

    public function exportGrade()
    {
        return Excel::download(new RegistrationGradeExport, '年级分布.xlsx');
    }

    public function exportOrganization()
    {
        return Excel::download(new RegistrationOrganizationExport, '单位分布.xlsx');
    }

    public function exportActivity()
    {
        return Excel::download(new RegistrationActivityExport, '活动报名情况.xlsx');
    }
}