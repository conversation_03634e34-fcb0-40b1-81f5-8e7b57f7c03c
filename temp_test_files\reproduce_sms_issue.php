<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Models\Activity;
use App\Models\User;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;

echo "=== 复现短信发送问题 ===\n\n";

// 1. 查找一个真实的活动
$activityDetail = ActivityDetail::with(['activity', 'smsTemplate'])->first();
if (!$activityDetail) {
    echo "错误：数据库中没有活动详情记录\n";
    exit(1);
}

echo "找到活动: {$activityDetail->activity->title}\n";
echo "活动详情ID: {$activityDetail->id}\n";
echo "短信模板ID: " . ($activityDetail->sms_template_id ?? '未设置') . "\n";
if ($activityDetail->smsTemplate) {
    echo "短信模板: {$activityDetail->smsTemplate->name} (代码: {$activityDetail->smsTemplate->code})\n";
}
echo "\n";

// 2. 查找一个真实的报名记录
$registration = Registration::where('activity_detail_id', $activityDetail->id)->first();
if (!$registration) {
    echo "该活动没有报名记录，创建一个测试报名记录...\n";
    
    // 创建测试用户（如果不存在）
    $testUser = User::where('email', '<EMAIL>')->first();
    if (!$testUser) {
        $testUser = User::create([
            'name' => '测试用户',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '13800138000'
        ]);
    }
    
    // 创建测试报名记录
    $registration = Registration::create([
        'user_id' => $testUser->id,
        'activity_detail_id' => $activityDetail->id,
        'name' => '测试用户',
        'phone' => '13800138000',
        'email' => '<EMAIL>',
        'status' => 'pending'
    ]);
    
    echo "创建了测试报名记录\n";
}

echo "报名记录ID: {$registration->id}\n";
echo "报名人姓名: {$registration->name}\n";
echo "报名人手机: {$registration->phone}\n";
echo "\n";

// 3. 模拟RegistrationController中的短信发送逻辑
echo "开始模拟短信发送流程...\n\n";

$smsService = new SmsService();

// 验证手机号
if (!$smsService->validateMobile($registration->phone)) {
    echo "❌ 手机号验证失败: {$registration->phone}\n";
    exit(1);
}
echo "✅ 手机号验证通过\n";

// 检查是否使用云片网模板
if ($activityDetail->sms_template_id && $activityDetail->smsTemplate) {
    echo "✅ 活动配置了短信模板\n";
    $templateCode = $activityDetail->smsTemplate->code;
} else {
    echo "⚠️ 活动未配置短信模板，使用默认模板\n";
    $templateCode = 'activity_registration_confirm';
}

echo "使用模板代码: {$templateCode}\n";

// 构建模板数据（复制RegistrationController的逻辑）
function buildTemplateData($registration, $activityDetail, $templateCode) {
    $baseData = [
        'name' => $registration->name,
        'topic' => $activityDetail->activity->title,
        'time' => $activityDetail->activity_time,
        'address' => $activityDetail->address,
        'obj' => $activityDetail->target ?? '全体人员'
    ];

    switch ($templateCode) {
        case 'activity_registration_confirm':
        case 'consultation_notice':
            return [
                'name' => $baseData['name'],
                'topic' => $baseData['topic'],
                'time' => $baseData['time'],
                'address' => $baseData['address'],
                'obj' => $baseData['obj']
            ];
        
        case 'summer_training_success':
            return [
                'name' => $baseData['topic']
            ];
        
        case 'account_approved':
        case 'account_rejected':
            return [
                'name' => $baseData['name']
            ];
        
        default:
            return $baseData;
    }
}

$templateData = buildTemplateData($registration, $activityDetail, $templateCode);

echo "构建的模板数据:\n";
foreach ($templateData as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 尝试发送短信
echo "尝试发送短信...\n";
try {
    $result = $smsService->sendSms(
        $registration->phone,
        $templateCode,
        $templateData
    );
    
    if ($result) {
        echo "✅ 短信发送成功！\n";
    } else {
        echo "❌ 短信发送失败！\n";
        echo "\n可能的原因：\n";
        echo "1. 云片网API密钥问题\n";
        echo "2. 模板不存在或参数不匹配\n";
        echo "3. 网络连接问题\n";
        echo "4. 云片网账户余额不足\n";
    }
} catch (Exception $e) {
    echo "❌ 短信发送异常: " . $e->getMessage() . "\n";
    
    // 分析具体错误
    if (strpos($e->getMessage(), '短信模板不存在') !== false) {
        echo "\n🔍 错误分析：模板不存在\n";
        echo "请检查：\n";
        echo "1. 数据库中是否有模板代码 '{$templateCode}'\n";
        echo "2. 运行: php artisan db:seed --class=ComprehensiveSmsTemplateSeeder\n";
    } elseif (strpos($e->getMessage(), '缺少必需参数') !== false) {
        echo "\n🔍 错误分析：参数不完整\n";
        echo "请检查模板所需参数是否都已提供\n";
    }
}

echo "\n=== 复现测试完成 ===\n";
echo "\n查看详细日志: storage/logs/laravel.log\n";
echo "搜索关键词: '短信发送失败' 或 '短信发送异常'\n";