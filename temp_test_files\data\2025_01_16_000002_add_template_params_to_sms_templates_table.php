<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('sms_templates', function (Blueprint $table) {
            $table->json('template_params')->nullable()->comment('模板参数配置');
            $table->text('param_description')->nullable()->comment('参数说明');
            $table->string('template_category', 50)->default('general')->comment('模板分类');
        });
    }

    public function down()
    {
        Schema::table('sms_templates', function (Blueprint $table) {
            $table->dropColumn(['template_params', 'param_description', 'template_category']);
        });
    }
};