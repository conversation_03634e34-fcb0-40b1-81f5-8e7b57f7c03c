<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== 检查目标对象字段 ===\n";

try {
    // 检查activity_details表的所有字段
    echo "activity_details表的所有字段:\n";
    $columns = Schema::getColumnListing('activity_details');
    foreach ($columns as $column) {
        echo "- {$column}\n";
    }
    
    // 查看activity_details表的实际数据，特别关注目标对象字段
    echo "\n=== 查看activity_details中的目标对象数据 ===\n";
    $activityDetails = DB::table('activity_details')
        ->limit(3)
        ->get();
    
    foreach ($activityDetails as $detail) {
        echo "\nactivity_detail ID: {$detail->id}\n";
        
        // 显示所有字段，特别关注目标对象相关的
        foreach ($detail as $key => $value) {
            if (strpos($key, 'target') !== false || strpos($key, 'obj') !== false || strpos($key, 'audience') !== false) {
                echo "  {$key}: '{$value}'\n";
            }
        }
        
        // 检查是否有target相关字段
        if (property_exists($detail, 'target')) {
            echo "  ✓ 找到target字段: '{$detail->target}'\n";
        }
        if (property_exists($detail, 'target_audience')) {
            echo "  ✓ 找到target_audience字段: '{$detail->target_audience}'\n";
        }
        if (!property_exists($detail, 'target') && !property_exists($detail, 'target_audience')) {
            echo "  ✗ 没有找到target或target_audience字段\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
