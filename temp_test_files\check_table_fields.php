<?php

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\ActivityDetail;

echo "=== activity_details表字段检查 ===\n\n";

try {
    // 获取表结构
    $columns = DB::select('DESCRIBE activity_details');
    
    echo "当前表字段:\n";
    foreach($columns as $column) {
        echo "- {$column->Field} ({$column->Type})";
        if (!empty($column->Comment)) {
            echo " - {$column->Comment}";
        }
        echo "\n";
    }
    
    echo "\n=== SMS模板字段映射分析 ===\n";
    echo "SMS模板占位符需要的字段:\n";
    echo "- #topic# -> 活动主题\n";
    echo "- #time# -> 活动时间\n";
    echo "- #address# -> 活动地址\n";
    echo "- #obj# -> 目标对象\n\n";
    
    echo "当前可用字段映射:\n";
    echo "- #topic# -> topic字段 (新增) 或 theme字段 (原有)\n";
    echo "- #time# -> activity_time字段 (原有)\n";
    echo "- #address# -> address字段 (原有)\n";
    echo "- #obj# -> target_audience字段 (新增) 或 target字段 (原有)\n\n";
    
    // 检查是否有数据
    $count = ActivityDetail::count();
    echo "表中记录数: {$count}\n";
    
    if ($count > 0) {
        $sample = ActivityDetail::first();
        echo "\n示例记录字段值:\n";
        echo "- theme: {$sample->theme}\n";
        echo "- topic: {$sample->topic}\n";
        echo "- activity_time: {$sample->activity_time}\n";
        echo "- address: {$sample->address}\n";
        echo "- target: {$sample->target}\n";
        echo "- target_audience: {$sample->target_audience}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";