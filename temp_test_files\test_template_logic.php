<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;

echo "=== 测试模板数据构建逻辑 ===\n";

try {
    // 获取一个测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到注册记录\n";
        exit;
    }
    
    echo "注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    
    $activityDetail = $registration->activityDetail;
    $activity = $activityDetail->activity;
    
    echo "\n=== 原始数据 ===\n";
    echo "activityDetail->activity_time: '{$activityDetail->activity_time}'\n";
    echo "activityDetail->target_audience: '{$activityDetail->target_audience}'\n";
    echo "activityDetail->target: '{$activityDetail->target}'\n";
    echo "activity->title: '{$activity->title}'\n";
    
    // 复制buildTemplateData的逻辑
    $name = !empty($registration->name) ? $registration->name : "用户";
    
    // #topic# - 活动主题
    $topic = "";
    if (!empty($activityDetail->topic)) {
        $topic = $activityDetail->topic;
    } elseif (!empty($activity->title)) {
        $topic = $activity->title;
    } elseif (!empty($activityDetail->theme)) {
        $topic = $activityDetail->theme;
    } else {
        $topic = "活动通知";
    }
    
    // #time# - 活动时间
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        try {
            $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
        } catch (Exception $e) {
            $time = "待定时间";
        }
    } else {
        $time = "待定时间";
    }
    
    // #address# - 活动地址
    $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
    
    // #obj# - 目标对象
    $obj = "";
    if (!empty($activityDetail->target_audience)) {
        $obj = $activityDetail->target_audience;
    } elseif (!empty($activityDetail->target)) {
        $obj = $activityDetail->target;
    } else {
        $obj = "全体人员";
    }
    
    $templateData = [
        "name" => $name,
        "topic" => $topic,
        "time" => $time,
        "address" => $address,
        "obj" => $obj
    ];
    
    echo "\n=== 构建的模板数据 ===\n";
    foreach ($templateData as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
