<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Models\SmsTemplate;
use App\Services\SmsService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试修复后的短信发送功能 ===\n\n";

// 1. 获取一个测试活动
$activityDetail = ActivityDetail::with(['activity', 'smsTemplate'])
    ->whereNotNull('sms_template_id')
    ->first();

if (!$activityDetail) {
    echo "错误：未找到已配置SMS模板的活动\n";
    exit(1);
}

echo "测试活动信息:\n";
echo "- 活动ID: {$activityDetail->id}\n";
echo "- 活动主题: {$activityDetail->theme}\n";
echo "- 地址: {$activityDetail->address}\n";
echo "- 地址长度: " . mb_strlen($activityDetail->address) . "\n";
echo "- SMS模板ID: {$activityDetail->sms_template_id}\n";

if ($activityDetail->smsTemplate) {
    echo "- SMS模板: {$activityDetail->smsTemplate->name}\n";
    echo "- 模板代码: {$activityDetail->smsTemplate->code}\n";
    echo "- 云片模板ID: {$activityDetail->smsTemplate->yunpian_template_id}\n";
} else {
    echo "- SMS模板: 未配置\n";
}

echo "\n=== 测试参数截断功能 ===\n";

// 2. 模拟buildTemplateData方法
function buildTemplateData($activityDetail) {
    $templateData = [
        "name" => "测试用户",
        "topic" => !empty($activityDetail->topic) ? mb_substr($activityDetail->topic, 0, 20) : "活动通知",
        "time" => !empty($activityDetail->time) ? mb_substr($activityDetail->formatted_time, 0, 20) : "待定时间",
        "address" => !empty($activityDetail->address) ? mb_substr($activityDetail->address, 0, 20) : "待定地点",
        "obj" => !empty($activityDetail->obj) ? mb_substr($activityDetail->obj, 0, 20) : "全体人员",
        "code" => !empty($activityDetail->code) ? mb_substr($activityDetail->code, 0, 20) : "",
        "pwd" => !empty($activityDetail->pwd) ? mb_substr($activityDetail->pwd, 0, 20) : ""
    ];
    
    return $templateData;
}

$testData = buildTemplateData($activityDetail);

echo "截断后的参数:\n";
foreach ($testData as $key => $value) {
    echo "- {$key}: '{$value}' (长度: " . mb_strlen($value) . ")\n";
}

echo "\n=== 测试短信发送 ===\n";

// 3. 测试短信发送
$testMobile = '15701696507'; // 使用日志中的测试号码

try {
    $smsService = new SmsService();
    
    if (!$smsService->validateMobile($testMobile)) {
        echo "错误：手机号码格式不正确\n";
        exit(1);
    }
    
    echo "开始发送短信...\n";
    echo "手机号: {$testMobile}\n";
    echo "模板代码: {$activityDetail->smsTemplate->code}\n";
    echo "参数: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    $result = $smsService->sendSms(
        $testMobile,
        $activityDetail->smsTemplate->code,
        $testData
    );
    
    if ($result) {
        echo "✅ 短信发送成功！\n";
        echo "返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "❌ 短信发送失败！\n";
    }
    
} catch (Exception $e) {
    echo "❌ 短信发送异常: " . $e->getMessage() . "\n";
    echo "异常详情: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 检查最新日志 ===\n";
echo "请查看 storage/logs/laravel.log 文件中的最新日志条目\n";