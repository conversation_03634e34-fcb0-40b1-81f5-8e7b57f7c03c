<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 为友情链接分类表添加缺失字段
        Schema::table('friendship_link_categories', function (Blueprint $table) {
            if (!Schema::hasColumn('friendship_link_categories', 'description')) {
                $table->text('description')->nullable()->after('slug')->comment('分类描述');
            }
            if (!Schema::hasColumn('friendship_link_categories', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('_rgt')->comment('排序');
            }
            if (!Schema::hasColumn('friendship_link_categories', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('sort_order')->comment('是否启用');
            }
        });

        // 为友情链接表添加缺失字段
        Schema::table('friendship_links', function (Blueprint $table) {
            if (!Schema::hasColumn('friendship_links', 'image')) {
                $table->string('image')->nullable()->after('url')->comment('链接图片');
            }
            if (!Schema::hasColumn('friendship_links', 'description')) {
                $table->text('description')->nullable()->after('image')->comment('链接描述');
            }
            if (!Schema::hasColumn('friendship_links', 'title')) {
                $table->string('title')->nullable()->after('description')->comment('链接标题');
            }
            if (!Schema::hasColumn('friendship_links', 'rel')) {
                $table->string('rel')->default('nofollow')->after('title')->comment('链接关系');
            }
            if (!Schema::hasColumn('friendship_links', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('rel')->comment('排序');
            }
            if (!Schema::hasColumn('friendship_links', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('sort_order')->comment('是否启用');
            }
            if (!Schema::hasColumn('friendship_links', 'open_new_window')) {
                $table->boolean('open_new_window')->default(true)->after('is_active')->comment('新窗口打开');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('friendship_link_categories', function (Blueprint $table) {
            $table->dropColumn(['description', 'sort_order', 'is_active']);
        });

        Schema::table('friendship_links', function (Blueprint $table) {
            $table->dropColumn(['image', 'description', 'title', 'rel', 'sort_order', 'is_active', 'open_new_window']);
        });
    }
};
