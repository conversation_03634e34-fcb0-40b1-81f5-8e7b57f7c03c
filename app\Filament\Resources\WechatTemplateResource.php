<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WechatTemplateResource\Pages;
use App\Models\WechatTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class WechatTemplateResource extends Resource
{
    protected static ?string $model = WechatTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationGroup = '系统管理';

    protected static ?string $navigationLabel = '公众号模板';

    protected static ?int $navigationSort = 7;

    protected static ?int $navigationGroupSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('模板名称')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('template_id')
                    ->label('微信模板ID')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('code')
                    ->label('模板代码')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('content')
                    ->label('模板内容')
                    ->required()
                    ->rows(3),
                Forms\Components\Textarea::make('example')
                    ->label('示例内容')
                    ->required()
                    ->rows(3),
                Forms\Components\Textarea::make('description')
                    ->label('描述')
                    ->rows(2),
                Forms\Components\Toggle::make('status')
                    ->label('状态')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('模板名称')
                    ->searchable(),
                Tables\Columns\TextColumn::make('template_id')
                    ->label('微信模板ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('code')
                    ->label('模板代码')
                    ->searchable(),
                Tables\Columns\IconColumn::make('status')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('status')
                    ->label('状态'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWechatTemplates::route('/'),
            'create' => Pages\CreateWechatTemplate::route('/create'),
            'edit' => Pages\EditWechatTemplate::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return '公众号模板';
    }

    public static function getPluralModelLabel(): string
    {
        return '公众号模板';
    }

    public static function getNavigationLabel(): string
    {
        return '公众号模板';
    }
}