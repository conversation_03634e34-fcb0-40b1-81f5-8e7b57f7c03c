<?php

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\SmsTemplate;
use App\Services\SmsService;
use Illuminate\Support\Facades\DB;

echo "=== 简化SMS发送测试 ===\n";

try {
    // 直接查询数据库获取活动信息
    $activity = DB::table('activity_details')
        ->whereNotNull('sms_template_id')
        ->first();
    
    if (!$activity) {
        echo "错误：未找到已配置SMS模板的活动\n";
        exit(1);
    }
    
    echo "测试活动信息：\n";
    echo "- ID: {$activity->id}\n";
    echo "- 主题: {$activity->theme}\n";
    echo "- 时间: {$activity->activity_time}\n";
    echo "- 地址: {$activity->address}\n";
    echo "- 目标: {$activity->target}\n";
    echo "- SMS模板ID: {$activity->sms_template_id}\n";
    
    // 获取SMS模板信息
    $template = DB::table('sms_templates')
        ->where('id', $activity->sms_template_id)
        ->first();
    
    if (!$template) {
        echo "错误：未找到SMS模板\n";
        exit(1);
    }
    
    echo "- 模板名称: {$template->name}\n";
    echo "- 模板代码: {$template->code}\n";
    echo "- 云片模板ID: {$template->yunpian_template_id}\n";
    echo "- 模板参数: {$template->template_params}\n";
    
    echo "\n=== 准备发送测试SMS ===\n";
    
    // 准备SMS参数
    $smsParams = [
        'topic' => $activity->theme,
        'time' => $activity->activity_time,
        'address' => $activity->address,
        'obj' => $activity->target
    ];
    
    echo "SMS参数: " . json_encode($smsParams, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 测试手机号（请替换为真实的测试号码）
    $testPhone = '13800138000';
    
    echo "\n发送到测试号码: {$testPhone}\n";
    
    // 创建SMS服务实例
    $smsService = new SmsService();
    
    // 发送SMS
    $result = $smsService->sendSms(
        $testPhone,
        $template->code,
        $smsParams
    );
    
    echo "\n=== 发送结果 ===\n";
    if ($result === true) {
        echo "✅ SMS发送成功！\n";
        echo "请检查日志文件获取详细信息\n";
    } else {
        echo "❌ SMS发送失败！\n";
        echo "请检查日志文件获取详细错误信息\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";