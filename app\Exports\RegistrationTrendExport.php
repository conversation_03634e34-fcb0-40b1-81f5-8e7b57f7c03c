<?php

namespace App\Exports;

use App\Models\Registration;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RegistrationTrendExport implements FromCollection, WithHeadings, WithTitle
{
    public function collection()
    {
        return Registration::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count')
        )
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->where('status', true) // 只统计有效报名
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    public function headings(): array
    {
        return [
            '日期',
            '报名人数'
        ];
    }

    public function title(): string
    {
        return '报名趋势';
    }
}