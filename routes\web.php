<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuillUploadController;
use App\Http\Controllers\CKEditorUploadController;
use App\Http\Controllers\RegistrationController;
use App\Http\Controllers\HomeController;
use App\Models\DatabaseBackup;
use Illuminate\Support\Facades\Storage;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/home', [HomeController::class, 'index'])->name('home');

Route::get('/test-session', function () {
    session(['foo' => 'bar']);
    return session('foo', 'none');
});

Route::get('/registration/{id}', [RegistrationController::class, 'showForm'])->name('registration.form');
Route::post('/registration/{id}', [RegistrationController::class, 'register'])->name('registration.register');

Route::post('/wangeditor/upload/image', [App\Http\Controllers\WangEditorUploadController::class, 'image']);
Route::post('/wangeditor/upload/video', [App\Http\Controllers\WangEditorUploadController::class, 'video']);
Route::post('/quill/upload/image', [QuillUploadController::class, 'image']);
Route::post('/quill/upload/video', [QuillUploadController::class, 'video']);
Route::post('/ckeditor/upload/image', [CKEditorUploadController::class, 'image']);
Route::post('/ckeditor/upload/file', [CKEditorUploadController::class, 'file']);
Route::post('/ckeditor/upload/video', [CKEditorUploadController::class, 'video']);
Route::post('/ckeditor/upload/audio', [CKEditorUploadController::class, 'audio']);
Route::post('/admin/ckeditor/upload', [CKEditorUploadController::class, 'upload']);
Route::get('/admin/ckeditor/browse', [CKEditorUploadController::class, 'browse']);

// 引入微信相关路由
require __DIR__.'/wechat.php';

Route::get('/test-captcha', function () {
    return captcha_img('flat');
});

// 数据备份下载路由
Route::get('/backup/download/{backup}', function (DatabaseBackup $backup) {
    // 检查权限
    if (!auth()->check() || !auth()->user()->can('view_database::backup')) {
        abort(403);
    }

    // 检查文件是否存在
    if (!$backup->fileExists()) {
        abort(404, '备份文件不存在');
    }

    return Storage::disk('local')->download($backup->file_path, $backup->name . '.sql');
})->name('backup.download')->middleware('auth');
