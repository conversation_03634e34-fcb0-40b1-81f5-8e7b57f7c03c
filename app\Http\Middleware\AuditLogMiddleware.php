<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class AuditLogMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // 记录所有非GET请求和重要的GET请求
        if ($this->shouldLog($request)) {
            try {
                AuditLog::create([
                    'user_id' => Auth::id(),
                    'operated_at' => now(),
                    'operation_type' => $this->getOperationType($request),
                    'module' => $this->getModuleName($request->path()),
                    'target_id' => $this->getTargetId($request),
                    'request_ip' => $request->ip(),
                    'request_url' => $request->fullUrl(),
                    'request_data' => $this->getRequestData($request),
                    'status' => $this->getStatus($response),
                    'error_message' => $this->getErrorMessage($response),
                    'remark' => $this->generateRemark($request),
                ]);
            } catch (\Exception $e) {
                // 记录日志失败不应该影响正常请求
                \Log::error('审计日志记录失败: ' . $e->getMessage());
            }
        }

        return $response;
    }

    /**
     * 判断是否需要记录日志
     */
    private function shouldLog(Request $request): bool
    {
        $path = $request->path();
        
        // 排除不需要记录的路径
        $excludePaths = [
            'livewire',
            '_debugbar',
            'telescope',
            'horizon',
            'css',
            'js',
            'images',
            'favicon.ico',
        ];
        
        foreach ($excludePaths as $excludePath) {
            if (Str::contains($path, $excludePath)) {
                return false;
            }
        }
        
        // 如果用户未登录，记录登录、注册、密码重置等操作
        if (!Auth::check()) {
            $guestPaths = ['login', 'register', 'password', 'registration'];
            foreach ($guestPaths as $guestPath) {
                if (Str::contains($path, $guestPath)) {
                    return true;
                }
            }
            return false;
        }

        // 记录所有非GET请求（包括前台和后台）
        if (!$request->isMethod('GET')) {
            return true;
        }

        // 记录重要的GET请求
        $importantPaths = [
            // 后台管理相关
            'admin/users',
            'admin/audit-logs', 
            'admin/settings',
            'admin/roles',
            'admin/permissions',
            'admin/articles',
            'admin/categories',
            'admin/posts',
            'admin/comments',
            'admin/activities',
            'admin/activity-details',
            'admin/registrations',
            'admin/media-files',
            'admin/friendship-links',
            'admin/friendship-link-categories',
            'admin/tags',
            'admin/database-backups',
            'admin/backup-schedules',
            'admin/sms-templates',
            'admin/wechat-templates',
            'admin/system-settings',
            'admin/organizations',
            'admin/focus-images',
            'admin/achievement-categories',
            'admin/achievement-centers',
            'admin/point-centers',
            // 导出下载相关
            'export',
            'download',
            'backup/download',
            // 前台重要操作
            'registration/',
            'api/v1/activities',
            'api/v1/friendship-links',
            // 文件上传相关
            'wangeditor/upload',
            'quill/upload',
            'ckeditor/upload',
        ];

        foreach ($importantPaths as $importantPath) {
            if (Str::contains($path, $importantPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 根据请求获取操作类型
     */
    private function getOperationType(Request $request): string
    {
        $method = $request->method();
        $path = $request->path();

        // 特殊路径的操作类型判断
        if (Str::contains($path, 'export')) {
            return 'EXPORT';
        }
        if (Str::contains($path, 'import')) {
            return 'IMPORT';
        }
        if (Str::contains($path, 'download')) {
            return 'DOWNLOAD';
        }

        return match ($method) {
            'POST' => 'CREATE',
            'PUT', 'PATCH' => 'UPDATE',
            'DELETE' => 'DELETE',
            'GET' => 'VIEW',
            default => 'OTHER',
        };
    }

    /**
     * 根据请求路径获取模块名称
     */
    private function getModuleName(string $path): string
    {
        $moduleMap = [
            // 后台管理模块
            'admin/users' => '用户管理',
            'admin/articles' => '文章管理',
            'admin/categories' => '分类管理',
            'admin/posts' => '帖子管理',
            'admin/comments' => '评论管理',
            'admin/activities' => '活动管理',
            'admin/activity-details' => '活动详情管理',
            'admin/registrations' => '报名管理',
            'admin/audit-logs' => '审计日志',
            'admin/settings' => '系统设置',
            'admin/media-files' => '媒体文件',
            'admin/friendship-links' => '友情链接',
            'admin/friendship-link-categories' => '友情链接分类',
            'admin/tags' => '标签管理',
            'admin/roles' => '角色管理',
            'admin/permissions' => '权限管理',
            'admin/database-backups' => '数据库备份',
            'admin/backup-schedules' => '备份计划',
            'admin/sms-templates' => '短信模板',
            'admin/wechat-templates' => '微信模板',
            'admin/system-settings' => '系统配置',
            'admin/organizations' => '组织机构管理',
            'admin/focus-images' => '焦点图管理',
            'admin/achievement-categories' => '成就分类管理',
            'admin/achievement-centers' => '成就中心',
            'admin/point-centers' => '积分中心',
            // 认证相关
            'login' => '系统登录',
            'logout' => '系统登出',
            'register' => '用户注册',
            'password' => '密码管理',
            // 前台功能模块
            'registration/' => '活动报名',
            'home' => '首页访问',
            // API接口模块
            'api/v1/activities' => 'API-活动接口',
            'api/v1/friendship-links' => 'API-友情链接',
            // 文件上传模块
            'wangeditor/upload' => '富文本上传',
            'quill/upload' => 'Quill编辑器上传',
            'ckeditor/upload' => 'CKEditor上传',
            // 文件下载模块
            'backup/download' => '备份下载',
            'export' => '数据导出',
            'download' => '文件下载',
        ];

        foreach ($moduleMap as $pattern => $name) {
            if (Str::contains($path, $pattern)) {
                return $name;
            }
        }

        // 默认解析
        $segments = explode('/', trim($path, '/'));
        
        // API路由解析
        if (count($segments) >= 3 && $segments[0] === 'api' && $segments[1] === 'v1') {
            return 'API-' . ucfirst($segments[2]) . '接口';
        }
        
        // 后台管理路由解析
        if (count($segments) >= 2 && $segments[0] === 'admin') {
            return ucfirst($segments[1]) . '管理';
        }
        
        // 前台路由解析
        if (count($segments) >= 1) {
            return ucfirst($segments[0]) . '模块';
        }

        return '未知模块';
    }

    /**
     * 从请求中获取目标ID
     */
    private function getTargetId(Request $request): ?string
    {
        // 常见的ID参数名
        $idParams = [
            'id', 'user', 'article', 'post', 'category', 'tag',
            'activity', 'registration', 'comment', 'media',
            'friendship_link', 'backup', 'template', 'setting',
            'audit_log', 'organization', 'focus_image'
        ];

        // 首先从路由参数中获取
        foreach ($idParams as $param) {
            $value = $request->route($param);
            if ($value && (is_numeric($value) || is_string($value))) {
                return (string) $value;
            }
        }

        // 然后从请求数据中获取单个ID
        foreach ($idParams as $param) {
            $value = $request->input($param);
            if ($value && (is_numeric($value) || is_string($value))) {
                return (string) $value;
            }
        }

        // 处理批量操作的IDs数组
        $idsArray = $request->input('ids');
        if (is_array($idsArray) && !empty($idsArray)) {
            // 过滤并转换为字符串数组
            $validIds = array_filter($idsArray, function($id) {
                return is_numeric($id) || is_string($id);
            });
            if (!empty($validIds)) {
                return implode(',', array_map('strval', $validIds));
            }
        }

        // 处理Filament的records数组（用于批量操作）
        $records = $request->input('records');
        if (is_array($records) && !empty($records)) {
            $recordIds = [];
            foreach ($records as $record) {
                if (is_array($record) && isset($record['id'])) {
                    $recordIds[] = (string) $record['id'];
                } elseif (is_numeric($record) || is_string($record)) {
                    $recordIds[] = (string) $record;
                }
            }
            if (!empty($recordIds)) {
                return implode(',', $recordIds);
            }
        }

        // 处理选中的记录IDs（Filament表格批量操作）
        $selectedRecords = $request->input('selectedRecords');
        if (is_array($selectedRecords) && !empty($selectedRecords)) {
            $validIds = array_filter($selectedRecords, function($id) {
                return is_numeric($id) || is_string($id);
            });
            if (!empty($validIds)) {
                return implode(',', array_map('strval', $validIds));
            }
        }

        return null;
    }

    /**
     * 获取请求数据（过滤敏感信息）
     */
    private function getRequestData(Request $request): string
    {
        $data = $request->all();
        
        // 过滤敏感字段
        $sensitiveFields = [
            'password', 'password_confirmation', 'current_password', 'new_password',
            'token', 'api_key', 'secret', 'private_key', 'access_token',
            '_token', 'csrf_token'
        ];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***';
            }
        }
        
        // 限制数据大小，避免过大的请求数据
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        if (strlen($jsonData) > 2000) {
            $data = ['message' => '请求数据过大，已省略显示'];
            $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        
        return $jsonData;
    }

    /**
     * 获取响应状态
     */
    private function getStatus(Response $response): string
    {
        if ($response->isSuccessful()) {
            return 'SUCCESS';
        } elseif ($response->isClientError() || $response->isServerError()) {
            return 'FAILED';
        } else {
            return 'ERROR';
        }
    }

    /**
     * 获取错误信息
     */
    private function getErrorMessage(Response $response): ?string
    {
        if (!$response->isSuccessful()) {
            $statusCode = $response->getStatusCode();
            $statusText = Response::$statusTexts[$statusCode] ?? '未知错误';
            return "HTTP {$statusCode}: {$statusText}";
        }
        
        return null;
    }

    /**
     * 生成操作备注
     */
    private function generateRemark(Request $request): string
    {
        $method = $request->method();
        $path = $request->path();
        $operationType = $this->getOperationType($request);
        $moduleName = $this->getModuleName($path);
        
        $user = Auth::user();
        $userName = $user ? $user->name : '访客';
        
        return "{$userName}在{$moduleName}模块执行了{$operationType}操作";
    }
}