<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use App\Models\Setting;

class SmsSetting extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static ?string $navigationGroup = '系统管理';
    protected static ?string $navigationLabel = '短信配置';
    protected static ?int $navigationSort = 3;
    protected static ?int $navigationGroupSort = 4;

    protected static string $view = 'filament.pages.sms-setting';

    public array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'sms_yunpian_api_key' => Setting::getValue('sms_yunpian_api_key', ''),
            'sms_yunpian_sign' => Setting::getValue('sms_yunpian_sign', ''),
        ]);
    }

    public function getTitle(): string
    {
        return '短信配置';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('云片网短信参数')
                    ->schema([
                        TextInput::make('sms_yunpian_api_key')
                            ->label('API 密钥')
                            ->required(),
                        TextInput::make('sms_yunpian_sign')
                            ->label('短信签名')
                            ->required(),
                    ]),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        foreach ($this->data as $key => $value) {
            Setting::setValue($key, $value);
        }
        Notification::make()
            ->title('保存成功')
            ->success()
            ->send();
    }
}