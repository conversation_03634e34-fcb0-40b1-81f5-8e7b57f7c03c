<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            // 将time字段从string改为datetime
            $table->dropColumn('time');
        });
        
        Schema::table('activity_details', function (Blueprint $table) {
            $table->datetime('time')->nullable()->comment('活动时间 - 直接对应云片网#time#参数');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            // 回滚：将time字段从datetime改回string
            $table->dropColumn('time');
        });
        
        Schema::table('activity_details', function (Blueprint $table) {
            $table->string('time')->nullable()->comment('活动时间 - 直接对应云片网#time#参数');
        });
    }
};
