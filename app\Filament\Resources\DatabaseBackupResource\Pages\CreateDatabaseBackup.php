<?php

namespace App\Filament\Resources\DatabaseBackupResource\Pages;

use App\Filament\Resources\DatabaseBackupResource;
use App\Services\DatabaseBackupService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateDatabaseBackup extends CreateRecord
{
    protected static string $resource = DatabaseBackupResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        $data['status'] = \App\Models\DatabaseBackup::STATUS_PENDING;
        
        return $data;
    }

    protected function afterCreate(): void
    {
        $backupService = app(DatabaseBackupService::class);
        
        try {
            // 异步执行备份
            $backupService->executeBackup($this->record);
            
            Notification::make()
                ->title('备份任务已创建')
                ->body("备份 '{$this->record->name}' 正在后台执行")
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('备份执行失败')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
