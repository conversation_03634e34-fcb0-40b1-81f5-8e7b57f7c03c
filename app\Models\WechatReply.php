<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class WechatReply extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'wechat_account_id',
        'name',
        'type',
        'trigger_type',
        'keywords',
        'match_type',
        'reply_type',
        'reply_content',
        'reply_media_id',
        'reply_url',
        'reply_title',
        'reply_description',
        'reply_thumb_media_id',
        'is_active',
        'priority',
        'usage_count',
        'last_used_at',
    ];

    protected $casts = [
        'keywords' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
    ];

    /**
     * 回复类型常量
     */
    const TYPE_SUBSCRIBE = 'subscribe';      // 关注回复
    const TYPE_DEFAULT = 'default';          // 默认回复
    const TYPE_KEYWORD = 'keyword';          // 关键词回复
    const TYPE_EVENT = 'event';              // 事件回复

    /**
     * 触发类型常量
     */
    const TRIGGER_SUBSCRIBE = 'subscribe';   // 关注触发
    const TRIGGER_KEYWORD = 'keyword';       // 关键词触发
    const TRIGGER_DEFAULT = 'default';       // 默认触发
    const TRIGGER_CLICK = 'click';           // 菜单点击触发
    const TRIGGER_SCAN = 'scan';             // 扫码触发

    /**
     * 匹配类型常量
     */
    const MATCH_EXACT = 'exact';             // 完全匹配
    const MATCH_PARTIAL = 'partial';         // 部分匹配
    const MATCH_REGEX = 'regex';             // 正则匹配

    /**
     * 回复内容类型常量
     */
    const REPLY_TYPE_TEXT = 'text';          // 文本回复
    const REPLY_TYPE_IMAGE = 'image';        // 图片回复
    const REPLY_TYPE_VOICE = 'voice';        // 语音回复
    const REPLY_TYPE_VIDEO = 'video';        // 视频回复
    const REPLY_TYPE_MUSIC = 'music';        // 音乐回复
    const REPLY_TYPE_NEWS = 'news';          // 图文回复

    /**
     * 关联微信账号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAccount::class);
    }

    /**
     * 获取回复类型标签
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            self::TYPE_SUBSCRIBE => '关注回复',
            self::TYPE_DEFAULT => '默认回复',
            self::TYPE_KEYWORD => '关键词回复',
            self::TYPE_EVENT => '事件回复',
            default => '未知类型'
        };
    }

    /**
     * 获取触发类型标签
     */
    public function getTriggerTypeLabel(): string
    {
        return match($this->trigger_type) {
            self::TRIGGER_SUBSCRIBE => '关注触发',
            self::TRIGGER_KEYWORD => '关键词触发',
            self::TRIGGER_DEFAULT => '默认触发',
            self::TRIGGER_CLICK => '菜单点击触发',
            self::TRIGGER_SCAN => '扫码触发',
            default => '未知触发类型'
        };
    }

    /**
     * 获取匹配类型标签
     */
    public function getMatchTypeLabel(): string
    {
        return match($this->match_type) {
            self::MATCH_EXACT => '完全匹配',
            self::MATCH_PARTIAL => '部分匹配',
            self::MATCH_REGEX => '正则匹配',
            default => '未知匹配类型'
        };
    }

    /**
     * 获取回复内容类型标签
     */
    public function getReplyTypeLabel(): string
    {
        return match($this->reply_type) {
            self::REPLY_TYPE_TEXT => '文本回复',
            self::REPLY_TYPE_IMAGE => '图片回复',
            self::REPLY_TYPE_VOICE => '语音回复',
            self::REPLY_TYPE_VIDEO => '视频回复',
            self::REPLY_TYPE_MUSIC => '音乐回复',
            self::REPLY_TYPE_NEWS => '图文回复',
            default => '未知回复类型'
        };
    }

    /**
     * 获取所有回复类型
     */
    public static function getReplyTypes(): array
    {
        return [
            self::TYPE_SUBSCRIBE => '关注回复',
            self::TYPE_DEFAULT => '默认回复',
            self::TYPE_KEYWORD => '关键词回复',
            self::TYPE_EVENT => '事件回复',
        ];
    }

    /**
     * 获取所有匹配类型
     */
    public static function getMatchTypes(): array
    {
        return [
            self::MATCH_EXACT => '完全匹配',
            self::MATCH_PARTIAL => '部分匹配',
            self::MATCH_REGEX => '正则匹配',
        ];
    }

    /**
     * 获取所有回复内容类型
     */
    public static function getReplyContentTypes(): array
    {
        return [
            self::REPLY_TYPE_TEXT => '文本回复',
            self::REPLY_TYPE_IMAGE => '图片回复',
            self::REPLY_TYPE_VOICE => '语音回复',
            self::REPLY_TYPE_VIDEO => '视频回复',
            self::REPLY_TYPE_MUSIC => '音乐回复',
            self::REPLY_TYPE_NEWS => '图文回复',
        ];
    }

    /**
     * 检查关键词是否匹配
     */
    public function matchesKeyword(string $keyword): bool
    {
        if (empty($this->keywords)) {
            return false;
        }

        foreach ($this->keywords as $ruleKeyword) {
            switch ($this->match_type) {
                case self::MATCH_EXACT:
                    if (strtolower(trim($keyword)) === strtolower(trim($ruleKeyword))) {
                        return true;
                    }
                    break;
                case self::MATCH_PARTIAL:
                    if (str_contains(strtolower($keyword), strtolower($ruleKeyword))) {
                        return true;
                    }
                    break;
                case self::MATCH_REGEX:
                    if (preg_match('/' . $ruleKeyword . '/i', $keyword)) {
                        return true;
                    }
                    break;
            }
        }

        return false;
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * 作用域：启用的回复规则
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按优先级排序
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * 作用域：关键词回复
     */
    public function scopeKeywordReplies($query)
    {
        return $query->where('type', self::TYPE_KEYWORD);
    }

    /**
     * 作用域：关注回复
     */
    public function scopeSubscribeReplies($query)
    {
        return $query->where('type', self::TYPE_SUBSCRIBE);
    }

    /**
     * 作用域：默认回复
     */
    public function scopeDefaultReplies($query)
    {
        return $query->where('type', self::TYPE_DEFAULT);
    }

    /**
     * 转换为微信回复格式
     */
    public function toWechatReply(): array
    {
        $reply = [
            'type' => $this->reply_type,
        ];

        switch ($this->reply_type) {
            case self::REPLY_TYPE_TEXT:
                $reply['content'] = $this->reply_content;
                break;
            case self::REPLY_TYPE_IMAGE:
                $reply['media_id'] = $this->reply_media_id;
                break;
            case self::REPLY_TYPE_VOICE:
                $reply['media_id'] = $this->reply_media_id;
                break;
            case self::REPLY_TYPE_VIDEO:
                $reply['media_id'] = $this->reply_media_id;
                $reply['title'] = $this->reply_title;
                $reply['description'] = $this->reply_description;
                break;
            case self::REPLY_TYPE_MUSIC:
                $reply['title'] = $this->reply_title;
                $reply['description'] = $this->reply_description;
                $reply['music_url'] = $this->reply_url;
                $reply['thumb_media_id'] = $this->reply_thumb_media_id;
                break;
            case self::REPLY_TYPE_NEWS:
                $reply['articles'] = [[
                    'title' => $this->reply_title,
                    'description' => $this->reply_description,
                    'url' => $this->reply_url,
                    'pic_url' => $this->reply_media_id, // 这里存储的是图片URL
                ]];
                break;
        }

        return $reply;
    }
}