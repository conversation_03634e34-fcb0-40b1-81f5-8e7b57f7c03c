<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->json('content');
            $table->string('summary')->nullable();
            $table->string('cover_image')->nullable();
            $table->json('meta_data')->nullable();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->timestamps();
            $table->softDeletes();
            $table->index('title');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
}; 