<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityDetail;
use Carbon\Carbon;

class ActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建活动
        $activities = [
            [
                'title' => '2024年春季教学研讨会',
                'status' => 'published',
                'publish_time' => now()->subDays(10),
                'qrcode_url' => 'https://example.com/qr/spring-seminar',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example1',
                'views_count' => 156,
            ],
            [
                'title' => '学生科技创新大赛',
                'status' => 'published',
                'publish_time' => now()->subDays(5),
                'qrcode_url' => 'https://example.com/qr/tech-competition',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example2',
                'views_count' => 89,
            ],
            [
                'title' => '家长开放日活动',
                'status' => 'published',
                'publish_time' => now()->subDays(3),
                'qrcode_url' => 'https://example.com/qr/parent-day',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example3',
                'views_count' => 234,
            ],
            [
                'title' => '暑期夏令营报名',
                'status' => 'draft',
                'publish_time' => now()->addDays(7),
                'qrcode_url' => 'https://example.com/qr/summer-camp',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example4',
                'views_count' => 0,
            ],
        ];

        foreach ($activities as $activityData) {
            $activity = Activity::firstOrCreate(
                ['title' => $activityData['title']],
                $activityData
            );

            // 为每个活动创建详细信息
            if ($activity->wasRecentlyCreated) {
                $this->createActivityDetails($activity);
            }
        }

        $this->command->info('活动数据创建成功！');
        $this->command->info('- 创建了 4 个活动');
        $this->command->info('- 为每个活动创建了详细信息');
    }

    /**
     * 为活动创建详细信息
     */
    private function createActivityDetails($activity)
    {
        $details = [
            [
                'theme' => $activity->title . ' - 第一场',
                'deadline' => Carbon::now()->addDays(30),
                'registration_deadline' => Carbon::now()->addDays(25),
                'status' => 1,
                'quota' => 100,
                'target' => '全体教师',
                'target_audience' => '小学教师',
                'current_count' => rand(10, 50),
                'activity_time' => Carbon::now()->addDays(35),
                'fee' => '免费',
                'reminder' => '请准时参加，携带相关材料',
                'registration_method' => '在线报名',
                'address' => '学校多功能厅',
                'sms_template' => '您好，您已成功报名参加{活动名称}，时间：{活动时间}，地点：{活动地点}。',
            ],
            [
                'theme' => $activity->title . ' - 第二场',
                'deadline' => Carbon::now()->addDays(45),
                'registration_deadline' => Carbon::now()->addDays(40),
                'status' => 1,
                'quota' => 80,
                'target' => '骨干教师',
                'target_audience' => '中学教师',
                'current_count' => rand(5, 30),
                'activity_time' => Carbon::now()->addDays(50),
                'fee' => '50元',
                'reminder' => '请提前15分钟到场',
                'registration_method' => '现场报名',
                'address' => '学校会议室',
                'sms_template' => '您好，您已成功报名参加{活动名称}，时间：{活动时间}，地点：{活动地点}。',
            ],
        ];

        foreach ($details as $detailData) {
            ActivityDetail::create(array_merge($detailData, [
                'activity_id' => $activity->id
            ]));
        }
    }
}
