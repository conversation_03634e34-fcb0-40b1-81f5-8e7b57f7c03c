<?php

namespace App\Filament\Resources\FriendshipLinkResource\Pages;

use App\Filament\Resources\FriendshipLinkResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFriendshipLink extends EditRecord
{
    protected static string $resource = FriendshipLinkResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
