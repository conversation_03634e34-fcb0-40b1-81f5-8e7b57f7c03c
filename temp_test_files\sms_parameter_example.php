<?php

/**
 * 短信模板参数传递示例
 * 演示如何在系统中使用短信模板和传递参数
 */

require_once __DIR__ . '/vendor/autoload.php';

// 初始化Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\SmsService;
use App\Models\SmsTemplate;
use App\Models\ActivityDetail;

echo "=== 短信模板参数传递示例 ===\n\n";

// 1. 直接使用模板代码发送短信
echo "1. 直接使用模板代码发送短信:\n";
$smsService = new SmsService();

// 验证码短信示例
$verificationParams = [
    'code' => '123456'  // 验证码参数
];

echo "发送验证码短信:\n";
echo "模板代码: verification_code\n";
echo "参数: " . json_encode($verificationParams, JSON_UNESCAPED_UNICODE) . "\n";
// $result = $smsService->sendSms('13800138000', 'verification_code', $verificationParams);
// echo "发送结果: " . ($result ? '成功' : '失败') . "\n\n";
echo "(演示模式，未实际发送)\n\n";

// 活动通知短信示例
$activityParams = [
    'topic' => '心理健康讲座',
    'time' => '2024年6月15日 14:00',
    'address' => '嘉定区教育学院报告厅',
    'obj' => '全体教师'
];

echo "发送活动通知短信:\n";
echo "模板代码: activity_notification\n";
echo "参数: " . json_encode($activityParams, JSON_UNESCAPED_UNICODE) . "\n";
// $result = $smsService->sendSms('13800138000', 'activity_notification', $activityParams);
// echo "发送结果: " . ($result ? '成功' : '失败') . "\n\n";
echo "(演示模式，未实际发送)\n\n";

// 2. 通过活动详情自动构建参数
echo "2. 通过活动详情自动构建参数:\n";

// 模拟活动详情数据
class MockActivityDetail {
    public $theme = '心理健康讲座';
    public $activity_time = '2024-06-15 14:00:00';
    public $address = '嘉定区教育学院报告厅';
    public $target = '全体教师';
    public $sms_template_id = 3; // 假设活动通知模板ID为3
    
    /**
     * 根据活动信息自动构建短信参数
     */
    public function buildSmsParams($userName = null) {
        $params = [];
        
        // 根据模板需要的参数构建数据
        if ($this->sms_template_id) {
            $template = SmsTemplate::find($this->sms_template_id);
            if ($template) {
                $requiredParams = $template->getTemplateParamsList();
                
                foreach ($requiredParams as $key => $description) {
                    switch ($key) {
                        case 'name':
                            $params['name'] = $userName ?: '用户';
                            break;
                        case 'topic':
                            $params['topic'] = $this->theme;
                            break;
                        case 'time':
                            $params['time'] = date('Y年m月d日 H:i', strtotime($this->activity_time));
                            break;
                        case 'address':
                            $params['address'] = $this->address;
                            break;
                        case 'obj':
                            $params['obj'] = $this->target;
                            break;
                        case 'code':
                            $params['code'] = rand(100000, 999999); // 生成6位验证码
                            break;
                        case 'pwd':
                            $params['pwd'] = substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyz'), 0, 8);
                            break;
                    }
                }
            }
        }
        
        return $params;
    }
}

$activity = new MockActivityDetail();
$autoParams = $activity->buildSmsParams('张老师');

echo "活动信息:\n";
echo "- 主题: {$activity->theme}\n";
echo "- 时间: {$activity->activity_time}\n";
echo "- 地点: {$activity->address}\n";
echo "- 对象: {$activity->target}\n\n";

echo "自动构建的短信参数:\n";
echo json_encode($autoParams, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

// 3. 在控制器中的使用示例
echo "3. 在控制器中的使用示例:\n";
echo "```php\n";
echo "// 在ActivityController中发送活动通知\n";
echo "public function sendActivityNotification(ActivityDetail \$activity, \$userPhone, \$userName) {\n";
echo "    \$smsService = new SmsService();\n";
echo "    \n";
echo "    // 构建参数\n";
echo "    \$params = [\n";
echo "        'name' => \$userName,\n";
echo "        'topic' => \$activity->theme,\n";
echo "        'time' => \$activity->activity_time->format('Y年m月d日 H:i'),\n";
echo "        'address' => \$activity->address,\n";
echo "        'obj' => \$activity->target\n";
echo "    ];\n";
echo "    \n";
echo "    // 发送短信\n";
echo "    \$result = \$smsService->sendSms(\$userPhone, 'activity_notification', \$params);\n";
echo "    \n";
echo "    return \$result;\n";
echo "}\n";
echo "```\n\n";

// 4. 可用的模板变量说明
echo "4. 可用的模板变量说明:\n";
$templates = [
    'verification_code' => ['code' => '验证码'],
    'account_approved' => ['name' => '用户姓名'],
    'account_rejected' => ['name' => '用户姓名'],
    'account_created' => ['name' => '用户姓名', 'pwd' => '初始密码'],
    'activity_notification' => ['topic' => '活动主题', 'time' => '活动时间', 'address' => '活动地点', 'obj' => '参与对象'],
    'activity_registration_confirm' => ['name' => '参与者姓名', 'topic' => '活动主题', 'time' => '活动时间', 'address' => '活动地点'],
    'training_registration_success' => ['name' => '课程名称']
];

foreach ($templates as $code => $params) {
    echo "- {$code}:\n";
    foreach ($params as $key => $desc) {
        echo "  #{$key}# - {$desc}\n";
    }
    echo "\n";
}

echo "=== 使用说明 ===\n";
echo "1. 在后台管理中，为活动详情设置 sms_template_id\n";
echo "2. 在代码中调用 SmsService::sendSms() 方法\n";
echo "3. 传递对应的参数数组，键名对应模板中的变量\n";
echo "4. 系统会自动验证参数完整性并发送短信\n";
echo "\n注意：实际发送需要配置有效的云片网API密钥\n";