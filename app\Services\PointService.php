<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserPoint;
use App\Models\UserBehavior;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class PointService
{
    /**
     * 记录用户登录积分
     */
    public function recordLoginPoints(User $user)
    {
        // 只有实名用户才能获得积分
        if (!$user->isVerified()) {
            return false;
        }

        $today = Carbon::today();
        
        // 检查今天是否已经获得过登录积分
        $existingLogin = UserBehavior::where('user_id', $user->id)
            ->where('action', UserBehavior::ACTION_LOGIN)
            ->where('behavior_date', $today)
            ->exists();

        if ($existingLogin) {
            return false;
        }

        // 记录登录行为
        UserBehavior::create([
            'user_id' => $user->id,
            'action' => UserBehavior::ACTION_LOGIN,
            'behavior_date' => $today,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return true;
    }

    /**
     * 记录浏览积分（需要停留超过30秒）
     */
    public function recordBrowsePoints(User $user, $targetType, $targetId, $duration, $url = null)
    {
        // 只有实名用户才能获得积分
        if (!$user->isVerified()) {
            return false;
        }

        // 停留时间必须超过30秒
        if ($duration < 30) {
            return false;
        }

        $today = Carbon::today();
        $oneHourAgo = Carbon::now()->subHour();

        // 检查1小时内是否已经在同一页面获得过积分
        $recentBrowse = UserBehavior::where('user_id', $user->id)
            ->where('action', UserBehavior::ACTION_BROWSE)
            ->where('target_type', $targetType)
            ->where('target_id', $targetId)
            ->where('created_at', '>=', $oneHourAgo)
            ->exists();

        if ($recentBrowse) {
            return false;
        }

        // 记录浏览行为
        UserBehavior::create([
            'user_id' => $user->id,
            'action' => UserBehavior::ACTION_BROWSE,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'url' => $url,
            'duration' => $duration,
            'behavior_date' => $today,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        // 检查今天是否已经获得过登录积分（首次登录+浏览才给积分）
        $hasLoginToday = UserBehavior::where('user_id', $user->id)
            ->where('action', UserBehavior::ACTION_LOGIN)
            ->where('behavior_date', $today)
            ->exists();

        if ($hasLoginToday) {
            // 给予1积分
            UserPoint::create([
                'user_id' => $user->id,
                'points' => 1,
                'type' => UserPoint::TYPE_BROWSE,
                'source' => '浏览页面超过30秒',
                'source_id' => $targetId,
                'earned_date' => $today,
            ]);
        }

        return true;
    }

    /**
     * 记录视频观看积分
     */
    public function recordVideoPoints(User $user, $videoId, $duration, $url = null)
    {
        // 只有实名用户才能获得积分
        if (!$user->isVerified()) {
            return false;
        }

        $today = Carbon::today();
        $oneHourAgo = Carbon::now()->subHour();

        // 检查1小时内是否已经观看过同一视频获得积分
        $recentVideo = UserBehavior::where('user_id', $user->id)
            ->where('action', UserBehavior::ACTION_VIDEO_VIEW)
            ->where('target_id', $videoId)
            ->where('created_at', '>=', $oneHourAgo)
            ->exists();

        if ($recentVideo) {
            return false;
        }

        // 记录视频观看行为
        UserBehavior::create([
            'user_id' => $user->id,
            'action' => UserBehavior::ACTION_VIDEO_VIEW,
            'target_type' => UserBehavior::TARGET_VIDEO,
            'target_id' => $videoId,
            'url' => $url,
            'duration' => $duration,
            'behavior_date' => $today,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        // 给予5积分
        UserPoint::create([
            'user_id' => $user->id,
            'points' => 5,
            'type' => UserPoint::TYPE_VIDEO,
            'source' => '观看视频内容',
            'source_id' => $videoId,
            'earned_date' => $today,
        ]);

        return true;
    }

    /**
     * 获取用户排名
     */
    public function getUserRanking(User $user, $type = 'total')
    {
        $cacheKey = "user_ranking_{$user->id}_{$type}";
        
        return Cache::remember($cacheKey, 300, function () use ($user, $type) {
            $query = User::where('is_verified', true)
                ->whereHas('points');

            switch ($type) {
                case 'month':
                    $query->withSum(['points as total_points' => function ($q) {
                        $q->whereYear('earned_date', now()->year)
                          ->whereMonth('earned_date', now()->month);
                    }], 'points');
                    break;
                case 'quarter':
                    $quarter = ceil(now()->month / 3);
                    $startMonth = ($quarter - 1) * 3 + 1;
                    $endMonth = $quarter * 3;
                    $query->withSum(['points as total_points' => function ($q) use ($startMonth, $endMonth) {
                        $q->whereYear('earned_date', now()->year)
                          ->whereMonth('earned_date', '>=', $startMonth)
                          ->whereMonth('earned_date', '<=', $endMonth);
                    }], 'points');
                    break;
                case 'year':
                    $query->withSum(['points as total_points' => function ($q) {
                        $q->whereYear('earned_date', now()->year);
                    }], 'points');
                    break;
                default: // total
                    $query->withSum('points as total_points', 'points');
                    break;
            }

            $users = $query->orderByDesc('total_points')->get();
            
            foreach ($users as $index => $u) {
                if ($u->id === $user->id) {
                    return $index + 1;
                }
            }
            
            return 0;
        });
    }

    /**
     * 获取地区排名
     */
    public function getRegionRanking($region, $type = 'total')
    {
        $cacheKey = "region_ranking_{$region}_{$type}";
        
        return Cache::remember($cacheKey, 300, function () use ($region, $type) {
            $query = User::where('is_verified', true)
                ->where('region', $region)
                ->whereHas('points');

            switch ($type) {
                case 'month':
                    $query->withSum(['points as total_points' => function ($q) {
                        $q->whereYear('earned_date', now()->year)
                          ->whereMonth('earned_date', now()->month);
                    }], 'points');
                    break;
                case 'quarter':
                    $quarter = ceil(now()->month / 3);
                    $startMonth = ($quarter - 1) * 3 + 1;
                    $endMonth = $quarter * 3;
                    $query->withSum(['points as total_points' => function ($q) use ($startMonth, $endMonth) {
                        $q->whereYear('earned_date', now()->year)
                          ->whereMonth('earned_date', '>=', $startMonth)
                          ->whereMonth('earned_date', '<=', $endMonth);
                    }], 'points');
                    break;
                case 'year':
                    $query->withSum(['points as total_points' => function ($q) {
                        $q->whereYear('earned_date', now()->year);
                    }], 'points');
                    break;
                default: // total
                    $query->withSum('points as total_points', 'points');
                    break;
            }

            $regions = $query->groupBy('region')
                ->selectRaw('region, SUM(total_points) as region_total')
                ->orderByDesc('region_total')
                ->get();
            
            foreach ($regions as $index => $r) {
                if ($r->region === $region) {
                    return $index + 1;
                }
            }
            
            return 0;
        });
    }
}
