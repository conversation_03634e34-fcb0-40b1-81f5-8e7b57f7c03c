<?php

namespace App\Filament\Resources\ActivityDetailResource\Pages;

use App\Filament\Resources\ActivityDetailResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListActivityDetails extends ListRecords
{
    protected static string $resource = ActivityDetailResource::class;
    protected static ?string $title = '活动场次';

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()->label('创建活动场次'),
        ];
    }
} 