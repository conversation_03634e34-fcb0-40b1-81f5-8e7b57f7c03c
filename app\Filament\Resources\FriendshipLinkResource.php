<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FriendshipLinkResource\Pages;
use App\Models\FriendshipLink;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use CodeWithDennis\FilamentSelectTree\SelectTree;

class FriendshipLinkResource extends Resource
{
    protected static ?string $model = FriendshipLink::class;

    protected static ?string $navigationIcon = 'heroicon-o-link';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?string $navigationLabel = '友情链接';

    protected static ?int $navigationSort = 7;

    protected static ?int $navigationGroupSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('链接名称')
                            ->required()
                            ->maxLength(255)
                            ->helperText('友情链接的显示名称'),
                        Forms\Components\TextInput::make('url')
                            ->label('网址地址')
                            ->required()
                            ->url()
                            ->maxLength(255)
                            ->helperText('请输入完整的URL地址，如：https://www.example.com'),
                        SelectTree::make('friendship_link_category_id')
                            ->label('友链分类')
                            ->relationship('category', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('选择友链分类')
                            ->helperText('选择友情链接所属分类'),
                        Forms\Components\FileUpload::make('image')
                            ->label('缩略图')
                            ->image()
                            ->directory('friendship-links')
                            ->disk('public')
                            ->visibility('public')
                            ->maxSize(2048)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
                            ->imageResizeMode('cover')
                            ->imageResizeTargetWidth(120)
                            ->imageResizeTargetHeight(120)
                            ->imageCropAspectRatio('1:1')
                            ->downloadable()
                            ->previewable()
                            ->openable()
                            ->deletable()
                            ->columnSpanFull()
                            ->helperText('建议尺寸：120x120像素，支持jpg、png、gif、webp格式，最大2MB')
                            ->getUploadedFileNameForStorageUsing(
                                fn (\Livewire\Features\SupportFileUploads\TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                    ->prepend(now()->format('YmdHis') . '_'),
                            ),
                        Forms\Components\Textarea::make('description')
                            ->label('链接描述')
                            ->maxLength(500)
                            ->rows(3)
                            ->helperText('友情链接的简要描述'),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true)
                            ->helperText('关闭后该友情链接将不会显示'),
                        Forms\Components\Toggle::make('open_new_window')
                            ->label('新窗口打开')
                            ->default(true)
                            ->helperText('是否在新窗口中打开链接'),
                    ])->columns(2),

                Forms\Components\Section::make('SEO设置')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('链接标题')
                            ->maxLength(255)
                            ->helperText('鼠标悬停时显示的标题'),
                        Forms\Components\Select::make('rel')
                            ->label('链接关系')
                            ->options([
                                'nofollow' => 'nofollow - 不传递权重',
                                'follow' => 'follow - 传递权重',
                                'sponsored' => 'sponsored - 赞助链接',
                                'ugc' => 'ugc - 用户生成内容',
                            ])
                            ->default('nofollow')
                            ->helperText('设置链接的rel属性'),
                    ])->columns(2)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('缩略图')
                    ->disk('public')
                    ->circular()
                    ->height(40)
                    ->width(40)
                    ->visibility('public')
                    ->defaultImageUrl('data:image/svg+xml;base64,' . base64_encode('
                        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="120" height="120" fill="#f3f4f6"/>
                            <rect x="30" y="30" width="60" height="40" fill="#d1d5db" rx="4"/>
                            <circle cx="45" cy="45" r="6" fill="#9ca3af"/>
                            <path d="M30 65l15-10 10 5 25-15v25H30z" fill="#9ca3af"/>
                            <text x="60" y="90" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">友情链接</text>
                        </svg>
                    ')),
                Tables\Columns\TextColumn::make('name')
                    ->label('链接名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('url')
                    ->label('网址地址')
                    ->searchable()
                    ->limit(50)
                    ->copyable()
                    ->copyMessage('网址已复制')
                    ->openUrlInNewTab(),
                Tables\Columns\TextColumn::make('category.name')
                    ->label('所属分类')
                    ->badge()
                    ->default('未分类'),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\IconColumn::make('open_new_window')
                    ->label('新窗口')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('rel')
                    ->label('链接关系')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'nofollow' => 'warning',
                        'follow' => 'success',
                        'sponsored' => 'info',
                        'ugc' => 'gray',
                        default => 'gray',
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('friendship_link_category_id')
                    ->label('友链分类')
                    ->relationship('category', 'name')
                    ->placeholder('全部分类'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('启用')
                    ->falseLabel('禁用')
                    ->placeholder('全部'),
                Tables\Filters\SelectFilter::make('rel')
                    ->label('链接关系')
                    ->options([
                        'nofollow' => 'nofollow',
                        'follow' => 'follow',
                        'sponsored' => 'sponsored',
                        'ugc' => 'ugc',
                    ])
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\Action::make('visit')
                    ->label('访问')
                    ->icon('heroicon-o-arrow-top-right-on-square')
                    ->url(fn (FriendshipLink $record): string => $record->url)
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('toggle_status')
                        ->label('切换状态')
                        ->icon('heroicon-o-arrow-path')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->update(['is_active' => !$record->is_active]);
                            }
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFriendshipLinks::route('/'),
            'create' => Pages\CreateFriendshipLink::route('/create'),
            'edit' => Pages\EditFriendshipLink::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '友情链接';
    }

    public static function getModelLabel(): string
    {
        return '友情链接';
    }

    public static function getPluralModelLabel(): string
    {
        return '友情链接';
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }
}
