<?php

namespace Database\Factories;

use App\Models\Comment;
use App\Models\User;
use App\Models\Article;
use Illuminate\Database\Eloquent\Factories\Factory;

class CommentFactory extends Factory
{
    protected $model = Comment::class;

    public function definition(): array
    {
        return [
            'article_id' => Article::factory(),
            'user_id' => User::factory(),
            'parent_id' => null,
            'author_name' => $this->faker->name(),
            'author_email' => $this->faker->email(),
            'author_ip' => $this->faker->ipv4(),
            'content' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'is_private' => false,
            'meta_data' => json_encode([
                'browser' => $this->faker->userAgent(),
                'platform' => $this->faker->randomElement(['Windows', 'Mac', 'Linux', 'iOS', 'Android'])
            ]),
            '_lft' => 0,
            '_rgt' => 0,
            'depth' => 0,
        ];
    }
} 