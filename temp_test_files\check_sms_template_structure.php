<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== 检查SMS模板表结构 ===\n";

try {
    // 获取表结构
    $columns = Schema::getColumnListing('sms_templates');
    
    echo "sms_templates表的字段:\n";
    foreach ($columns as $column) {
        echo "- {$column}\n";
    }
    
    // 查看实际数据
    echo "\n=== 查看SMS模板数据 ===\n";
    $templates = DB::table('sms_templates')->get();
    
    foreach ($templates as $template) {
        echo "\nID: {$template->id}\n";
        echo "名称: {$template->name}\n";
        
        // 检查可能的内容字段
        $possibleContentFields = ['content', 'template_content', 'message', 'text', 'body'];
        foreach ($possibleContentFields as $field) {
            if (property_exists($template, $field)) {
                echo "{$field}: '" . ($template->$field ?? 'NULL') . "'\n";
            }
        }
        
        // 显示所有字段
        echo "所有字段:\n";
        foreach ($template as $key => $value) {
            echo "  {$key}: '" . ($value ?? 'NULL') . "'\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
