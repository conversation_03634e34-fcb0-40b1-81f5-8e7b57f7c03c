# Activity Detail SMS模板选择功能使用指南

## 功能概述

为了避免每次发送SMS都需要进行数据库操作，我们在Activity Detail（活动场次）管理页面中添加了SMS模板选择功能。现在管理员可以直接在创建或编辑活动场次时选择相应的SMS模板。

## 新增功能

### 1. SMS模板选择字段

在Activity Detail的创建/编辑表单中，新增了"短信模板"选择字段：

- **字段位置**: 在"友情提醒"字段之后，"启用状态"字段之前
- **字段类型**: 下拉选择框，支持搜索
- **显示格式**: 模板名称 (模板代码)
- **帮助文本**: "选择用于发送短信通知的模板"

### 2. 表格列显示

在Activity Detail列表页面中，新增了"短信模板"列：

- **显示内容**: 关联的SMS模板名称
- **未设置时**: 显示"未设置"
- **可切换显示**: 支持隐藏/显示该列
- **支持搜索**: 可以通过模板名称搜索

### 3. 筛选功能

新增了SMS模板筛选器：

- **筛选位置**: 在活动筛选器之后
- **筛选方式**: 通过SMS模板名称筛选
- **支持搜索**: 可以搜索模板名称

## 使用方法

### 创建活动场次时设置SMS模板

1. 进入"活动场次"管理页面
2. 点击"创建"按钮
3. 填写基本信息（活动、主题、时间等）
4. 在"短信模板"字段中选择合适的模板：
   - 点击下拉框查看所有可用模板
   - 使用搜索功能快速找到目标模板
   - 选择格式："模板名称 (模板代码)"
5. 完成其他字段填写并保存

### 编辑现有活动场次的SMS模板

1. 在活动场次列表中找到目标活动
2. 点击"编辑"按钮
3. 修改"短信模板"字段
4. 保存更改

### 查看和筛选

1. **查看模板关联**：在列表页面的"短信模板"列中查看每个活动的模板设置
2. **按模板筛选**：使用"短信模板"筛选器筛选特定模板的活动
3. **搜索功能**：在模板列中使用搜索功能快速定位

## 可用的SMS模板

当前系统中可用的SMS模板包括：

- **嘉定幸福课程身份验证** (jiading_verification)
- **通用验证码** (general_verification)
- **现场咨询通知** (consultation_notice)
- **账号审核通过** (account_approved)
- **账号审核未通过** (account_rejected)
- **账号开通通知** (account_activated)
- **心灵嘉园身份验证** (xinling_verification)
- **暑期大培训报名成功** (summer_training_success)

## 技术实现

### 数据库关联

- Activity Detail模型通过`sms_template_id`字段关联SMS Template
- 使用`belongsTo`关系：`smsTemplate()`
- 支持预加载：`ActivityDetail::with('smsTemplate')`

### 兼容性处理

系统保持了向后兼容性：

- 旧的`sms_template`字段（直接存储内容）仍然有效
- `getSmsTemplateContent()`方法优先使用新的模板关联
- 如果没有设置模板关联，则回退到旧的直接存储内容

### 代码位置

- **Resource文件**: `app/Filament/Resources/ActivityDetailResource.php`
- **模型文件**: `app/Models/ActivityDetail.php`
- **SMS模板模型**: `app/Models/SmsTemplate.php`

## 注意事项

1. **模板选择是可选的**：不强制要求每个活动都设置SMS模板
2. **模板内容验证**：选择模板前请确认模板内容符合活动需求
3. **参数匹配**：确保选择的模板参数与活动数据匹配
4. **权限控制**：只有有权限的用户才能修改SMS模板设置

## 测试验证

可以运行测试脚本验证功能：

```bash
php test_sms_template_selection.php
```

该脚本会验证：
- SMS模板列表获取
- 活动场次模板关联
- 模板设置和查询功能
- 选项生成逻辑

## 后续优化建议

1. **模板分类显示**：在选择框中按模板分类分组显示
2. **模板预览**：添加模板内容预览功能
3. **批量设置**：支持批量为多个活动设置相同模板
4. **使用统计**：统计各模板的使用频率
5. **模板推荐**：根据活动类型推荐合适的模板