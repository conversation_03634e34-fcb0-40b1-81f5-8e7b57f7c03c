<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SmsTemplate;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;

echo "=== 短信模板系统测试 ===\n\n";

// 1. 测试模板查询
echo "1. 测试模板查询功能\n";
$templates = SmsTemplate::all();
echo "共找到 " . $templates->count() . " 个短信模板\n";

foreach ($templates as $template) {
    echo "- {$template->name} (代码: {$template->code}, 分类: {$template->template_category})\n";
}
echo "\n";

// 2. 测试通过代码查找模板
echo "2. 测试通过代码查找模板\n";
$testCode = 'activity_registration_confirm';
$template = SmsTemplate::findByCode($testCode);
if ($template) {
    echo "找到模板: {$template->name}\n";
    echo "云片网模板ID: {$template->yunpian_template_id}\n";
    echo "参数列表: " . json_encode($template->getTemplateParamsList(), JSON_UNESCAPED_UNICODE) . "\n";
} else {
    echo "未找到代码为 {$testCode} 的模板\n";
}
echo "\n";

// 3. 测试参数验证
echo "3. 测试参数验证功能\n";
if ($template) {
    // 测试完整参数
    $completeParams = [
        'name' => '张三',
        'topic' => '测试活动',
        'time' => '2024-01-20 14:00',
        'address' => '上海市嘉定区'
    ];
    
    $validation = $template->validateParams($completeParams);
    if ($validation === true) {
        echo "✓ 完整参数验证通过\n";
    } else {
        echo "✗ 缺少参数: " . implode(', ', $validation) . "\n";
    }
    
    // 测试不完整参数
    $incompleteParams = [
        'name' => '张三',
        'topic' => '测试活动'
    ];
    
    $validation = $template->validateParams($incompleteParams);
    if ($validation === true) {
        echo "✓ 不完整参数验证通过\n";
    } else {
        echo "✗ 缺少参数: " . implode(', ', $validation) . "\n";
    }
}
echo "\n";

// 4. 测试分类功能
echo "4. 测试模板分类功能\n";
$categories = SmsTemplate::getCategoryOptions();
echo "可用分类: " . json_encode($categories, JSON_UNESCAPED_UNICODE) . "\n";

$groupedTemplates = SmsTemplate::all()->groupBy('template_category');
foreach ($groupedTemplates as $category => $templates) {
    echo "分类 {$category}: {$templates->count()} 个模板\n";
    foreach ($templates as $template) {
        echo "  - {$template->name}\n";
    }
}
echo "\n";

// 5. 测试SmsService（模拟）
echo "5. 测试SmsService功能（模拟）\n";
try {
    $smsService = new SmsService();
    
    // 测试手机号验证
    $validMobile = '13800138000';
    $invalidMobile = '12345';
    
    echo "手机号 {$validMobile} 验证: " . ($smsService->validateMobile($validMobile) ? '✓ 有效' : '✗ 无效') . "\n";
    echo "手机号 {$invalidMobile} 验证: " . ($smsService->validateMobile($invalidMobile) ? '✓ 有效' : '✗ 无效') . "\n";
    
    // 注意：这里不会真正发送短信，只是测试逻辑
    echo "\n注意：实际短信发送需要配置有效的云片网API密钥\n";
    
} catch (Exception $e) {
    echo "SmsService测试出错: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n使用说明:\n";
echo "1. 运行 update_comprehensive_sms_templates.bat 来更新模板数据\n";
echo "2. 在ActivityDetail中设置sms_template_id来选择短信模板\n";
echo "3. 系统会根据模板自动构建相应的参数数据\n";
echo "4. 支持多种模板类型：验证码、账号管理、活动通知等\n";