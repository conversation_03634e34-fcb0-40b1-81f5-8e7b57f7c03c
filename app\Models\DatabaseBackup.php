<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class DatabaseBackup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'file_path',
        'file_size',
        'backup_type',
        'status',
        'description',
        'created_by',
        'schedule_id',
        'backup_started_at',
        'backup_completed_at',
        'error_message',
    ];

    protected $casts = [
        'backup_started_at' => 'datetime',
        'backup_completed_at' => 'datetime',
        'file_size' => 'integer',
    ];

    /**
     * 备份类型常量
     */
    const TYPE_FULL = 'full';
    const TYPE_STRUCTURE = 'structure';
    const TYPE_DATA = 'data';

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_RUNNING = 'running';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 获取备份类型选项
     */
    public static function getBackupTypes(): array
    {
        return [
            self::TYPE_FULL => '完整备份',
            self::TYPE_STRUCTURE => '结构备份',
            self::TYPE_DATA => '数据备份',
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => '等待中',
            self::STATUS_RUNNING => '备份中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
        ];
    }

    /**
     * 获取创建者关系
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取关联的备份计划
     */
    public function schedule()
    {
        return $this->belongsTo(BackupSchedule::class, 'schedule_id');
    }

    /**
     * 获取格式化的文件大小
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return '-';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 获取备份耗时
     */
    public function getDurationAttribute(): ?string
    {
        if (!$this->backup_started_at || !$this->backup_completed_at) {
            return null;
        }

        $duration = $this->backup_completed_at->diffInSeconds($this->backup_started_at);

        if ($duration < 60) {
            return $duration . ' 秒';
        } elseif ($duration < 3600) {
            return round($duration / 60, 1) . ' 分钟';
        } else {
            return round($duration / 3600, 1) . ' 小时';
        }
    }

    /**
     * 检查文件是否存在
     */
    public function fileExists(): bool
    {
        return $this->file_path && Storage::disk('local')->exists($this->file_path);
    }

    /**
     * 获取下载URL
     */
    public function getDownloadUrl(): ?string
    {
        if (!$this->fileExists()) {
            return null;
        }

        return route('backup.download', $this->id);
    }

    /**
     * 删除备份文件
     */
    public function deleteFile(): bool
    {
        if ($this->fileExists()) {
            return Storage::disk('local')->delete($this->file_path);
        }
        return true;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_RUNNING => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            default => 'gray',
        };
    }

    /**
     * 获取备份类型颜色
     */
    public function getBackupTypeColorAttribute(): string
    {
        return match ($this->backup_type) {
            self::TYPE_FULL => 'success',
            self::TYPE_STRUCTURE => 'info',
            self::TYPE_DATA => 'warning',
            default => 'gray',
        };
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('backup_type', $type);
    }

    /**
     * 作用域：已完成的备份
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：失败的备份
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }
}
