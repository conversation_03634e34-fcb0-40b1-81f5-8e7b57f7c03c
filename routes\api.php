<?php

use App\Http\Controllers\RegistrationController;
use App\Http\Controllers\Api\FriendshipLinkController;
use App\Http\Controllers\Api\WechatCollectController;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->group(function () {
    // 活动报名相关
    Route::get('activities/{activityDetail}/status', [RegistrationController::class, 'checkStatus']);
    Route::post('activities/{activityDetail}/register', [RegistrationController::class, 'register']);

    // 友情链接相关
    Route::prefix('friendship-links')->group(function () {
        Route::get('/', [FriendshipLinkController::class, 'index']); // 获取所有分类及其链接
        Route::get('/links', [FriendshipLinkController::class, 'links']); // 获取所有链接（分页）
        Route::get('/categories', [FriendshipLinkController::class, 'categories']); // 获取所有分类
        Route::get('/categories/{categoryId}', [FriendshipLinkController::class, 'byCategory']); // 获取指定分类的链接
    });

    // 微信文章采集相关
    Route::prefix('wechat-collect')->middleware(['throttle:60,1', 'wechat.rate.limit'])->group(function () {
        Route::post('/accounts/{account}/collect', [WechatCollectController::class, 'collectFromAccount']); // 采集指定公众号文章
        Route::post('/collect-from-url', [WechatCollectController::class, 'collectFromUrl']); // 从URL采集文章
        Route::post('/articles/{wechatArticle}/convert', [WechatCollectController::class, 'convertToArticle']); // 转换为本地文章
        Route::get('/stats', [WechatCollectController::class, 'getStats']); // 获取统计信息
    });
});