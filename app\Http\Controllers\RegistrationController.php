<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRegistrationRequest;
use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Services\SmsService;
use App\Services\WechatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;
use EasyWeChat\Factory;
use Illuminate\Support\Facades\Validator;

class RegistrationController extends Controller
{
    protected $smsService;
    protected $wechatService;

    public function __construct(SmsService $smsService, WechatService $wechatService)
    {
        $this->smsService = $smsService;
        $this->wechatService = $wechatService;
    }

    public function showForm($id)
    {
        $activityDetail = ActivityDetail::findOrFail($id);

        // 配置微信 JS-SDK
        $app = Factory::officialAccount(config('wechat.official_account.default'));
        $js = $app->jssdk;
        $jsConfig = $js->buildConfig(['getLocation'], false, false, false);

        return view('registration.form', compact('activityDetail', 'jsConfig'));
    }

    public function register(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'organization' => 'nullable|string|max:255',
            'grade' => 'nullable|string|max:255',
            'gender' => 'required|in:male,female',
            'source' => 'required|string|max:255',
            'openid' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        $activityDetail = ActivityDetail::findOrFail($id);

        // 检查报名人数是否已满
        if ($activityDetail->current_count >= $activityDetail->quota) {
            return back()->with('error', '报名人数已满');
        }

        // 检查报名截止时间
        if ($activityDetail->registration_deadline->isPast()) {
            return back()->with('error', '报名已截止');
        }

        // 创建报名记录
        $registration = Registration::create([
            'activity_detail_id' => $id,
            'name' => $request->name,
            'phone' => $request->phone,
            'organization' => $request->organization,
            'grade' => $request->grade,
            'gender' => $request->gender,
            'source' => $request->source,
            'status' => true,
        ]);

        // 更新当前报名人数
        $activityDetail->increment('current_count');

        // 发送短信通知
        $this->sendSmsNotification($registration, $activityDetail);

        // 发送公众号模板消息
        $this->sendWechatNotification($request, $activityDetail);

        return back()->with('success', '报名成功！');
    }

    /**
     * 发送短信通知
     */
    private function sendSmsNotification($registration, $activityDetail)
    {
        try {
            if (!$this->smsService->validateMobile($registration->phone)) {
                Log::warning('无效手机号', ['phone' => $registration->phone]);
                return false;
            }

            // 优先使用云片网模板
            if ($activityDetail->usesYunpianTemplate()) {
                // 获取模板代码，默认使用活动报名确认模板
                $templateCode = $activityDetail->smsTemplate ? $activityDetail->smsTemplate->code : 'activity_registration_confirm';
                $templateData = $this->buildTemplateData($registration, $activityDetail, $templateCode);
                return $this->smsService->sendSms(
                    $registration->phone,
                    $templateCode,
                    $templateData
                );
            }

            // 兼容旧的直接内容发送方式
            $content = $activityDetail->getSmsTemplateContent();
            if ($content) {
                $content = str_replace(
                    ['{name}', '{topic}', '{time}', '{location}'],
                    [
                        $registration->name,
                        $activityDetail->activity->title,
                        $activityDetail->activity_time,
                        $activityDetail->address
                    ],
                    $content
                );
                
                // 这里需要实现直接内容发送的逻辑
                // 或者转换为使用默认模板
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error('短信发送失败', [
                'registration_id' => $registration->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 构建云片网SMS模板数据 - 通用版本
     * 根据数据库中模板的template_params配置动态构建参数
     * @param $registration
     * @param $activityDetail
     * @param $templateCode
     * @return array
     */
    private function buildTemplateData($registration, $activityDetail = null, $templateCode = 'consultation_notice')
    {
        // 如果没有传入activityDetail，从registration中获取
        if ($activityDetail === null) {
            $activityDetail = $registration->activityDetail;
        }
        
        // 从数据库获取模板配置
        $smsTemplate = \App\Models\SmsTemplate::where('code', $templateCode)->first();
        if (!$smsTemplate || !$smsTemplate->template_params) {
            \Log::warning("SMS模板配置未找到或参数为空", [
                'template_code' => $templateCode
            ]);
            return [];
        }
        
        // 获取模板需要的参数列表
        $requiredParams = is_array($smsTemplate->template_params) 
            ? $smsTemplate->template_params 
            : json_decode($smsTemplate->template_params, true);
            
        if (!$requiredParams) {
            \Log::warning("SMS模板参数解析失败", [
                'template_code' => $templateCode,
                'template_params' => $smsTemplate->template_params
            ]);
            return [];
        }
        
        // 准备所有可能的数据源
        $dataSources = [
            'name' => !empty($registration->name) ? $registration->name : '用户',
            'topic' => !empty($activityDetail->topic) ? $activityDetail->topic : '活动通知',
            'time' => !empty($activityDetail->time) ? $activityDetail->formatted_time : '待定时间',
            'address' => !empty($activityDetail->address) ? $activityDetail->address : '待定地点',
            'obj' => !empty($activityDetail->obj) ? $activityDetail->obj : '全体人员',
            'code' => !empty($activityDetail->code) ? $activityDetail->code : '',
            'pwd' => !empty($activityDetail->pwd) ? $activityDetail->pwd : ''
        ];
        
        // 根据模板配置动态构建参数，云片网要求每个参数长度不超过20个字符
        $templateData = [];
        foreach ($requiredParams as $paramKey => $paramDescription) {
            if (isset($dataSources[$paramKey])) {
                $templateData[$paramKey] = mb_substr($dataSources[$paramKey], 0, 20);
            } else {
                \Log::warning("SMS模板参数数据源未找到", [
                    'template_code' => $templateCode,
                    'param_key' => $paramKey,
                    'param_description' => $paramDescription
                ]);
                $templateData[$paramKey] = ''; // 提供空值作为默认值
            }
        }
        
        // 记录日志以便调试
        \Log::info("构建云片网SMS模板数据", [
            "registration_id" => $registration->id,
            "activity_detail_id" => $activityDetail->id,
            "activity_id" => $activityDetail->activity_id,
            "template_code" => $templateCode,
            "required_params" => array_keys($requiredParams),
            "yunpian_template_data" => $templateData
        ]);
        
        return $templateData;
    }

    /**
     * 生成临时密码
     */
    private function generateTempPassword($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $password;
     }

    /**
     * 发送微信公众号模板消息
     */
    private function sendWechatNotification(Request $request, ActivityDetail $activityDetail)
    {
        if ($request->openid && isset($activityDetail->wechatTemplate)) {
            try {
                if (!$this->wechatService) {
                    $this->wechatService = app(WechatService::class);
                }

                $this->wechatService->sendTemplateMessage(
                    $request->openid,
                    $activityDetail->wechatTemplate->code,
                    [
                        'first' => ['value' => '您已成功报名活动'],
                        'keyword1' => ['value' => !empty($activityDetail->topic) ? $activityDetail->topic : '活动通知'],
                        'keyword2' => ['value' => !empty($activityDetail->time) ? $activityDetail->formatted_time : ($activityDetail->activity_time ? $activityDetail->activity_time->format('Y-m-d H:i') : '待定时间')],
                        'keyword3' => ['value' => !empty($activityDetail->address) ? $activityDetail->address : '待定地点'],
                        'remark' => ['value' => '请准时参加活动，如有疑问请联系主办方。'],
                    ],
                    route('registration.form', ['id' => $activityDetail->id])
                );
                
                Log::info('微信模板消息发送成功', [
                    'openid' => $request->openid,
                    'activity' => $activityDetail->topic
                ]);
            } catch (Exception $e) {
                // 记录错误但不影响报名流程
                Log::error('发送公众号模板消息失败：' . $e->getMessage(), [
                    'openid' => $request->openid ?? 'unknown',
                    'activity_id' => $activityDetail->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    public function checkStatus($id)
    {
        $activityDetail = ActivityDetail::findOrFail($id);

        return response()->json([
            'quota' => $activityDetail->quota,
            'current_count' => $activityDetail->current_count,
            'deadline' => $activityDetail->registration_deadline,
            'is_available' => $activityDetail->current_count < $activityDetail->quota && !$activityDetail->registration_deadline->isPast(),
        ]);
    }
}