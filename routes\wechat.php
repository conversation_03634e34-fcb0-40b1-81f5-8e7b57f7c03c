<?php

use App\Http\Controllers\WechatWebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| WeChat Routes
|--------------------------------------------------------------------------
|
| Here is where you can register WeChat related routes for your application.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group.
|
*/

// 微信服务器验证和消息接收
Route::any('/wechat/webhook/{account}', [WechatWebhookController::class, 'handle'])
    ->name('wechat.webhook')
    ->where('account', '[0-9]+');

// 微信服务器IP获取
Route::get('/wechat/server-ips', [WechatWebhookController::class, 'getServerIps'])
    ->name('wechat.server-ips');

// 微信Webhook测试
Route::get('/wechat/test/{account}', [WechatWebhookController::class, 'test'])
    ->name('wechat.test')
    ->where('account', '[0-9]+');