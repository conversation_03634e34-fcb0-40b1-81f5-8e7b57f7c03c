<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 微信文章采集配置
    |--------------------------------------------------------------------------
    |
    | 这里配置微信公众号文章采集的相关参数
    |
    */

    // 默认采集设置
    'default' => [
        // 每次采集的文章数量
        'count' => 20,
        
        // 采集间隔（小时）
        'interval' => 1,
        
        // 是否自动转换为本地文章
        'auto_convert' => false,
        
        // 默认分类ID（转换时使用）
        'default_category_id' => null,
    ],

    // 缩略图设置
    'thumbnail' => [
        // 缩略图存储路径
        'storage_path' => 'wechat/thumbnails',
        
        // 缩略图URL前缀
        'url_prefix' => '/storage/wechat/thumbnails',
        
        // 允许的图片格式
        'allowed_formats' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        
        // 最大文件大小（字节）
        'max_size' => 5 * 1024 * 1024, // 5MB
    ],

    // 内容处理设置
    'content' => [
        // 是否清理HTML标签
        'clean_html' => true,
        
        // 允许的HTML标签
        'allowed_tags' => [
            'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'div',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li',
            'a', 'img',
            'blockquote', 'pre', 'code',
            'table', 'thead', 'tbody', 'tr', 'th', 'td'
        ],
        
        // 是否下载图片到本地
        'download_images' => true,
        
        // 图片存储路径
        'image_storage_path' => 'wechat/images',
    ],

    // 队列设置
    'queue' => [
        // 采集队列名称
        'collect_queue' => 'wechat-collect',
        
        // 转换队列名称
        'convert_queue' => 'wechat-convert',
        
        // 是否使用队列
        'enabled' => true,
    ],

    // 日志设置
    'logging' => [
        // 是否启用日志
        'enabled' => true,
        
        // 日志频道
        'channel' => 'daily',
        
        // 日志级别
        'level' => 'info',
    ],

    // 限制设置
    'limits' => [
        // 单个公众号每天最大采集次数
        'max_collect_per_day' => 24,
        
        // 单次采集最大文章数
        'max_articles_per_collect' => 50,
        
        // 请求超时时间（秒）
        'request_timeout' => 30,
        
        // 重试次数
        'retry_times' => 3,
    ],

    // 微信API设置
    'api' => [
        // API基础URL
        'base_url' => 'https://api.weixin.qq.com',
        
        // 获取素材列表的URL
        'material_list_url' => '/cgi-bin/material/batchget_material',
        
        // 获取素材详情的URL
        'material_get_url' => '/cgi-bin/material/get_material',
        
        // Access Token缓存时间（秒）
        'token_cache_time' => 7000,
    ],

    // 文章状态映射
    'status_mapping' => [
        'pending' => '待处理',
        'processing' => '处理中',
        'processed' => '已处理',
        'published' => '已发布',
        'failed' => '失败',
    ],

    // 采集类型映射
    'collect_type_mapping' => [
        'api' => 'API采集',
        'url' => 'URL采集',
        'manual' => '手动添加',
    ],
];