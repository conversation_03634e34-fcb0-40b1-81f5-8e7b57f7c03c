<?php

namespace App\Filament\Resources\WechatAccountResource\Pages;

use App\Filament\Resources\WechatAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateWechatAccount extends CreateRecord
{
    protected static string $resource = WechatAccountResource::class;
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}