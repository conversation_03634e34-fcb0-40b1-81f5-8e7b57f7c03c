<?php

namespace App\Filament\Resources\ActivityResource\Pages;

use App\Filament\Resources\ActivityResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListActivities extends ListRecords
{
    protected static string $resource = ActivityResource::class;
    protected static ?string $title = '活动管理';

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()->label('创建活动'),
        ];
    }
} 