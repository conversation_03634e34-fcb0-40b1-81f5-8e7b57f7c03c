<?php

/**
 * 数据迁移脚本：将现有字段数据迁移到与云片网直接对应的字段
 * 这样可以确保短信模板的 #topic#, #time#, #address#, #obj# 参数都有数据
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

echo "开始迁移数据到云片网直接对应字段...\n";

try {
    // 首先运行迁移，添加新字段
    echo "1. 运行数据库迁移...\n";
    $output = shell_exec('php artisan migrate --path=database/migrations/2025_01_21_000001_redesign_activity_details_for_yunpian.php 2>&1');
    echo $output . "\n";
    
    // 获取所有activity_details记录
    $activityDetails = DB::table('activity_details')->get();
    
    echo "2. 找到 " . $activityDetails->count() . " 条活动详情记录\n";
    
    $updatedCount = 0;
    
    foreach ($activityDetails as $detail) {
        $updateData = [];
        
        // 迁移topic字段
        if (empty($detail->topic)) {
            // 优先使用activities表的title，然后是theme
            $activity = DB::table('activities')->where('id', $detail->activity_id)->first();
            if ($activity && !empty($activity->title)) {
                $updateData['topic'] = $activity->title;
            } elseif (!empty($detail->theme)) {
                $updateData['topic'] = $detail->theme;
            } else {
                $updateData['topic'] = '活动通知';
            }
        }
        
        // 迁移time字段
        if (empty($detail->time) && !empty($detail->activity_time)) {
            try {
                $updateData['time'] = Carbon::parse($detail->activity_time)->format('Y年m月d日 H:i');
            } catch (Exception $e) {
                $updateData['time'] = $detail->activity_time;
            }
        }
        
        // address字段已经存在，不需要迁移
        
        // 迁移obj字段
        if (empty($detail->obj)) {
            if (!empty($detail->target_audience)) {
                $updateData['obj'] = $detail->target_audience;
            } elseif (!empty($detail->target)) {
                $updateData['obj'] = $detail->target;
            } else {
                $updateData['obj'] = '全体人员';
            }
        }
        
        // 如果有数据需要更新
        if (!empty($updateData)) {
            DB::table('activity_details')
                ->where('id', $detail->id)
                ->update($updateData);
            $updatedCount++;
            
            echo "更新活动详情 ID {$detail->id}:\n";
            foreach ($updateData as $field => $value) {
                echo "  - {$field}: {$value}\n";
            }
        }
    }
    
    echo "\n3. 数据迁移完成！\n";
    echo "总共更新了 {$updatedCount} 条记录\n";
    
    // 验证迁移结果
    echo "\n4. 验证迁移结果...\n";
    $emptyFields = [];
    
    $topicEmpty = DB::table('activity_details')->whereNull('topic')->orWhere('topic', '')->count();
    $timeEmpty = DB::table('activity_details')->whereNull('time')->orWhere('time', '')->count();
    $addressEmpty = DB::table('activity_details')->whereNull('address')->orWhere('address', '')->count();
    $objEmpty = DB::table('activity_details')->whereNull('obj')->orWhere('obj', '')->count();
    
    echo "空字段统计：\n";
    echo "- topic字段为空: {$topicEmpty} 条\n";
    echo "- time字段为空: {$timeEmpty} 条\n";
    echo "- address字段为空: {$addressEmpty} 条\n";
    echo "- obj字段为空: {$objEmpty} 条\n";
    
    if ($topicEmpty + $timeEmpty + $addressEmpty + $objEmpty == 0) {
        echo "\n✅ 所有云片网字段都已填充完整！\n";
    } else {
        echo "\n⚠️ 仍有部分字段为空，可能需要手动填充\n";
    }
    
} catch (Exception $e) {
    echo "❌ 迁移过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "\n数据迁移脚本执行完成。\n";