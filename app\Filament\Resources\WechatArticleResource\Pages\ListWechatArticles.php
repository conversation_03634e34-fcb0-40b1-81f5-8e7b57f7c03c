<?php

namespace App\Filament\Resources\WechatArticleResource\Pages;

use App\Filament\Resources\WechatArticleResource;
use App\Services\WechatArticleCollectService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms;

class ListWechatArticles extends ListRecords
{
    protected static string $resource = WechatArticleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('新增文章'),
            
            Actions\Action::make('collectFromUrl')
                ->label('采集文章')
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->form([
                    Forms\Components\TextInput::make('url')
                        ->label('微信文章链接')
                        ->url()
                        ->required()
                        ->placeholder('请输入微信公众号文章链接')
                        ->helperText('支持微信公众号文章的分享链接'),
                    
                    Forms\Components\Select::make('wechat_account_id')
                        ->label('关联公众号（可选）')
                        ->relationship('wechatAccount', 'name')
                        ->searchable()
                        ->preload(),
                ])
                ->action(function (array $data) {
                    try {
                        $service = app(WechatArticleCollectService::class);
                        $wechatAccount = $data['wechat_account_id'] ? \App\Models\WechatAccount::find($data['wechat_account_id']) : null;
                        $article = $service->collectFromUrl($data['url'], $wechatAccount);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('采集成功')
                            ->body('文章《' . $article->title . '》采集成功')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        \Filament\Notifications\Notification::make()
                            ->title('采集失败')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }
}