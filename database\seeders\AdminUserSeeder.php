<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建管理员角色
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);

        // 创建基本权限
        $permissions = [
            'system.view' => '查看系统信息',
            'system.manage' => '管理系统设置',
            'users.view' => '查看用户',
            'users.create' => '创建用户',
            'users.edit' => '编辑用户',
            'users.delete' => '删除用户',
            'roles.view' => '查看角色',
            'roles.create' => '创建角色',
            'roles.edit' => '编辑角色',
            'roles.delete' => '删除角色',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(['name' => $name, 'guard_name' => 'web']);
        }

        // 为管理员角色分配所有权限
        $adminRole->syncPermissions(array_keys($permissions));

        // 创建超级管理员账号
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'real_name' => '超级管理员',
                'password' => Hash::make('password'),
                'is_active' => true,
                'is_verified' => true,
                'region' => '上海市嘉定区管理中心',
                'school' => '系统管理',
                'phone' => '13800000000',
                'email_verified_at' => now(),
            ]
        );
        $superAdmin->assignRole($adminRole);

        // 创建管理员用户
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'real_name' => '管理员',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'is_verified' => true,
                'region' => '上海市嘉定区管理中心',
                'school' => '系统管理',
                'phone' => '13800000001',
            ]
        );
        $admin->assignRole($adminRole);

        // 创建测试用户账号
        $testUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'real_name' => '测试用户',
                'password' => Hash::make('password'),
                'is_active' => true,
                'is_verified' => true,
                'region' => '上海市嘉定区实验小学',
                'school' => '实验小学',
                'phone' => '13800000002',
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('管理员账号创建成功：');
        $this->command->info('超级管理员: <EMAIL> / password');
        $this->command->info('管理员: <EMAIL> / admin123');
        $this->command->info('测试用户: <EMAIL> / password');
    }
}
