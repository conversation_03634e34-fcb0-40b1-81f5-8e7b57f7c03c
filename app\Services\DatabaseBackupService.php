<?php

namespace App\Services;

use App\Models\DatabaseBackup;
use App\Models\Setting;
use App\Models\User;
use App\Notifications\DatabaseBackupNotification;
use App\Services\DingTalkService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DatabaseBackupService
{
    /**
     * 创建数据库备份
     */
    public function createBackup(array $options = []): DatabaseBackup
    {
        $backupType = $options['backup_type'] ?? DatabaseBackup::TYPE_FULL;
        $description = $options['description'] ?? '';
        $createdBy = $options['created_by'] ?? auth()->id();

        // 生成备份名称
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $name = $options['name'] ?? "backup_{$backupType}_{$timestamp}";

        // 创建备份记录
        $backup = DatabaseBackup::create([
            'name' => $name,
            'backup_type' => $backupType,
            'description' => $description,
            'created_by' => $createdBy,
            'status' => DatabaseBackup::STATUS_PENDING,
        ]);

        // 异步执行备份
        $this->executeBackup($backup);

        return $backup;
    }

    /**
     * 执行备份
     */
    public function executeBackup(DatabaseBackup $backup): void
    {
        try {
            // 更新状态为运行中
            $backup->update([
                'status' => DatabaseBackup::STATUS_RUNNING,
                'backup_started_at' => Carbon::now(),
            ]);

            // 生成文件路径
            $fileName = $backup->name . '.sql';
            $filePath = 'private/backups/' . $fileName;

            // 确保备份目录存在
            Storage::disk('local')->makeDirectory('private/backups');

            // 执行备份
            $sqlContent = $this->generateSqlBackup($backup->backup_type);

            // 保存文件
            Storage::disk('local')->put($filePath, $sqlContent);

            // 获取文件大小
            $fileSize = Storage::disk('local')->size($filePath);

            // 更新备份记录
            $backup->update([
                'status' => DatabaseBackup::STATUS_COMPLETED,
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'backup_completed_at' => Carbon::now(),
            ]);

            Log::info("数据库备份完成: {$backup->name}");

            // 发送成功通知
            $this->sendNotification($backup, 'completed');

        } catch (\Exception $e) {
            // 备份失败
            $backup->update([
                'status' => DatabaseBackup::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'backup_completed_at' => Carbon::now(),
            ]);

            Log::error("数据库备份失败: {$backup->name}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 发送失败通知
            $this->sendNotification($backup, 'failed');

            throw $e;
        }
    }

    /**
     * 生成SQL备份内容
     */
    private function generateSqlBackup(string $backupType): string
    {
        $sql = '';
        $databaseName = config('database.connections.mysql.database');

        // 添加备份头部信息
        $sql .= "-- MySQL Database Backup\n";
        $sql .= "-- Generated on: " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: {$databaseName}\n";
        $sql .= "-- Backup Type: {$backupType}\n";
        $sql .= "-- Generated by: CMS Admin System\n\n";

        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n";
        $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql .= "SET AUTOCOMMIT = 0;\n";
        $sql .= "START TRANSACTION;\n";
        $sql .= "SET time_zone = \"+00:00\";\n\n";

        // 获取所有表
        $tables = $this->getAllTables();

        foreach ($tables as $table) {
            $tableName = $table->{'Tables_in_' . $databaseName};

            if ($backupType === DatabaseBackup::TYPE_FULL || $backupType === DatabaseBackup::TYPE_STRUCTURE) {
                $sql .= $this->getTableStructure($tableName);
            }

            if ($backupType === DatabaseBackup::TYPE_FULL || $backupType === DatabaseBackup::TYPE_DATA) {
                $sql .= $this->getTableData($tableName);
            }
        }

        $sql .= "\nSET FOREIGN_KEY_CHECKS=1;\n";
        $sql .= "COMMIT;\n";

        return $sql;
    }

    /**
     * 获取所有表
     */
    private function getAllTables(): array
    {
        $databaseName = config('database.connections.mysql.database');
        return DB::select("SHOW TABLES FROM `{$databaseName}`");
    }

    /**
     * 获取表结构
     */
    private function getTableStructure(string $tableName): string
    {
        $sql = "\n-- Table structure for table `{$tableName}`\n";
        $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";

        $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
        $sql .= $createTable->{'Create Table'} . ";\n\n";

        return $sql;
    }

    /**
     * 获取表数据
     */
    private function getTableData(string $tableName): string
    {
        $sql = "-- Dumping data for table `{$tableName}`\n";

        $rows = DB::table($tableName)->get();

        if ($rows->isEmpty()) {
            return $sql . "-- No data found\n\n";
        }

        $sql .= "LOCK TABLES `{$tableName}` WRITE;\n";
        $sql .= "/*!40000 ALTER TABLE `{$tableName}` DISABLE KEYS */;\n";

        $insertSql = "INSERT INTO `{$tableName}` VALUES ";
        $values = [];

        foreach ($rows as $row) {
            $rowValues = [];
            foreach ((array)$row as $value) {
                if (is_null($value)) {
                    $rowValues[] = 'NULL';
                } else {
                    $rowValues[] = "'" . addslashes($value) . "'";
                }
            }
            $values[] = '(' . implode(',', $rowValues) . ')';
        }

        $sql .= $insertSql . implode(',', $values) . ";\n";
        $sql .= "/*!40000 ALTER TABLE `{$tableName}` ENABLE KEYS */;\n";
        $sql .= "UNLOCK TABLES;\n\n";

        return $sql;
    }

    /**
     * 删除备份
     */
    public function deleteBackup(DatabaseBackup $backup): bool
    {
        try {
            // 删除文件
            $backup->deleteFile();

            // 删除记录
            $backup->delete();

            Log::info("删除备份: {$backup->name}");
            return true;

        } catch (\Exception $e) {
            Log::error("删除备份失败: {$backup->name}", [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 清理过期备份
     */
    public function cleanupOldBackups(int $keepDays = 30): int
    {
        $cutoffDate = Carbon::now()->subDays($keepDays);

        $oldBackups = DatabaseBackup::where('created_at', '<', $cutoffDate)
            ->where('status', DatabaseBackup::STATUS_COMPLETED)
            ->get();

        $deletedCount = 0;

        foreach ($oldBackups as $backup) {
            if ($this->deleteBackup($backup)) {
                $deletedCount++;
            }
        }

        Log::info("清理过期备份完成", [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate->format('Y-m-d H:i:s'),
        ]);

        return $deletedCount;
    }

    /**
     * 获取备份统计信息
     */
    public function getBackupStats(): array
    {
        return [
            'total_backups' => DatabaseBackup::count(),
            'completed_backups' => DatabaseBackup::completed()->count(),
            'failed_backups' => DatabaseBackup::failed()->count(),
            'total_size' => DatabaseBackup::completed()->sum('file_size'),
            'latest_backup' => DatabaseBackup::completed()->latest()->first(),
        ];
    }

    /**
     * 发送备份通知
     */
    private function sendNotification(DatabaseBackup $backup, string $type): void
    {
        try {
            // 检查是否启用通知
            if (!Setting::getValue('backup_notification_enabled', true)) {
                return;
            }

            // 发送钉钉通知
            $dingTalkService = app(DingTalkService::class);
            if ($type === 'completed') {
                $dingTalkService->sendBackupCompletedNotification($backup);
            } else {
                $dingTalkService->sendBackupFailedNotification($backup);
            }

            // 发送给超级管理员（数据库通知）
            $superAdmins = User::role('super_admin')->get();
            foreach ($superAdmins as $admin) {
                $admin->notify(new DatabaseBackupNotification($backup, $type));
            }

            Log::info("备份通知已发送", [
                'backup_id' => $backup->id,
                'type' => $type,
                'dingtalk_enabled' => !empty(Setting::getValue('dingtalk_webhook', '')),
                'admin_count' => $superAdmins->count(),
            ]);

        } catch (\Exception $e) {
            Log::error("发送备份通知失败", [
                'backup_id' => $backup->id,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
