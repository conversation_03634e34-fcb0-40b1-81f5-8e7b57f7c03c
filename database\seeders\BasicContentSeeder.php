<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Post;
use App\Models\Tag;
use App\Models\User;

class BasicContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建分类
        $categories = [
            [
                'name' => '新闻资讯',
                'slug' => 'news',
                'description' => '最新的教育新闻和资讯',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => '教学资源',
                'slug' => 'resources',
                'description' => '教学相关的资源和材料',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => '视频课程',
                'slug' => 'videos',
                'description' => '在线视频课程',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => '活动通知',
                'slug' => 'activities',
                'description' => '学校活动和通知',
                'sort_order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // 创建标签
        $tags = [
            ['name' => '重要', 'slug' => 'important', 'color' => '#ef4444'],
            ['name' => '推荐', 'slug' => 'recommended', 'color' => '#3b82f6'],
            ['name' => '热门', 'slug' => 'hot', 'color' => '#f59e0b'],
            ['name' => '最新', 'slug' => 'latest', 'color' => '#10b981'],
            ['name' => '精选', 'slug' => 'featured', 'color' => '#8b5cf6'],
        ];

        foreach ($tags as $tagData) {
            Tag::firstOrCreate(
                ['name' => $tagData['name']],
                $tagData
            );
        }

        // 获取管理员用户
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            $admin = User::first();
        }

        // 创建示例文章
        $posts = [
            [
                'title' => '欢迎使用成就管理系统',
                'slug' => 'welcome-to-achievement-system',
                'excerpt' => '这是一个全新的成就管理系统，帮助您更好地管理和展示用户成就。',
                'content' => '<h2>欢迎使用成就管理系统</h2><p>这个系统包含了成就中心和积分中心两个主要功能模块。</p><h3>主要功能：</h3><ul><li>用户积分管理</li><li>成就排行榜</li><li>地区积分统计</li><li>行为跟踪记录</li></ul>',
                'category_id' => Category::where('slug', 'news')->first()->id,
                'is_published' => true,
                'published_at' => now(),
                'sort_order' => 1,
            ],
            [
                'title' => '积分规则详细说明',
                'slug' => 'point-rules-explanation',
                'excerpt' => '详细了解系统的积分获取规则和计算方式。',
                'content' => '<h2>积分规则说明</h2><p>用户使用实名账号登录系统后：</p><ul><li>当日首次登录 + 浏览任意页面超过 30 秒，可获得 1 积分</li><li>浏览视频内容可获得 5 积分</li><li>同一页面/版块1小时内重复刷新不重复计分</li><li>游客账号不参与积分统计</li></ul>',
                'category_id' => Category::where('slug', 'news')->first()->id,
                'is_published' => true,
                'published_at' => now()->subDays(1),
                'sort_order' => 2,
            ],
            [
                'title' => '如何提高您的积分排名',
                'slug' => 'how-to-improve-ranking',
                'excerpt' => '学习如何通过合理的使用系统来提高您的积分排名。',
                'content' => '<h2>提高积分排名的方法</h2><p>以下是一些提高积分的有效方法：</p><ol><li>每日登录系统</li><li>认真浏览教学内容</li><li>观看视频课程</li><li>参与系统活动</li></ol>',
                'category_id' => Category::where('slug', 'resources')->first()->id,
                'is_published' => true,
                'published_at' => now()->subDays(2),
                'sort_order' => 3,
            ],
            [
                'title' => '系统使用教程视频',
                'slug' => 'system-tutorial-video',
                'excerpt' => '通过视频教程快速掌握系统的使用方法。',
                'content' => '<h2>系统使用教程</h2><p>这是一个详细的系统使用教程，帮助您快速上手。</p><div class="video-container"><p>视频内容：系统功能介绍和操作指南</p></div>',
                'category_id' => Category::where('slug', 'videos')->first()->id,
                'is_published' => true,
                'published_at' => now()->subDays(3),
                'sort_order' => 4,
            ],
            [
                'title' => '本月积分竞赛活动通知',
                'slug' => 'monthly-point-competition',
                'excerpt' => '参与本月的积分竞赛活动，赢取丰厚奖品！',
                'content' => '<h2>本月积分竞赛</h2><p>活动时间：本月1日-30日</p><h3>奖励设置：</h3><ul><li>第一名：精美奖品</li><li>第二名：优秀奖品</li><li>第三名：鼓励奖品</li></ul>',
                'category_id' => Category::where('slug', 'activities')->first()->id,
                'is_published' => true,
                'published_at' => now()->subDays(5),
                'sort_order' => 5,
            ],
        ];

        foreach ($posts as $postData) {
            $post = Post::firstOrCreate(
                ['slug' => $postData['slug']],
                $postData
            );

            // 为文章添加标签
            if ($post->wasRecentlyCreated) {
                $randomTags = Tag::inRandomOrder()->limit(rand(1, 3))->get();
                $post->tags()->attach($randomTags);
            }
        }

        $this->command->info('基础内容数据创建成功！');
        $this->command->info('- 创建了 4 个分类');
        $this->command->info('- 创建了 5 个标签');
        $this->command->info('- 创建了 5 篇示例文章');
    }
}
