<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Article extends Model
{
    use HasFactory;

    // ... existing code ...

    protected $fillable = [
        'title',
        'short_title',
        'content',
        'summary',
        'cover_image',
        'thumb_image',
        'meta_data',
        'user_id',
        'view_count',
        'comment_count',
        'like_count',
        'hot_score',
        'last_viewed_at',
        'last_commented_at',
        'custom_flags',
        'weight',
        'source',
        'author',
        'title_color',
        'allow_comment',
        'view_permission',
        'status',
        'published_at',
        'extra_options',
    ];

    protected $casts = [
        'content' => 'array',
        'meta_data' => 'array',
        'last_viewed_at' => 'datetime',
        'last_commented_at' => 'datetime',
        'published_at' => 'datetime',
        'hot_score' => 'decimal:2',
        'custom_flags' => 'array',
        'extra_options' => 'array',
        'allow_comment' => 'boolean',
    ];

    public function versions(): HasMany
    {
        return $this->hasMany(ArticleVersion::class);
    }

    public function incrementViewCount(): void
    {
        $this->increment('view_count');
        $this->updateLastViewedAt();
        $this->updateHotScore();
    }

    public function incrementCommentCount(): void
    {
        $this->increment('comment_count');
        $this->updateLastCommentedAt();
        $this->updateHotScore();
    }

    public function incrementLikeCount(): void
    {
        $this->increment('like_count');
        $this->updateHotScore();
    }

    protected function updateLastViewedAt(): void
    {
        $this->update(['last_viewed_at' => now()]);
    }

    protected function updateLastCommentedAt(): void
    {
        $this->update(['last_commented_at' => now()]);
    }

    protected function updateHotScore(): void
    {
        // 热度计算公式：阅读量 * 0.4 + 评论数 * 0.4 + 点赞数 * 0.2
        $hotScore = ($this->view_count * 0.4) + ($this->comment_count * 0.4) + ($this->like_count * 0.2);
        $this->update(['hot_score' => $hotScore]);
    }

    public function createVersion(string $version, string $remark = null, $createdBy = null): ArticleVersion
    {
        return $this->versions()->create([
            'version' => $version,
            'title' => $this->title,
            'content' => $this->content,
            'summary' => $this->summary,
            'cover_image' => $this->cover_image,
            'meta_data' => $this->meta_data,
            'tag' => 'auto',
            'remark' => $remark,
            'created_by' => $createdBy ?? auth()->id(),
        ]);
    }

    public function rollbackToVersion(ArticleVersion $version): bool
    {
        $this->update([
            'title' => $version->title,
            'content' => $version->content,
            'summary' => $version->summary,
            'cover_image' => $version->cover_image,
            'meta_data' => $version->meta_data,
        ]);

        return true;
    }

    public static function getHotArticles(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember('hot_articles', 3600, function () use ($limit) {
            return static::orderBy('hot_score', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    public static function getMostViewedArticles(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember('most_viewed_articles', 3600, function () use ($limit) {
            return static::orderBy('view_count', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    public static function getMostCommentedArticles(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember('most_commented_articles', 3600, function () use ($limit) {
            return static::orderBy('comment_count', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    public function category()
    {
        return $this->belongsTo(\App\Models\Category::class, 'category_id');
    }

    public function subCategories()
    {
        return $this->belongsToMany(
            \App\Models\Category::class,
            'article_sub_category',
            'article_id',
            'category_id'
        );
    }

    public function tags()
    {
        return $this->belongsToMany(
            \App\Models\Tag::class,
            'article_tag',
            'article_id',
            'tag_id'
        );
    }

    public function comments()
    {
        return $this->hasMany(\App\Models\Comment::class, 'article_id');
    }
}