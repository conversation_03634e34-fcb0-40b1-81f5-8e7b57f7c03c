<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use Carbon\Carbon;

class RegistrationTestSeeder extends Seeder
{
    /**
     * 专门用于测试报名系统数据统计准确性的Seeder
     */
    public function run(): void
    {
        $this->command->info('🧪 开始创建报名系统测试数据...');

        // 清理现有的报名相关数据
        $this->command->info('清理现有数据...');
        \DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Registration::truncate();
        ActivityDetail::truncate();
        Activity::truncate();
        \DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 创建测试活动
        $this->createTestActivities();

        // 验证数据统计
        $this->validateDataStatistics();

        $this->command->info('🎉 报名系统测试数据创建完成！');
    }

    /**
     * 创建测试活动
     */
    private function createTestActivities()
    {
        // 测试场景1：满员活动
        $activity1 = Activity::create([
            'title' => '【测试】满员活动 - 教师培训',
            'status' => 'published',
            'publish_time' => now()->subDays(5),
            'qrcode_url' => 'https://example.com/qr/test1',
            'wechat_url' => 'https://mp.weixin.qq.com/s/test1',
            'views_count' => 100,
        ]);

        $detail1 = ActivityDetail::create([
            'activity_id' => $activity1->id,
            'theme' => '满员测试场次',
            'deadline' => Carbon::now()->addDays(30),
            'registration_deadline' => Carbon::now()->addDays(25),
            'status' => 1,
            'quota' => 50,
            'target' => '全体教师',
            'target_audience' => '小学教师',
            'current_count' => 0, // 初始为0，通过报名递增
            'activity_time' => Carbon::now()->addDays(35),
            'fee' => '免费',
            'reminder' => '测试提醒',
            'registration_method' => '在线报名',
            'address' => '测试地址',
            'sms_template' => '测试短信模板',
        ]);

        // 创建50个报名记录（满员）
        for ($i = 1; $i <= 50; $i++) {
            Registration::create([
                'activity_detail_id' => $detail1->id,
                'name' => "测试用户{$i}",
                'phone' => '138' . str_pad($i, 8, '0', STR_PAD_LEFT),
                'organization' => "测试学校{$i}",
                'grade' => '一年级',
                'gender' => $i % 2 == 0 ? 'male' : 'female',
                'source' => '网站',
                'status' => true,
                'created_at' => Carbon::now()->subDays(rand(1, 10)),
            ]);

            // 更新当前报名人数
            $detail1->increment('current_count');
        }

        // 测试场景2：部分报名活动
        $activity2 = Activity::create([
            'title' => '【测试】部分报名活动 - 学术研讨',
            'status' => 'published',
            'publish_time' => now()->subDays(3),
            'qrcode_url' => 'https://example.com/qr/test2',
            'wechat_url' => 'https://mp.weixin.qq.com/s/test2',
            'views_count' => 75,
        ]);

        $detail2 = ActivityDetail::create([
            'activity_id' => $activity2->id,
            'theme' => '部分报名测试场次',
            'deadline' => Carbon::now()->addDays(20),
            'registration_deadline' => Carbon::now()->addDays(15),
            'status' => 1,
            'quota' => 80,
            'target' => '骨干教师',
            'target_audience' => '中学教师',
            'current_count' => 0,
            'activity_time' => Carbon::now()->addDays(25),
            'fee' => '100元',
            'reminder' => '测试提醒2',
            'registration_method' => '电话报名',
            'address' => '测试地址2',
            'sms_template' => '测试短信模板2',
        ]);

        // 创建30个报名记录（部分报名）
        for ($i = 1; $i <= 30; $i++) {
            Registration::create([
                'activity_detail_id' => $detail2->id,
                'name' => "部分测试用户{$i}",
                'phone' => '139' . str_pad($i, 8, '0', STR_PAD_LEFT),
                'organization' => "部分测试学校{$i}",
                'grade' => '二年级',
                'gender' => $i % 3 == 0 ? 'male' : 'female',
                'source' => '微信',
                'status' => $i <= 25, // 前25个有效，后5个无效
                'created_at' => Carbon::now()->subDays(rand(1, 5)),
            ]);

            // 只有有效报名才增加计数
            if ($i <= 25) {
                $detail2->increment('current_count');
            }
        }

        // 测试场景3：多场次活动
        $activity3 = Activity::create([
            'title' => '【测试】多场次活动 - 技能培训',
            'status' => 'published',
            'publish_time' => now()->subDays(7),
            'qrcode_url' => 'https://example.com/qr/test3',
            'wechat_url' => 'https://mp.weixin.qq.com/s/test3',
            'views_count' => 150,
        ]);

        // 创建3个场次
        for ($session = 1; $session <= 3; $session++) {
            $detail = ActivityDetail::create([
                'activity_id' => $activity3->id,
                'theme' => "多场次测试 - 第{$session}场",
                'deadline' => Carbon::now()->addDays(15 + $session * 5),
                'registration_deadline' => Carbon::now()->addDays(10 + $session * 5),
                'status' => 1,
                'quota' => 40,
                'target' => '新入职教师',
                'target_audience' => '高中教师',
                'current_count' => 0,
                'activity_time' => Carbon::now()->addDays(20 + $session * 5),
                'fee' => '50元',
                'reminder' => "第{$session}场测试提醒",
                'registration_method' => '现场报名',
                'address' => "测试地址{$session}",
                'sms_template' => "第{$session}场测试短信模板",
            ]);

            // 每个场次创建不同数量的报名
            $registrationCount = 15 + $session * 5; // 20, 25, 30
            for ($i = 1; $i <= $registrationCount; $i++) {
                Registration::create([
                    'activity_detail_id' => $detail->id,
                    'name' => "多场次用户{$session}-{$i}",
                    'phone' => '137' . str_pad($session * 100 + $i, 8, '0', STR_PAD_LEFT),
                    'organization' => "多场次学校{$session}-{$i}",
                    'grade' => '三年级',
                    'gender' => ($session + $i) % 2 == 0 ? 'male' : 'female',
                    'source' => '现场',
                    'status' => true,
                    'created_at' => Carbon::now()->subDays(rand(1, 15)),
                ]);

                $detail->increment('current_count');
            }
        }

        $this->command->info('✓ 创建了3个测试活动，包含5个场次');
    }

    /**
     * 验证数据统计准确性
     */
    private function validateDataStatistics()
    {
        $this->command->info('');
        $this->command->info('🔍 验证数据统计准确性...');

        // 总体统计
        $totalActivities = Activity::count();
        $totalDetails = ActivityDetail::count();
        $totalRegistrations = Registration::count();
        $validRegistrations = Registration::where('status', true)->count();

        $this->command->info("活动总数: {$totalActivities}");
        $this->command->info("场次总数: {$totalDetails}");
        $this->command->info("报名总数: {$totalRegistrations}");
        $this->command->info("有效报名: {$validRegistrations}");

        // 详细验证每个场次
        $this->command->info('');
        $this->command->info('📋 场次详细验证:');

        ActivityDetail::with(['activity', 'registrations'])->get()->each(function ($detail) {
            $actualCount = $detail->registrations->where('status', true)->count();
            $storedCount = $detail->current_count;
            $quota = $detail->quota;

            $status = $actualCount == $storedCount ? '✅' : '❌';
            $this->command->info("{$status} {$detail->activity->title} - {$detail->theme}");
            $this->command->info("   实际有效报名: {$actualCount}");
            $this->command->info("   存储计数: {$storedCount}");
            $this->command->info("   名额: {$quota}");
            $this->command->info("   报名率: " . round(($actualCount / $quota) * 100, 1) . "%");

            if ($actualCount != $storedCount) {
                $this->command->error("   ⚠️  数据不一致！需要修复");
            }
            $this->command->info('');
        });

        // 性别统计验证
        $this->command->info('👥 性别统计:');
        $maleCount = Registration::where('status', true)->where('gender', 'male')->count();
        $femaleCount = Registration::where('status', true)->where('gender', 'female')->count();
        $this->command->info("男性: {$maleCount}");
        $this->command->info("女性: {$femaleCount}");

        // 来源统计验证
        $this->command->info('');
        $this->command->info('📱 报名来源统计:');
        $sources = Registration::where('status', true)
            ->groupBy('source')
            ->selectRaw('source, count(*) as count')
            ->get();

        foreach ($sources as $source) {
            $this->command->info("{$source->source}: {$source->count}");
        }
    }
}
