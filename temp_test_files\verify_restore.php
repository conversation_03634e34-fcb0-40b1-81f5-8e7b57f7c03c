<?php
/**
 * 验证数据恢复结果
 */

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔍 验证数据恢复结果\n";
echo "==================\n\n";

// 检查cache数据
$cacheCount = DB::table('cache')->count();
echo "Cache记录数: {$cacheCount}\n";

if ($cacheCount > 0) {
    $permissionCache = DB::table('cache')->where('key', 'spatie.permission.cache')->first();
    if ($permissionCache) {
        echo "✅ 权限缓存存在\n";
        $cacheData = unserialize($permissionCache->value);
        if (isset($cacheData['permissions'])) {
            echo "权限数量: " . count($cacheData['permissions']) . "\n";
        }
        if (isset($cacheData['roles'])) {
            echo "角色数量: " . count($cacheData['roles']) . "\n";
        }
    } else {
        echo "❌ 权限缓存不存在\n";
    }
}

// 检查用户数据
$userCount = DB::table('users')->count();
echo "\n用户数量: {$userCount}\n";

$users = DB::table('users')->select('id', 'name', 'email', 'is_active')->get();
foreach ($users as $user) {
    echo "  - ID:{$user->id} {$user->name} ({$user->email}) " . ($user->is_active ? '✅' : '❌') . "\n";
}

// 检查角色数据
$roleCount = DB::table('roles')->count();
echo "\n角色数量: {$roleCount}\n";

$roles = DB::table('roles')->select('id', 'name')->get();
foreach ($roles as $role) {
    echo "  - ID:{$role->id} {$role->name}\n";
}

// 检查权限数据
$permissionCount = DB::table('permissions')->count();
echo "\n权限数量: {$permissionCount}\n";

// 检查用户角色关联
$userRoleCount = DB::table('model_has_roles')->count();
echo "\n用户角色关联数: {$userRoleCount}\n";

$userRoles = DB::table('model_has_roles')
    ->join('users', 'model_has_roles.model_id', '=', 'users.id')
    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
    ->select('users.email', 'roles.name as role_name')
    ->get();

foreach ($userRoles as $ur) {
    echo "  - {$ur->email} → {$ur->role_name}\n";
}

echo "\n🎯 登录测试信息:\n";
echo "URL: http://127.0.0.1:8000/admin\n";
echo "邮箱: <EMAIL>\n";
echo "密码: password\n";

echo "\n✨ 验证完成！\n";
?>
