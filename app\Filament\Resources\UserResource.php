<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use App\Models\Organization;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = '系统管理';

    protected static ?string $navigationLabel = '用户设置';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\FileUpload::make('avatar')
                            ->label('头像')
                            ->image()
                            ->directory('avatars')
                            ->visibility('public')
                            ->imageEditor()
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('name')
                            ->label('姓名')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label('邮箱')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('password')
                            ->label('密码')
                            ->password()
                            ->required(fn (string $operation): bool => $operation === 'create')
                            ->maxLength(255)
                            ->dehydrateStateUsing(fn ($state) => bcrypt($state))
                            ->dehydrated(fn ($state) => filled($state)),
                        Forms\Components\TextInput::make('phone')
                            ->label('手机号')
                            ->tel()
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('实名信息')
                    ->schema([
                        Forms\Components\TextInput::make('real_name')
                            ->label('真实姓名')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('region')
                            ->label('地区归属')
                            ->maxLength(255)
                            ->helperText('如：上海市嘉定区实验小学'),
                        Forms\Components\TextInput::make('school')
                            ->label('学校')
                            ->maxLength(255),
                        Forms\Components\Toggle::make('is_verified')
                            ->label('实名认证')
                            ->default(false),
                    ])->columns(2),

                Forms\Components\Section::make('组织信息')
                    ->schema([
                        SelectTree::make('organization_id')
                            ->label('所属组织')
                            ->relationship('organization', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('选择所属组织'),
                        Forms\Components\TextInput::make('position')
                            ->label('职位')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('employee_id')
                            ->label('员工编号')
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\DatePicker::make('join_date')
                            ->label('入职日期')
                            ->native(false),
                        Forms\Components\Select::make('employment_status')
                            ->label('在职状态')
                            ->options(User::getEmploymentStatusOptions())
                            ->default('active')
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('角色权限')
                    ->schema([
                        Forms\Components\Select::make('roles')
                            ->label('角色')
                            ->multiple()
                            ->relationship('roles', 'name')
                            ->preload()
                            ->helperText('用户将继承所选角色的所有权限'),
                    ])->columns(1),

                Forms\Components\Section::make('状态设置')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true),
                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('邮箱验证时间')
                            ->nullable(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('头像')
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->label('姓名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label('邮箱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('organization.name')
                    ->label('所属组织')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('position')
                    ->label('职位')
                    ->searchable(),
                Tables\Columns\TextColumn::make('employee_id')
                    ->label('员工编号')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('employment_status')
                    ->label('在职状态')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'inactive',
                        'danger' => 'resigned',
                        'secondary' => 'suspended',
                    ])
                    ->formatStateUsing(fn (string $state): string => User::getEmploymentStatusOptions()[$state] ?? $state),
                Tables\Columns\TextColumn::make('roles.name')
                    ->label('角色')
                    ->badge(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('email_verified_at')
                    ->label('邮箱验证时间')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('organization')
                    ->label('所属组织')
                    ->relationship('organization', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('employment_status')
                    ->label('在职状态')
                    ->options(User::getEmploymentStatusOptions()),
                Tables\Filters\SelectFilter::make('roles')
                    ->label('角色')
                    ->relationship('roles', 'name'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('启用')
                    ->falseLabel('禁用')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
