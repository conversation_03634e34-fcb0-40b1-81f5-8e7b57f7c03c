<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class WechatMenu extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'wechat_account_id',
        'parent_id',
        'name',
        'type',
        'key',
        'url',
        'media_id',
        'appid',
        'pagepath',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 菜单类型常量
     */
    const TYPE_CLICK = 'click';
    const TYPE_VIEW = 'view';
    const TYPE_SCANCODE_PUSH = 'scancode_push';
    const TYPE_SCANCODE_WAITMSG = 'scancode_waitmsg';
    const TYPE_PIC_SYSPHOTO = 'pic_sysphoto';
    const TYPE_PIC_PHOTO_OR_ALBUM = 'pic_photo_or_album';
    const TYPE_PIC_WEIXIN = 'pic_weixin';
    const TYPE_LOCATION_SELECT = 'location_select';
    const TYPE_MEDIA_ID = 'media_id';
    const TYPE_VIEW_LIMITED = 'view_limited';
    const TYPE_MINIPROGRAM = 'miniprogram';

    /**
     * 关联微信账号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAccount::class);
    }

    /**
     * 父级菜单
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(WechatMenu::class, 'parent_id');
    }

    /**
     * 子级菜单
     */
    public function children(): HasMany
    {
        return $this->hasMany(WechatMenu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * 获取菜单类型标签
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            self::TYPE_CLICK => '点击事件',
            self::TYPE_VIEW => '跳转链接',
            self::TYPE_SCANCODE_PUSH => '扫码推事件',
            self::TYPE_SCANCODE_WAITMSG => '扫码带提示',
            self::TYPE_PIC_SYSPHOTO => '系统拍照发图',
            self::TYPE_PIC_PHOTO_OR_ALBUM => '拍照或者相册发图',
            self::TYPE_PIC_WEIXIN => '微信相册发图',
            self::TYPE_LOCATION_SELECT => '发送位置',
            self::TYPE_MEDIA_ID => '下发消息',
            self::TYPE_VIEW_LIMITED => '图文消息',
            self::TYPE_MINIPROGRAM => '小程序',
            default => '未知类型'
        };
    }

    /**
     * 获取所有菜单类型
     */
    public static function getMenuTypes(): array
    {
        return [
            self::TYPE_CLICK => '点击事件',
            self::TYPE_VIEW => '跳转链接',
            self::TYPE_SCANCODE_PUSH => '扫码推事件',
            self::TYPE_SCANCODE_WAITMSG => '扫码带提示',
            self::TYPE_PIC_SYSPHOTO => '系统拍照发图',
            self::TYPE_PIC_PHOTO_OR_ALBUM => '拍照或者相册发图',
            self::TYPE_PIC_WEIXIN => '微信相册发图',
            self::TYPE_LOCATION_SELECT => '发送位置',
            self::TYPE_MEDIA_ID => '下发消息',
            self::TYPE_VIEW_LIMITED => '图文消息',
            self::TYPE_MINIPROGRAM => '小程序',
        ];
    }

    /**
     * 检查是否为顶级菜单
     */
    public function isTopLevel(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * 检查是否有子菜单
     */
    public function hasChildren(): bool
    {
        return $this->children()->count() > 0;
    }

    /**
     * 作用域：启用的菜单
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：顶级菜单
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 转换为微信API格式
     */
    public function toWechatFormat(): array
    {
        $menu = [
            'name' => $this->name,
        ];

        if ($this->hasChildren()) {
            $menu['sub_button'] = $this->children->map(function ($child) {
                return $child->toWechatFormat();
            })->toArray();
        } else {
            $menu['type'] = $this->type;
            
            switch ($this->type) {
                case self::TYPE_CLICK:
                    $menu['key'] = $this->key;
                    break;
                case self::TYPE_VIEW:
                    $menu['url'] = $this->url;
                    break;
                case self::TYPE_MINIPROGRAM:
                    $menu['url'] = $this->url;
                    $menu['appid'] = $this->appid;
                    $menu['pagepath'] = $this->pagepath;
                    break;
                case self::TYPE_MEDIA_ID:
                case self::TYPE_VIEW_LIMITED:
                    $menu['media_id'] = $this->media_id;
                    break;
                default:
                    $menu['key'] = $this->key;
                    break;
            }
        }

        return $menu;
    }
}