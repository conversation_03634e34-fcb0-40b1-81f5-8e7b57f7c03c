<?php

namespace App\Filament\Resources\WechatArticleResource\Pages;

use App\Filament\Resources\WechatArticleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewWechatArticle extends ViewRecord
{
    protected static string $resource = WechatArticleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('编辑'),
        ];
    }
}