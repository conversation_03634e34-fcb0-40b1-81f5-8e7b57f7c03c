<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 解码模板参数 ===\n\n";

// 获取现场咨询模板
$template = DB::table('sms_templates')
    ->where('code', 'consultation_notice')
    ->first();

if (!$template) {
    echo "❌ 未找到现场咨询模板\n";
    exit;
}

echo "现场咨询模板信息:\n";
echo "- 模板ID: {$template->id}\n";
echo "- 云片模板ID: {$template->yunpian_template_id}\n";
echo "- 模板描述: {$template->description}\n";
echo "\n";

// 原始参数
$paramsJson = $template->template_params;
echo "原始参数JSON: {$paramsJson}\n\n";

// 手动解码Unicode
function decodeUnicode($str) {
    return preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function ($match) {
        return mb_convert_encoding(pack('H*', $match[1]), 'UTF-8', 'UCS-2BE');
    }, $str);
}

// 解码Unicode字符
$decodedJson = decodeUnicode($paramsJson);
echo "解码后的JSON: {$decodedJson}\n\n";

// 解析JSON
$params = json_decode($decodedJson, true);
if (is_array($params)) {
    echo "✅ JSON解析成功\n";
    echo "模板参数:\n";
    foreach ($params as $key => $value) {
        echo "- {$key}: {$value}\n";
    }
} else {
    echo "❌ JSON解析仍然失败\n";
    // 尝试直接解析原始JSON
    $params = json_decode($paramsJson, true);
    if (is_array($params)) {
        echo "✅ 原始JSON解析成功\n";
        echo "模板参数:\n";
        foreach ($params as $key => $value) {
            // 手动解码每个值
            $decodedValue = decodeUnicode($value);
            echo "- {$key}: {$decodedValue}\n";
        }
    } else {
        echo "❌ 原始JSON也解析失败\n";
        echo "JSON错误: " . json_last_error_msg() . "\n";
    }
}

echo "\n";

// 分析参数映射
echo "参数映射分析:\n";
echo "buildTemplateData方法构建的参数:\n";
echo "- name: 报名者姓名\n";
echo "- topic: 活动标题 (来自activities.title)\n";
echo "- time: 活动时间 (来自activity_details.activity_time)\n";
echo "- address: 活动地点 (来自activity_details.address)\n";
echo "- obj: 目标对象 (来自activity_details.target)\n";
echo "\n";

echo "云片网模板期望的参数 (从模板描述分析):\n";
echo "模板: {$template->description}\n";
echo "\n";

// 从模板描述中提取参数
preg_match_all('/#(\w+)#/', $template->description, $matches);
if (!empty($matches[1])) {
    echo "从模板描述提取的参数:\n";
    foreach ($matches[1] as $param) {
        echo "- {$param}\n";
    }
    
    // 检查参数是否匹配
    $templateParams = $matches[1];
    $buildParams = ['name', 'topic', 'time', 'address', 'obj'];
    
    echo "\n参数匹配检查:\n";
    $missing = array_diff($templateParams, $buildParams);
    $extra = array_diff($buildParams, $templateParams);
    
    if (empty($missing) && empty($extra)) {
        echo "✅ 参数完全匹配\n";
    } else {
        if (!empty($missing)) {
            echo "❌ buildTemplateData缺少参数: " . implode(', ', $missing) . "\n";
        }
        if (!empty($extra)) {
            echo "⚠️ buildTemplateData多余参数: " . implode(', ', $extra) . "\n";
        }
    }
} else {
    echo "❌ 无法从模板描述中提取参数\n";
}

echo "\n";

// 检查实际问题
echo "问题诊断:\n";
echo "根据用户提供的短信内容：\n";
echo "【上海市嘉定区教育学院】现场咨询，主题为：；时间为：；地点为：学校多功能厅；对象为：；请准时参加。\n";
echo "\n";
echo "分析：\n";
echo "- topic (主题) 为空\n";
echo "- time (时间) 为空\n";
echo "- address (地点) 有值：学校多功能厅\n";
echo "- obj (对象) 为空\n";
echo "\n";
echo "可能的原因：\n";
echo "1. activities.title 为空 (导致topic为空)\n";
echo "2. activity_details.activity_time 为空 (导致time为空)\n";
echo "3. activity_details.target 为空 (导致obj为空)\n";
echo "4. activity_details.address 有值 (所以address正常显示)\n";

echo "\n=== 解码完成 ===\n";