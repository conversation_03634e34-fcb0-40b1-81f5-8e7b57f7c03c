<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Models\SystemSetting;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // 每天凌晨2点自动清理过期审计日志
        $schedule->command('audit:clean')
            ->dailyAt('02:00')
            ->when(function () {
                // 检查是否启用自动清理
                return SystemSetting::get('audit_log_auto_cleanup', true);
            })
            ->withoutOverlapping()
            ->runInBackground();
            
        // 每小时采集微信公众号文章
        $schedule->command('wechat:collect-articles')
            ->hourly()
            ->withoutOverlapping()
            ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}