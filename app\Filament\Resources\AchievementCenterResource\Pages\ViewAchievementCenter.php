<?php

namespace App\Filament\Resources\AchievementCenterResource\Pages;

use App\Filament\Resources\AchievementCenterResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewAchievementCenter extends ViewRecord
{
    protected static string $resource = AchievementCenterResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('用户基本信息')
                    ->schema([
                        Infolists\Components\ImageEntry::make('avatar')
                            ->label('头像')
                            ->circular()
                            ->defaultImageUrl('/images/default-avatar.png'),
                        Infolists\Components\TextEntry::make('real_name')
                            ->label('真实姓名'),
                        Infolists\Components\TextEntry::make('name')
                            ->label('用户名'),
                        Infolists\Components\TextEntry::make('email')
                            ->label('邮箱'),
                        Infolists\Components\TextEntry::make('region')
                            ->label('地区归属'),
                        Infolists\Components\TextEntry::make('school')
                            ->label('学校'),
                        Infolists\Components\IconEntry::make('is_verified')
                            ->label('实名认证')
                            ->boolean(),
                    ])->columns(3),

                Infolists\Components\Section::make('积分统计')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_points')
                            ->label('总积分')
                            ->suffix(' 分')
                            ->color('success'),
                        Infolists\Components\TextEntry::make('month_points')
                            ->label('本月积分')
                            ->suffix(' 分')
                            ->color('info'),
                        Infolists\Components\TextEntry::make('quarter_points')
                            ->label('本季度积分')
                            ->suffix(' 分')
                            ->color('warning'),
                        Infolists\Components\TextEntry::make('year_points')
                            ->label('本年积分')
                            ->suffix(' 分')
                            ->color('danger'),
                    ])->columns(4),

                Infolists\Components\Section::make('积分明细')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('points')
                            ->label('积分记录')
                            ->schema([
                                Infolists\Components\TextEntry::make('points')
                                    ->label('积分')
                                    ->suffix(' 分'),
                                Infolists\Components\TextEntry::make('type')
                                    ->label('类型')
                                    ->formatStateUsing(fn ($state) => match($state) {
                                        'login' => '登录积分',
                                        'browse' => '浏览积分',
                                        'video' => '视频积分',
                                        default => $state
                                    }),
                                Infolists\Components\TextEntry::make('source')
                                    ->label('来源'),
                                Infolists\Components\TextEntry::make('earned_date')
                                    ->label('获得日期')
                                    ->date(),
                            ])
                            ->columns(4)
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
