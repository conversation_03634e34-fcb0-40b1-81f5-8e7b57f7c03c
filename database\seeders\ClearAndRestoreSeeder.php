<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClearAndRestoreSeeder extends Seeder
{
    /**
     * 清空现有数据并从旧库恢复
     */
    public function run(): void
    {
        $this->command->info('🗑️ 清空现有用户和权限数据...');

        // 禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // 清空权限相关表
        $permissionTables = [
            'model_has_permissions',
            'model_has_roles', 
            'role_has_permissions',
            'permissions',
            'roles',
            'users'
        ];

        foreach ($permissionTables as $table) {
            DB::table($table)->truncate();
            $this->command->info("  ✅ 清空表: {$table}");
        }

        // 重新启用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('🔄 从旧数据库恢复数据...');

        // 从旧数据库文件中提取并恢复数据
        $this->restoreFromOldDatabase();

        $this->command->info('✅ 数据恢复完成！');
    }

    /**
     * 从旧数据库恢复数据
     */
    private function restoreFromOldDatabase()
    {
        $oldDbFile = 'storage/app/private/private/backups/123_178188_xyz8.sql';
        
        if (!file_exists($oldDbFile)) {
            $this->command->error("❌ 旧数据库文件不存在: {$oldDbFile}");
            return;
        }

        $content = file_get_contents($oldDbFile);

        // 恢复roles表
        $this->restoreTable($content, 'roles');
        
        // 恢复permissions表
        $this->restoreTable($content, 'permissions');
        
        // 恢复users表
        $this->restoreTable($content, 'users');
        
        // 恢复model_has_roles表
        $this->restoreTable($content, 'model_has_roles');
        
        // 恢复role_has_permissions表
        $this->restoreTable($content, 'role_has_permissions');
    }

    /**
     * 恢复单个表的数据
     */
    private function restoreTable($content, $tableName)
    {
        // 查找表的INSERT语句
        $pattern = '/-- Records of ' . preg_quote($tableName) . '.*?INSERT INTO `' . preg_quote($tableName) . '` VALUES (.*?);/s';
        
        if (preg_match($pattern, $content, $matches)) {
            $values = $matches[1];
            
            // 构建完整的INSERT语句
            $sql = "INSERT INTO `{$tableName}` VALUES {$values}";
            
            try {
                DB::statement($sql);
                $this->command->info("  ✅ 恢复表: {$tableName}");
            } catch (\Exception $e) {
                $this->command->error("  ❌ 恢复表失败: {$tableName} - " . $e->getMessage());
                
                // 如果是users表，尝试手动创建管理员
                if ($tableName === 'users') {
                    $this->createManualAdmin();
                }
            }
        } else {
            $this->command->warn("  ⚠️ 未找到表数据: {$tableName}");
            
            // 如果没有找到数据，创建基础数据
            if ($tableName === 'roles') {
                $this->createBasicRoles();
            } elseif ($tableName === 'permissions') {
                $this->createBasicPermissions();
            } elseif ($tableName === 'users') {
                $this->createManualAdmin();
            }
        }
    }

    /**
     * 创建基础角色
     */
    private function createBasicRoles()
    {
        $roles = [
            ['id' => 1, 'name' => 'admin', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'name' => 'super_admin', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()]
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insert($role);
        }
        
        $this->command->info("  ✅ 创建基础角色");
    }

    /**
     * 创建基础权限
     */
    private function createBasicPermissions()
    {
        // 创建一些基础权限
        $permissions = [
            'view_any_user',
            'view_user', 
            'create_user',
            'update_user',
            'delete_user',
            'view_any_role',
            'view_role',
            'create_role',
            'update_role', 
            'delete_role'
        ];

        $id = 1;
        foreach ($permissions as $permission) {
            DB::table('permissions')->insert([
                'id' => $id++,
                'name' => $permission,
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        // 给super_admin角色分配所有权限
        for ($i = 1; $i <= count($permissions); $i++) {
            DB::table('role_has_permissions')->insert([
                'permission_id' => $i,
                'role_id' => 2 // super_admin
            ]);
        }

        $this->command->info("  ✅ 创建基础权限");
    }

    /**
     * 手动创建管理员
     */
    private function createManualAdmin()
    {
        $admins = [
            [
                'id' => 1,
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'is_active' => 1,
                'is_verified' => 1,
                'real_name' => '超级管理员',
                'region' => '系统管理',
                'school' => '管理中心',
                'phone' => '13800000000',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 2,
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'is_active' => 1,
                'is_verified' => 1,
                'real_name' => '管理员',
                'region' => '系统管理', 
                'school' => '管理中心',
                'phone' => '13800000001',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($admins as $admin) {
            DB::table('users')->insert($admin);
        }

        // 分配角色
        DB::table('model_has_roles')->insert([
            ['role_id' => 2, 'model_type' => 'App\\Models\\User', 'model_id' => 1], // super_admin
            ['role_id' => 1, 'model_type' => 'App\\Models\\User', 'model_id' => 2]  // admin
        ]);

        $this->command->info("  ✅ 创建管理员用户");
    }
}
