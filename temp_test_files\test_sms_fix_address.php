<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SmsTemplate;
use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Services\SmsService;

echo "=== 测试修复后的短信发送功能 ===\n\n";

// 1. 获取现场咨询通知模板
$template = SmsTemplate::where('code', 'consultation_notice')->first();

if (!$template) {
    echo "❌ 未找到现场咨询通知模板\n";
    exit(1);
}

echo "✓ 找到模板: {$template->name}\n";
echo "  - 代码: {$template->code}\n";
echo "  - 云片网模板ID: {$template->yunpian_template_id}\n\n";

// 2. 获取使用此模板的活动
$activity = ActivityDetail::where('sms_template_id', $template->id)
    ->with(['activity', 'smsTemplate'])
    ->first();

if (!$activity) {
    echo "❌ 未找到使用此模板的活动\n";
    exit(1);
}

echo "✓ 找到活动: {$activity->activity->title}\n";
echo "  - 活动时间: {$activity->activity_time}\n";
echo "  - 活动地点: {$activity->address}\n";
echo "  - 目标对象: {$activity->target}\n\n";

// 3. 模拟报名数据
$mockRegistration = (object) [
    'name' => '张三',
    'phone' => '13800138000'
];

echo "✓ 模拟报名用户: {$mockRegistration->name} ({$mockRegistration->phone})\n\n";

// 4. 构建模板数据（使用修复后的逻辑）
$templateData = [
    'name' => $mockRegistration->name,
    'topic' => $activity->activity->title,
    'time' => $activity->activity_time,
    'address' => $activity->address, // 修复：使用address而不是location
    'obj' => $activity->target ?? '全体人员'
];

echo "构建的模板数据:\n";
foreach ($templateData as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 5. 验证参数
$validation = $template->validateParams($templateData);
if ($validation === true) {
    echo "✓ 参数验证通过\n\n";
} else {
    echo "❌ 参数验证失败，缺少参数: " . implode(', ', $validation) . "\n\n";
    exit(1);
}

// 6. 模拟SmsService的参数处理
echo "SmsService参数处理:\n";
$tplValue = '';
if (!empty($templateData)) {
    $pairs = [];
    foreach ($templateData as $key => $value) {
        $varName = strpos($key, '#') === false ? "#{$key}#" : $key;
        $pairs[] = urlencode($varName) . '=' . urlencode($value);
    }
    $tplValue = implode('&', $pairs);
}

echo "构建的tpl_value:\n{$tplValue}\n\n";

// 7. 解码验证
parse_str($tplValue, $decodedParams);
echo "解码后的参数:\n";
foreach ($decodedParams as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 8. 检查关键字段
$hasAllFields = true;
$requiredFields = ['#topic#', '#time#', '#address#', '#obj#'];
foreach ($requiredFields as $field) {
    if (!isset($decodedParams[$field]) || empty($decodedParams[$field])) {
        echo "❌ 缺少或为空的字段: {$field}\n";
        $hasAllFields = false;
    } else {
        echo "✓ 字段 {$field}: {$decodedParams[$field]}\n";
    }
}

if ($hasAllFields) {
    echo "\n🎉 所有必需字段都有值，短信模板参数问题已修复！\n";
    echo "\n现在短信内容应该显示为:\n";
    echo "【上海市嘉定区教育学院】现场咨询，主题为：{$decodedParams['#topic#']}；时间为：{$decodedParams['#time#']}；地点为：{$decodedParams['#address#']}；对象为：{$decodedParams['#obj#']}；请准时参加。\n";
} else {
    echo "\n❌ 仍有字段缺失，需要进一步检查\n";
}

echo "\n=== 测试完成 ===\n";