<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CommentResource\Pages;
use App\Models\Comment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
class CommentResource extends Resource
{

    protected static ?string $model = Comment::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?int $navigationSort = 4;

    protected static ?int $navigationGroupSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('评论信息')
                    ->schema([
                        Forms\Components\Select::make('post_id')
                            ->label('文章')
                            ->relationship('post', 'title')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('user_id')
                            ->label('用户')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->nullable(),

                        Forms\Components\Select::make('parent_id')
                            ->label('父评论')
                            ->relationship('parent', 'content')
                            ->searchable()
                            ->preload()
                            ->nullable(),

                        Forms\Components\TextInput::make('author_name')
                            ->label('作者名称')
                            ->maxLength(255)
                            ->required(fn (Forms\Get $get) => !$get('user_id')),

                        Forms\Components\TextInput::make('author_email')
                            ->label('作者邮箱')
                            ->email()
                            ->maxLength(255)
                            ->required(fn (Forms\Get $get) => !$get('user_id')),

                        Forms\Components\Textarea::make('content')
                            ->label('评论内容')
                            ->required()
                            ->maxLength(65535)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('status')
                            ->label('状态')
                            ->options([
                                'pending' => '待审核',
                                'approved' => '已通过',
                                'rejected' => '已拒绝',
                            ])
                            ->default('pending')
                            ->required(),

                        Forms\Components\Toggle::make('is_private')
                            ->label('私密评论')
                            ->default(false),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('post.title')
                    ->label('文章')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('author_name')
                    ->label('作者')
                    ->searchable(),

                Tables\Columns\TextColumn::make('content')
                    ->label('评论内容')
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\SelectColumn::make('status')
                    ->label('状态')
                    ->options([
                        'pending' => '待审核',
                        'approved' => '已通过',
                        'rejected' => '已拒绝',
                    ])
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_private')
                    ->label('私密')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('状态')
                    ->options([
                        'pending' => '待审核',
                        'approved' => '已通过',
                        'rejected' => '已拒绝',
                    ]),

                Tables\Filters\SelectFilter::make('post')
                    ->label('文章')
                    ->relationship('post', 'title'),

                Tables\Filters\TernaryFilter::make('is_private')
                    ->label('私密评论')
                    ->boolean()
                    ->trueLabel('是')
                    ->falseLabel('否')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('通过')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(fn (Comment $record) => $record->status === 'pending')
                    ->action(fn (Comment $record) => $record->update(['status' => 'approved'])),
                Tables\Actions\Action::make('reject')
                    ->label('拒绝')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->visible(fn (Comment $record) => $record->status === 'pending')
                    ->action(fn (Comment $record) => $record->update(['status' => 'rejected'])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('approve')
                        ->label('批量通过')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'approved'])),
                    Tables\Actions\BulkAction::make('reject')
                        ->label('批量拒绝')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'rejected'])),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListComments::route('/'),
            'create' => Pages\CreateComment::route('/create'),
            'edit' => Pages\EditComment::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '评论管理';
    }

    public static function getModelLabel(): string
    {
        return '评论';
    }

    public static function getPluralModelLabel(): string
    {
        return '评论';
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['post', 'user', 'parent']);
    }
}