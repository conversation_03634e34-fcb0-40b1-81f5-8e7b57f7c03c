<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserBehavior extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'target_type',
        'target_id',
        'url',
        'duration',
        'ip_address',
        'user_agent',
        'behavior_date',
    ];

    protected $casts = [
        'behavior_date' => 'date',
        'duration' => 'integer',
    ];

    /**
     * 用户关联
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 行为类型常量
     */
    const ACTION_LOGIN = 'login';
    const ACTION_BROWSE = 'browse';
    const ACTION_VIDEO_VIEW = 'video_view';

    /**
     * 目标类型常量
     */
    const TARGET_POST = 'post';
    const TARGET_VIDEO = 'video';
    const TARGET_PAGE = 'page';

    /**
     * 获取行为类型选项
     */
    public static function getActionOptions()
    {
        return [
            self::ACTION_LOGIN => '登录',
            self::ACTION_BROWSE => '浏览',
            self::ACTION_VIDEO_VIEW => '视频观看',
        ];
    }

    /**
     * 获取目标类型选项
     */
    public static function getTargetTypeOptions()
    {
        return [
            self::TARGET_POST => '文章',
            self::TARGET_VIDEO => '视频',
            self::TARGET_PAGE => '页面',
        ];
    }

    /**
     * 获取行为类型标签
     */
    public function getActionLabel()
    {
        return self::getActionOptions()[$this->action] ?? $this->action;
    }

    /**
     * 获取目标类型标签
     */
    public function getTargetTypeLabel()
    {
        return self::getTargetTypeOptions()[$this->target_type] ?? $this->target_type;
    }
}
