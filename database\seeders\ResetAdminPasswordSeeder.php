<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class ResetAdminPasswordSeeder extends Seeder
{
    /**
     * 重置管理员密码
     */
    public function run(): void
    {
        $this->command->info('🔐 重置管理员密码...');

        // 确保角色存在
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);

        // 重置第一个管理员密码
        $admin1 = User::where('email', '<EMAIL>')->first();
        if ($admin1) {
            $admin1->update([
                'password' => Hash::make('password'),
                'is_active' => true,
                'is_verified' => true,
            ]);
            
            // 确保有正确的角色
            $admin1->syncRoles(['admin', 'super_admin']);
            
            $this->command->info("✅ 重置 <EMAIL> 密码成功");
            $this->command->info("   新密码: password");
        }

        // 重置第二个管理员密码
        $admin2 = User::where('email', '<EMAIL>')->first();
        if ($admin2) {
            $admin2->update([
                'password' => Hash::make('password'),
                'is_active' => true,
                'is_verified' => true,
            ]);
            
            // 确保有正确的角色
            $admin2->syncRoles(['admin', 'super_admin']);
            
            $this->command->info("✅ 重置 <EMAIL> 密码成功");
            $this->command->info("   新密码: password");
        }

        // 如果没有管理员，创建一个
        if (!$admin1 && !$admin2) {
            $newAdmin = User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'is_verified' => true,
                'real_name' => '超级管理员',
                'region' => '系统管理',
                'school' => '管理中心',
                'phone' => '13800000000',
            ]);

            $newAdmin->assignRole(['admin', 'super_admin']);
            
            $this->command->info("✅ 创建新管理员成功");
            $this->command->info("   邮箱: <EMAIL>");
            $this->command->info("   密码: password");
        }

        // 验证登录权限
        $this->validateAdminAccess();
    }

    /**
     * 验证管理员访问权限
     */
    private function validateAdminAccess()
    {
        $this->command->info('');
        $this->command->info('🔍 验证管理员访问权限...');

        $admins = User::whereIn('email', ['<EMAIL>', '<EMAIL>'])->get();

        foreach ($admins as $admin) {
            $this->command->info("检查用户: {$admin->email}");
            
            // 检查基本状态
            $isActive = $admin->is_active;
            $hasAdminRole = $admin->hasRole('admin');
            $hasSuperAdminRole = $admin->hasRole('super_admin');
            
            $this->command->info("  激活状态: " . ($isActive ? '✅' : '❌'));
            $this->command->info("  admin角色: " . ($hasAdminRole ? '✅' : '❌'));
            $this->command->info("  super_admin角色: " . ($hasSuperAdminRole ? '✅' : '❌'));
            
            // 检查密码
            $passwordCorrect = Hash::check('password', $admin->password);
            $this->command->info("  密码正确: " . ($passwordCorrect ? '✅' : '❌'));
            
            // 检查面板访问权限
            try {
                $panel = \Filament\Facades\Filament::getDefaultPanel();
                $canAccess = $admin->canAccessPanel($panel);
                $this->command->info("  可访问后台: " . ($canAccess ? '✅' : '❌'));
            } catch (\Exception $e) {
                $this->command->error("  检查访问权限时出错: " . $e->getMessage());
            }
            
            $this->command->info('');
        }

        $this->command->info('🎯 推荐登录信息:');
        $this->command->info('URL: http://127.0.0.1:8000/admin');
        $this->command->info('邮箱: <EMAIL>');
        $this->command->info('密码: password');
        $this->command->info('');
        $this->command->info('备用登录信息:');
        $this->command->info('邮箱: <EMAIL>');
        $this->command->info('密码: password');
    }
}
