<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SmsTemplate;
use Illuminate\Support\Facades\Log;

echo "=== 云片网API调试测试 ===\n\n";

// 获取配置
$apiKey = config('services.yunpian.api_key');
$baseUrl = 'https://sms.yunpian.com/v2/sms/';

echo "📋 配置信息:\n";
echo "   - API Key: " . (empty($apiKey) ? '❌ 未配置' : '✅ 已配置 (' . substr($apiKey, 0, 8) . '...)') . "\n";
echo "   - Base URL: {$baseUrl}\n\n";

if (empty($apiKey)) {
    echo "❌ 错误: 云片网API Key未配置\n";
    echo "请在 .env 文件中设置 YUNPIAN_API_KEY\n";
    exit(1);
}

// 测试API连接
echo "🔗 测试API连接...\n";

function testApiConnection($apiKey, $baseUrl) {
    $url = $baseUrl . 'user/get.json';
    $data = ['apikey' => $apiKey];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error,
        'verbose' => $verboseLog
    ];
}

$result = testApiConnection($apiKey, $baseUrl);

echo "   - HTTP状态码: {$result['http_code']}\n";
if (!empty($result['error'])) {
    echo "   - cURL错误: {$result['error']}\n";
}

if ($result['response']) {
    $responseData = json_decode($result['response'], true);
    echo "   - 响应数据: " . json_encode($responseData, JSON_UNESCAPED_UNICODE) . "\n";
    
    if (isset($responseData['code'])) {
        if ($responseData['code'] == 0) {
            echo "   ✅ API连接成功\n";
            if (isset($responseData['user'])) {
                echo "   - 账户信息: 余额 {$responseData['user']['balance']} 元\n";
            }
        } else {
            echo "   ❌ API返回错误: {$responseData['msg']}\n";
        }
    }
} else {
    echo "   ❌ 无响应数据\n";
}

echo "\n详细调试信息:\n";
echo $result['verbose'] . "\n";

// 测试模板发送
echo "\n📄 测试模板发送...\n";

$template = SmsTemplate::where('code', 'activity_registration_confirm')->first();
if (!$template) {
    echo "❌ 模板不存在\n";
    exit(1);
}

echo "   - 模板代码: {$template->code}\n";
echo "   - 云片模板ID: {$template->yunpian_template_id}\n";

// 测试数据
$testMobile = '15167356799';
$testData = [
    'name' => '测试用户',
    'topic' => '测试活动',
    'time' => '2025-05-28 15:00',
    'address' => '测试地址',
    'obj' => '测试对象'
];

echo "   - 测试手机号: {$testMobile}\n";
echo "   - 测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";

function testSmsSend($apiKey, $baseUrl, $mobile, $templateId, $data) {
    $url = $baseUrl . 'tpl_single_send.json';
    $postData = [
        'apikey' => $apiKey,
        'mobile' => $mobile,
        'tpl_id' => $templateId,
        'tpl_value' => http_build_query($data)
    ];
    
    echo "📤 发送请求:\n";
    echo "   - URL: {$url}\n";
    echo "   - 参数: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error,
        'verbose' => $verboseLog
    ];
}

$smsResult = testSmsSend($apiKey, $baseUrl, $testMobile, $template->yunpian_template_id, $testData);

echo "📥 响应结果:\n";
echo "   - HTTP状态码: {$smsResult['http_code']}\n";
if (!empty($smsResult['error'])) {
    echo "   - cURL错误: {$smsResult['error']}\n";
}

if ($smsResult['response']) {
    $responseData = json_decode($smsResult['response'], true);
    echo "   - 响应数据: " . json_encode($responseData, JSON_UNESCAPED_UNICODE) . "\n";
    
    if (isset($responseData['code'])) {
        if ($responseData['code'] == 0) {
            echo "   ✅ 短信发送成功\n";
        } else {
            echo "   ❌ 短信发送失败: {$responseData['msg']}\n";
            
            // 常见错误码解释
            $errorCodes = [
                1 => '参数错误',
                2 => 'apikey错误',
                3 => '手机号错误',
                4 => '内容包含敏感词',
                5 => '余额不足',
                6 => '定时发送时间格式错误',
                7 => '提交信息含有敏感词',
                8 => '发送内容过长',
                9 => '发送号码过多',
                10 => '内容不能为空',
                11 => '手机号不能为空',
                12 => '模板不存在',
                13 => '模板变量格式错误',
                14 => '模板变量个数不匹配',
                15 => '模板审核中',
                16 => '模板审核失败',
                17 => '账户被禁用',
                18 => 'IP被禁用',
                19 => '账户类型错误',
                20 => '接口被禁用'
            ];
            
            if (isset($errorCodes[$responseData['code']])) {
                echo "   - 错误说明: {$errorCodes[$responseData['code']]}\n";
            }
        }
    }
} else {
    echo "   ❌ 无响应数据\n";
}

echo "\n详细调试信息:\n";
echo $smsResult['verbose'] . "\n";

echo "\n=== 调试完成 ===\n";