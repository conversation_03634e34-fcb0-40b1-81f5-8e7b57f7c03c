{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bezhansalleh/filament-panel-switch": "^1.1", "bezhansalleh/filament-shield": "^3.3", "charrafimed/global-search-modal": "^3.7", "codewithdennis/filament-select-tree": "^3.1", "endroid/qr-code": "^5.0", "filament/filament": "^3.2", "flowframe/laravel-trend": "^0.4.0", "hugomyb/filament-media-action": "^3.1", "icetalker/filament-table-repeater": "^1.4", "jiannei/laravel-response": "^6.0", "kahusoftware/filament-ckeditor-field": "^0.0.4@alpha", "kalnoy/nestedset": "^6.0", "laravel/framework": "^11.31", "laravel/pulse": "^1.4", "laravel/tinker": "^2.9", "maatwebsite/excel": "^3.1", "mews/captcha": "*", "opcodesio/log-viewer": "^3.17", "overtrue/easy-sms": "^3.1", "overtrue/laravel-wechat": "^7.4", "overtrue/pinyin": "^5.0", "overtrue/wechat": "^6.17", "owen-it/laravel-auditing": "^14.0", "rawilk/filament-quill": "^1.0", "simplesoftwareio/simple-qrcode": "^4.2", "solution-forest/filament-tree": "^2.1", "spatie/laravel-medialibrary": "^11.12", "spatie/laravel-query-builder": "^6.3", "spatie/laravel-route-attributes": "^1.25", "w7corp/easywechat": "^6.17"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}