<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Http\Controllers\RegistrationController;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;

echo "=== 修复短信空值问题 ===\n\n";

// 1. 检查并修复Activity表中的空title
echo "1. 检查Activity表中的空title...\n";
$emptyTitleActivities = Activity::where('title', '')->orWhereNull('title')->get();

if ($emptyTitleActivities->count() > 0) {
    echo "   发现 {$emptyTitleActivities->count()} 个空标题的活动\n";
    
    foreach ($emptyTitleActivities as $activity) {
        // 尝试从关联的ActivityDetail中获取theme作为标题
        $activityDetail = ActivityDetail::where('activity_id', $activity->id)->first();
        
        if ($activityDetail && !empty($activityDetail->theme)) {
            $activity->title = $activityDetail->theme;
            $activity->save();
            echo "   - 活动ID {$activity->id}: 使用theme '{$activityDetail->theme}' 作为标题\n";
        } else {
            // 使用默认标题
            $defaultTitle = '活动通知 - ' . $activity->id;
            $activity->title = $defaultTitle;
            $activity->save();
            echo "   - 活动ID {$activity->id}: 使用默认标题 '{$defaultTitle}'\n";
        }
    }
} else {
    echo "   ✅ 所有活动都有标题\n";
}

// 2. 检查并修复ActivityDetail表中的空值
echo "\n2. 检查ActivityDetail表中的空值...\n";
$activityDetails = ActivityDetail::all();

foreach ($activityDetails as $detail) {
    $needsUpdate = false;
    $updates = [];
    
    // 检查target字段
    if (empty($detail->target)) {
        $detail->target = '全体人员';
        $updates[] = 'target设为默认值';
        $needsUpdate = true;
    }
    
    // 检查address字段
    if (empty($detail->address)) {
        $detail->address = '待定';
        $updates[] = 'address设为默认值';
        $needsUpdate = true;
    }
    
    // 检查theme字段
    if (empty($detail->theme)) {
        $detail->theme = $detail->activity ? $detail->activity->title : '活动详情';
        $updates[] = 'theme设为活动标题';
        $needsUpdate = true;
    }
    
    if ($needsUpdate) {
        $detail->save();
        echo "   - ActivityDetail ID {$detail->id}: " . implode(', ', $updates) . "\n";
    }
}

echo "\n3. 创建改进的buildTemplateData方法...\n";

// 创建一个改进的buildTemplateData函数
function buildImprovedTemplateData($registration, $activityDetail, $templateCode = 'activity_registration_confirm')
{
    // 确保所有必要的关联都已加载
    if (!$activityDetail->activity) {
        $activityDetail->load('activity');
    }
    
    // 基础数据，带有默认值保护
    $baseData = [
        'name' => $registration->name ?: '用户',
        'topic' => $activityDetail->activity->title ?: $activityDetail->theme ?: '活动通知',
        'time' => $activityDetail->activity_time ?: '待定',
        'address' => $activityDetail->address ?: '待定',
        'obj' => $activityDetail->target ?: '全体人员'
    ];
    
    // 根据模板类型返回相应数据
    switch ($templateCode) {
        case 'activity_registration_confirm':
        case 'consultation_notice':
            return [
                'name' => $baseData['name'],
                'topic' => $baseData['topic'],
                'time' => $baseData['time'],
                'address' => $baseData['address'],
                'obj' => $baseData['obj']
            ];
        
        case 'summer_training_success':
            return [
                'name' => $baseData['topic'] // 课程名称
            ];
        
        case 'account_approved':
        case 'account_rejected':
            return [
                'name' => $baseData['name']
            ];
        
        default:
            return $baseData;
    }
}

echo "\n4. 测试改进后的模板数据构建...\n";

// 获取一个测试用的报名记录
$testRegistration = Registration::with('activityDetail.activity')->first();

if ($testRegistration) {
    echo "   使用报名ID: {$testRegistration->id}\n";
    
    $templateData = buildImprovedTemplateData(
        $testRegistration, 
        $testRegistration->activityDetail, 
        'consultation_notice'
    );
    
    echo "   改进后的模板数据:\n";
    foreach ($templateData as $key => $value) {
        echo "   - {$key}: '{$value}'\n";
        
        // 检查是否还有空值
        if (empty($value)) {
            echo "     ⚠️ 警告: {$key} 仍然为空\n";
        }
    }
    
    // 构建SMS参数字符串
    $pairs = [];
    foreach ($templateData as $key => $value) {
        $smsKey = "#{$key}#";
        $pairs[] = urlencode($smsKey) . '=' . urlencode($value);
    }
    $tplValue = implode('&', $pairs);
    
    echo "\n   构建的tpl_value参数:\n";
    echo "   {$tplValue}\n";
    
    // 检查是否包含空值编码
    if (strpos($tplValue, '=&') !== false || strpos($tplValue, '=%23') !== false) {
        echo "   ⚠️ 警告: tpl_value中仍包含空值\n";
    } else {
        echo "   ✅ tpl_value中没有空值\n";
    }
} else {
    echo "   ❌ 没有找到测试用的报名记录\n";
}

echo "\n=== 修复完成 ===\n";
echo "\n建议:\n";
echo "1. 更新RegistrationController中的buildTemplateData方法\n";
echo "2. 在创建Activity和ActivityDetail时添加必填字段验证\n";
echo "3. 定期检查数据完整性\n";
echo "4. 云片网短信模板应该能够处理所有参数都有值的情况\n";