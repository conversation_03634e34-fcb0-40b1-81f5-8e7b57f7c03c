# 短信模板后台配置指南

## 概述

本指南详细说明如何在后台管理系统中配置和使用短信模板功能。

## 1. 短信模板管理

### 1.1 访问短信模板管理

1. 登录后台管理系统
2. 在左侧菜单中找到「短信模板」或「SMS Templates」
3. 点击进入短信模板列表页面

### 1.2 短信模板字段说明

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 模板名称 | 模板的显示名称 | 验证码短信 |
| 模板代码 | 程序中使用的唯一标识 | verification_code |
| 云片网模板ID | 云片网平台的模板ID | 12345 |
| 模板描述 | 模板的详细说明 | 用于发送验证码的短信模板 |
| 模板分类 | 模板的分类 | 验证码、账号管理、活动通知等 |
| 模板参数 | JSON格式的参数定义 | {"code":"验证码"} |
| 状态 | 模板是否启用 | 启用/禁用 |

### 1.3 创建新的短信模板

1. 点击「新建」或「Create」按钮
2. 填写模板信息：
   ```
   模板名称: 活动通知短信
   模板代码: activity_notification
   云片网模板ID: 67890
   模板描述: 用于发送活动通知的短信
   模板分类: 活动通知
   模板参数: {
     "topic": "活动主题",
     "time": "活动时间",
     "address": "活动地点",
     "obj": "参与对象"
   }
   状态: 启用
   ```
3. 点击「保存」

## 2. 在活动详情中配置短信模板

### 2.1 访问活动详情管理

1. 在后台菜单中找到「活动详情」或「Activity Details」
2. 选择要配置的活动，点击「编辑」

### 2.2 设置短信模板

1. 在活动详情编辑页面中找到「短信模板」字段
2. 从下拉列表中选择合适的短信模板
3. 系统会自动根据活动信息构建短信参数

### 2.3 活动信息与短信参数的映射

| 活动字段 | 短信参数 | 说明 |
|----------|----------|------|
| theme | #topic# | 活动主题 |
| activity_time | #time# | 活动时间 |
| address | #address# | 活动地点 |
| target | #obj# | 参与对象 |

## 3. 可用的短信模板类型

### 3.1 验证码类模板

**模板代码**: `verification_code`
**参数**: 
- `#code#` - 验证码

**示例内容**: 
```
【上海市嘉定区教育学院】验证码#code#，您正在进行嘉定幸福课程身份验证，感谢关注
```

### 3.2 账号管理类模板

#### 账号审核通过
**模板代码**: `account_approved`
**参数**: 
- `#name#` - 用户姓名

**示例内容**: 
```
【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已通过审核，感谢关注
```

#### 账号审核未通过
**模板代码**: `account_rejected`
**参数**: 
- `#name#` - 用户姓名

**示例内容**: 
```
【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号未能通过审核，可能的原因是您不是本课程的适用对象，感谢关注！
```

#### 账号开通
**模板代码**: `account_created`
**参数**: 
- `#name#` - 用户姓名
- `#pwd#` - 初始密码

**示例内容**: 
```
【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已开通，请用微信关注公众号嘉定区幸福课程，并用手机号登录，密码为#pwd#
```

### 3.3 活动通知类模板

#### 现场咨询通知
**模板代码**: `activity_notification`
**参数**: 
- `#topic#` - 活动主题
- `#time#` - 活动时间
- `#address#` - 活动地点
- `#obj#` - 参与对象

**示例内容**: 
```
【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。
```

#### 活动报名确认
**模板代码**: `activity_registration_confirm`
**参数**: 
- `#name#` - 参与者姓名
- `#topic#` - 活动主题
- `#time#` - 活动时间
- `#address#` - 活动地点

#### 培训报名成功
**模板代码**: `training_registration_success`
**参数**: 
- `#name#` - 课程名称

**示例内容**: 
```
【上海市嘉定区教育学院】您好，您报的暑期大培训课程"#name#"已报名成功，请关注开课时间，准时参加！
```

## 4. 系统配置

### 4.1 云片网API配置

在 `.env` 文件中配置云片网API密钥：
```
YUNPIAN_API_KEY=your_yunpian_api_key_here
```

### 4.2 服务配置

在 `config/services.php` 中确保有以下配置：
```php
'yunpian' => [
    'api_key' => env('YUNPIAN_API_KEY'),
],
```

## 5. 使用流程

### 5.1 管理员操作流程

1. **创建短信模板**
   - 在后台创建或编辑短信模板
   - 设置模板参数和云片网模板ID

2. **配置活动短信**
   - 编辑活动详情
   - 选择对应的短信模板
   - 保存配置

3. **发送短信**
   - 系统会在适当时机自动发送短信
   - 或通过程序接口手动触发发送

### 5.2 系统自动处理

1. **参数构建**
   - 系统根据活动信息自动构建短信参数
   - 验证参数完整性

2. **短信发送**
   - 调用云片网API发送短信
   - 记录发送日志

## 6. 注意事项

1. **模板参数格式**
   - 模板参数必须是有效的JSON格式
   - 参数键名要与短信内容中的变量对应

2. **云片网模板**
   - 确保云片网平台已审核通过对应模板
   - 模板ID必须正确

3. **参数验证**
   - 系统会自动验证必需参数是否完整
   - 缺少参数时会阻止发送并记录错误

4. **权限管理**
   - 确保操作用户有相应的权限
   - 短信发送会产生费用，需谨慎操作

## 7. 故障排除

### 7.1 常见问题

**问题**: 短信发送失败
**解决方案**: 
1. 检查云片网API密钥是否正确
2. 确认模板ID是否有效
3. 验证手机号格式是否正确
4. 查看系统日志获取详细错误信息

**问题**: 参数验证失败
**解决方案**: 
1. 检查模板参数JSON格式是否正确
2. 确认活动信息是否完整
3. 验证参数键名是否与模板变量匹配

### 7.2 日志查看

系统日志位置: `storage/logs/laravel.log`

搜索关键词:
- `短信发送成功`
- `短信发送失败`
- `短信发送异常`

## 8. 扩展功能

### 8.1 添加新的模板类型

1. 在云片网平台创建新模板
2. 在后台添加对应的短信模板记录
3. 根据需要修改参数构建逻辑

### 8.2 自定义参数映射

可以在模型中添加自定义方法来处理特殊的参数映射需求。

---

**更新时间**: 2024年1月
**版本**: 1.0