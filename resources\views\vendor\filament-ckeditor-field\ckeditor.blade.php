@php
    $name = $getName() ?? config('filament-ckeditor-field.upload_url');
    $uploadUrl = $getUploadUrl();
    $placeholder = $getPlaceholder();
    $isConcealed = $isConcealed();
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <x-filament::input.wrapper :valid="$errors->count() === 0">
        <div wire:ignore>
            <!-- 加载自定义视频上传插件 -->
            <script src="{{ asset('js/ckeditor-video-upload.js') }}"></script>

            <script type="text/javascript">
                // 添加视频上传按钮到工具栏
                function addVideoUploadButton(editor) {
                    // 查找工具栏
                    const toolbar = document.querySelector('.ck-toolbar');
                    if (!toolbar) return;

                    // 创建视频上传按钮
                    const videoButton = document.createElement('button');
                    videoButton.className = 'ck ck-button ck-off';
                    videoButton.type = 'button';
                    videoButton.title = '上传视频';
                    videoButton.innerHTML = `
                        <svg class="ck ck-icon ck-button__icon" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H2zm0 2h16v10H2V5z"/>
                            <path d="M8 7l4 2.5L8 12V7z"/>
                        </svg>
                        <span class="ck ck-button__label">上传视频</span>
                    `;

                    // 添加点击事件
                    videoButton.addEventListener('click', () => {
                        createVideoUploadButton()(editor);
                    });

                    // 插入到工具栏中（在图片按钮后面）
                    const imageButton = toolbar.querySelector('[title*="图"]') || toolbar.querySelector('[title*="Image"]');
                    if (imageButton && imageButton.parentNode) {
                        imageButton.parentNode.insertBefore(videoButton, imageButton.nextSibling);
                    } else {
                        // 如果找不到图片按钮，就添加到工具栏末尾
                        toolbar.appendChild(videoButton);
                    }
                }

                // Initialize the instance and event listener flags if not already set
                if (!window.ckeditorInstances["ckeditor-{{ $name }}"]) {
                    window.ckeditorInstances["ckeditor-{{ $name }}"] = {
                        instance: null,
                        eventListenerAdded: false
                    };
                }

                function createCKEditor() {
                    // Destroy existing editor to prevent duplicates
                    if (window.ckeditorInstances["ckeditor-{{ $name }}"].instance) {
                        // destroyCKEditor();
                        return;
                    }

                    // Create new editor instance
                    ClassicEditor
                        .create(document.querySelector('#ckeditor-{{ $name }}'), {
                            language: 'zh-cn',
                            plugins: [
                                AccessibilityHelp,
                                Alignment,
                                Autoformat,
                                AutoImage,
                                AutoLink,
                                Autosave,
                                BlockQuote,
                                Bold,
                                Code,
                                CodeBlock,
                                Essentials,
                                FindAndReplace,
                                FontBackgroundColor,
                                FontColor,
                                FontFamily,
                                FontSize,
                                GeneralHtmlSupport,
                                Heading,
                                Highlight,
                                HorizontalLine,
                                HtmlComment,
                                HtmlEmbed,
                                ImageBlock,
                                ImageCaption,
                                ImageInline,
                                ImageInsert,
                                ImageInsertViaUrl,
                                ImageResize,
                                ImageStyle,
                                ImageTextAlternative,
                                ImageToolbar,
                                ImageUpload,
                                Indent,
                                IndentBlock,
                                Italic,
                                Link,
                                LinkImage,
                                List,
                                ListProperties,
                                MediaEmbed,
                                PageBreak,
                                Paragraph,
                                PasteFromOffice,
                                RemoveFormat,
                                SelectAll,
                                ShowBlocks,
                                SimpleUploadAdapter,
                                SourceEditing,
                                SpecialCharacters,
                                SpecialCharactersArrows,
                                SpecialCharactersCurrency,
                                SpecialCharactersEssentials,
                                SpecialCharactersLatin,
                                SpecialCharactersMathematical,
                                SpecialCharactersText,
                                Strikethrough,
                                Style,
                                Subscript,
                                Superscript,
                                Table,
                                TableCaption,
                                TableCellProperties,
                                TableColumnResize,
                                TableProperties,
                                TableToolbar,
                                TextTransformation,
                                TodoList,
                                Underline,
                                Undo
                            ],
                            toolbar: {
                                items: [
                                    'undo',
                                    'redo',
                                    '|',
                                    'sourceEditing',
                                    'showBlocks',
                                    '|',
                                    'heading',
                                    'style',
                                    '|',
                                    'fontSize',
                                    'fontFamily',
                                    'fontColor',
                                    'fontBackgroundColor',
                                    '|',
                                    'bold',
                                    'italic',
                                    'underline',
                                    '|',
                                    'link',
                                    'insertImage',
                                    'mediaEmbed',
                                    'insertTable',
                                    'highlight',
                                    'blockQuote',
                                    'codeBlock',
                                    '|',
                                    'alignment',
                                    '|',
                                    'bulletedList',
                                    'numberedList',
                                    'todoList',
                                    'outdent',
                                    'indent'
                                ],
                                shouldNotGroupWhenFull: false
                            },
                            autosave: {
                                save( editor ) {
                                    Livewire.dispatch('contentUpdated', { content: editor.getData(), editor: 'ckeditor-{{ $name }}' })
                                }
                            },
                            fontFamily: {
                                supportAllValues: true
                            },
                            fontSize: {
                                options: [10, 12, 14, 'default', 18, 20, 22],
                                supportAllValues: true
                            },
                            heading: {
                                options: [
                                    {
                                        model: 'paragraph',
                                        title: 'Paragraph',
                                        class: 'ck-heading_paragraph'
                                    },
                                    {
                                        model: 'heading1',
                                        view: 'h1',
                                        title: 'Heading 1',
                                        class: 'ck-heading_heading1'
                                    },
                                    {
                                        model: 'heading2',
                                        view: 'h2',
                                        title: 'Heading 2',
                                        class: 'ck-heading_heading2'
                                    },
                                    {
                                        model: 'heading3',
                                        view: 'h3',
                                        title: 'Heading 3',
                                        class: 'ck-heading_heading3'
                                    },
                                    {
                                        model: 'heading4',
                                        view: 'h4',
                                        title: 'Heading 4',
                                        class: 'ck-heading_heading4'
                                    },
                                    {
                                        model: 'heading5',
                                        view: 'h5',
                                        title: 'Heading 5',
                                        class: 'ck-heading_heading5'
                                    },
                                    {
                                        model: 'heading6',
                                        view: 'h6',
                                        title: 'Heading 6',
                                        class: 'ck-heading_heading6'
                                    }
                                ]
                            },
                            htmlSupport: {
                                allow: [
                                    {
                                        name: /^.*$/,
                                        styles: true,
                                        attributes: true,
                                        classes: true
                                    }
                                ]
                            },
                            image: {
                                toolbar: [
                                    'toggleImageCaption',
                                    'imageTextAlternative',
                                    '|',
                                    'imageStyle:inline',
                                    'imageStyle:wrapText',
                                    'imageStyle:breakText',
                                    '|',
                                    'resizeImage'
                                ]
                            },
                            link: {
                                addTargetToExternalLinks: true,
                                defaultProtocol: 'https://',
                                decorators: {
                                    toggleDownloadable: {
                                        mode: 'manual',
                                        label: 'Downloadable',
                                        attributes: {
                                            download: 'file'
                                        }
                                    }
                                }
                            },
                            list: {
                                properties: {
                                    styles: true,
                                    startIndex: true,
                                    reversed: true
                                }
                            },
                            menuBar: {
                                isVisible: true
                            },
                            placeholder: '{{ $placeholder }}',
                            style: {
                                definitions: [
                                    {
                                        name: 'Article category',
                                        element: 'h3',
                                        classes: ['category']
                                    },
                                    {
                                        name: 'Title',
                                        element: 'h2',
                                        classes: ['document-title']
                                    },
                                    {
                                        name: 'Subtitle',
                                        element: 'h3',
                                        classes: ['document-subtitle']
                                    },
                                    {
                                        name: 'Info box',
                                        element: 'p',
                                        classes: ['info-box']
                                    },
                                    {
                                        name: 'Side quote',
                                        element: 'blockquote',
                                        classes: ['side-quote']
                                    },
                                    {
                                        name: 'Marker',
                                        element: 'span',
                                        classes: ['marker']
                                    },
                                    {
                                        name: 'Spoiler',
                                        element: 'span',
                                        classes: ['spoiler']
                                    },
                                    {
                                        name: 'Code (dark)',
                                        element: 'pre',
                                        classes: ['fancy-code', 'fancy-code-dark']
                                    },
                                    {
                                        name: 'Code (bright)',
                                        element: 'pre',
                                        classes: ['fancy-code', 'fancy-code-bright']
                                    }
                                ]
                            },
                            table: {
                                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
                            },
                            @isset($uploadUrl)

                            simpleUpload: {
                                uploadUrl: '{{ $uploadUrl }}',
                                withCredentials: true,
                                headers: {
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                }
                            }

                            @endisset
                        })
                        .then(editor => {
                            window.ckeditorInstances["ckeditor-{{ $name }}"].instance = editor;

                            // Find the main ckeditor class and add some helpful class names to it
                            {{-- todo: $isReadOnly() --}}
                            {{-- todo: @if($isRequired() && (! $isConcealed)) @endif --}}
                            document.getElementsByClassName('ck-editor__main')[0].classList.add('prose', 'max-w-none', 'dark:prose-invert')

                            // 添加自定义视频上传按钮
                            addVideoUploadButton(editor);

                            // Listen to changes
                            editor.model.document.on('change:data', () => {
                                // Emit Livewire event
                                Livewire.dispatch('contentUpdated', { content: editor.getData(), editor: 'ckeditor-{{ $name }}' })
                            });
                        })
                        .catch(err => {
                            console.error(err);
                        });
                }

                function destroyCKEditor() {
                    if (window.ckeditorInstances["ckeditor-{{ $name }}"].instance) {
                        window.ckeditorInstances["ckeditor-{{ $name }}"].instance.destroy()
                            .then(() => {
                                window.ckeditorInstances["ckeditor-{{ $name }}"].instance = null;
                            })
                            .catch(err => {
                                console.error('Failed to destroy editor:', err);
                            });
                    }
                }
            </script>
            <div
                x-data="{
                    state: $wire.$entangle('{{ $getStatePath() }}'),
                    init() {
                        // Remove existing event listeners to prevent duplicates
                        document.removeEventListener('livewire:navigated', createCKEditor);
                        document.removeEventListener('livewire:navigate', destroyCKEditor);

                        // Add event listeners if not already added
                        if (!window.ckeditorInstances['ckeditor-{{ $name }}'].eventListenerAdded) {
                            // todo: Look into the { once: true } option if necessary
                            document.addEventListener('livewire:navigated', createCKEditor);
                            document.addEventListener('livewire:navigate', destroyCKEditor);
                            window.ckeditorInstances['ckeditor-{{ $name }}'].eventListenerAdded = true;
                        }

                        Livewire.on('contentUpdated', (payload) => {
                            this.state = payload.content;
                        });
                    }
                }"
                x-load-js="[@js(\Filament\Support\Facades\FilamentAsset::getScriptSrc('filament-ckeditor-field', package: 'kahusoftware/filament-ckeditor-field'))]"
                x-load-css="[@js(\Filament\Support\Facades\FilamentAsset::getStyleHref('filament-ckeditor-field', package: 'kahusoftware/filament-ckeditor-field'))]"
            >
                <textarea
                    id="ckeditor-{{ $name }}"
                    name="{{ $name }}"
                    x-model="state"
                ></textarea>
            </div>
        </div>
    </x-filament::input.wrapper>
</x-dynamic-component>
