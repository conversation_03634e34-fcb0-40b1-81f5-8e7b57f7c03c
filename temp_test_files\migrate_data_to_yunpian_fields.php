<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

echo "=== 数据迁移到云片网字段 ===\n";

try {
    // 开始事务
    DB::beginTransaction();
    
    // 1. 迁移theme数据到topic字段
    $themeUpdateCount = DB::table('activity_details')
        ->whereNotNull('theme')
        ->where('theme', '!=', '')
        ->whereNull('topic')
        ->update(['topic' => DB::raw('theme')]);
    
    echo "从theme迁移到topic的记录数: {$themeUpdateCount}\n";
    
    // 2. 迁移target数据到target_audience字段
    $targetUpdateCount = DB::table('activity_details')
        ->whereNotNull('target')
        ->where('target', '!=', '')
        ->whereNull('target_audience')
        ->update(['target_audience' => DB::raw('target')]);
    
    echo "从target迁移到target_audience的记录数: {$targetUpdateCount}\n";
    
    // 3. 对于topic字段为空的记录，使用activity.title填充
    $titleUpdateCount = DB::table('activity_details')
        ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
        ->whereNull('activity_details.topic')
        ->whereNotNull('activities.title')
        ->where('activities.title', '!=', '')
        ->update(['activity_details.topic' => DB::raw('activities.title')]);
    
    echo "使用activity.title填充topic的记录数: {$titleUpdateCount}\n";
    
    // 4. 检查迁移结果
    $totalRecords = DB::table('activity_details')->count();
    $topicFilledRecords = DB::table('activity_details')->whereNotNull('topic')->where('topic', '!=', '')->count();
    $targetAudienceFilledRecords = DB::table('activity_details')->whereNotNull('target_audience')->where('target_audience', '!=', '')->count();
    
    echo "\n=== 迁移结果统计 ===\n";
    echo "总记录数: {$totalRecords}\n";
    echo "topic字段已填充记录数: {$topicFilledRecords}\n";
    echo "target_audience字段已填充记录数: {$targetAudienceFilledRecords}\n";
    
    // 5. 显示一些示例数据
    $sampleData = DB::table('activity_details')
        ->select('id', 'theme', 'topic', 'target', 'target_audience')
        ->limit(5)
        ->get();
    
    echo "\n=== 示例数据 ===\n";
    foreach ($sampleData as $record) {
        echo "ID: {$record->id}, theme: '{$record->theme}' -> topic: '{$record->topic}', target: '{$record->target}' -> target_audience: '{$record->target_audience}'\n";
    }
    
    // 提交事务
    DB::commit();
    echo "\n✓ 数据迁移成功完成\n";
    
} catch (Exception $e) {
    // 回滚事务
    DB::rollback();
    echo "✗ 数据迁移失败: " . $e->getMessage() . "\n";
    Log::error('数据迁移失败', ['error' => $e->getMessage()]);
}

echo "\n=== 迁移完成 ===\n";
