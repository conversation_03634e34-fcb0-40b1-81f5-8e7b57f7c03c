<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ArticleVersion extends Model
{
    protected $fillable = [
        'article_id',
        'version',
        'title',
        'content',
        'summary',
        'cover_image',
        'meta_data',
        'tag',
        'remark',
        'created_by',
    ];

    protected $casts = [
        'content' => 'array',
        'meta_data' => 'array',
    ];

    public function article(): BelongsTo
    {
        return $this->belongsTo(Article::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function getVersionDiff(ArticleVersion $otherVersion): array
    {
        return [
            'title' => $this->getDiff($this->title, $otherVersion->title),
            'content' => $this->getDiff($this->content, $otherVersion->content),
            'summary' => $this->getDiff($this->summary, $otherVersion->summary),
        ];
    }

    protected function getDiff($old, $new): array
    {
        if (is_array($old)) {
            return array_diff_assoc($old, $new);
        }
        return $old !== $new ? ['old' => $old, 'new' => $new] : [];
    }
} 