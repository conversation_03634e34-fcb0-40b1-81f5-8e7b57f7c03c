#!/bin/bash

# 短信模板更新脚本
# 用于更新数据库中的短信模板数据

echo "开始更新短信模板数据..."

# 检查PHP和Artisan是否可用
if ! command -v php &> /dev/null; then
    echo "错误: PHP 未安装或不在PATH中"
    exit 1
fi

if [ ! -f "artisan" ]; then
    echo "错误: 未找到 artisan 文件，请确保在Laravel项目根目录中运行此脚本"
    exit 1
fi

# 运行数据库种子
echo "正在执行短信模板种子数据..."
php artisan db:seed --class=ComprehensiveSmsTemplateSeeder

if [ $? -eq 0 ]; then
    echo "✓ 短信模板数据更新成功"
else
    echo "✗ 短信模板数据更新失败"
    exit 1
fi

# 运行测试脚本验证
echo "正在验证短信模板系统..."
php test_sms_template_system.php

if [ $? -eq 0 ]; then
    echo "✓ 短信模板系统验证通过"
else
    echo "✗ 短信模板系统验证失败"
    exit 1
fi

echo "短信模板系统更新完成！"
echo ""
echo "使用说明:"
echo "1. 在后台管理中选择活动详情"
echo "2. 设置 sms_template_id 字段选择短信模板"
echo "3. 系统会自动根据活动信息构建短信参数"
echo "4. 支持的模板变量: #name#, #code#, #topic#, #time#, #address#, #obj#, #pwd#"