<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\AdministrativeInstitution;
use Exception;

class ClearAndReimportAdministrativeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:clear-reimport-data {--force : 强制执行，不询问确认}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清空行政区划数据并重新导入，确保无重复数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 开始清空并重新导入行政区划数据...');
        
        // 确认操作
        if (!$this->option('force')) {
            if (!$this->confirm('⚠️  此操作将清空所有行政区划数据并重新导入，是否继续？')) {
                $this->info('操作已取消');
                return 0;
            }
        }
        
        try {
            // 1. 清空现有数据
            $this->clearExistingData();
            
            // 2. 重新导入数据
            $this->reimportData();
            
            // 3. 验证数据完整性
            $this->validateData();
            
            $this->info('✅ 行政区划数据清空并重新导入完成！');
            
        } catch (Exception $e) {
            $this->error('❌ 操作失败: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 清空现有数据
     */
    private function clearExistingData()
    {
        $this->info('🗑️  正在清空现有行政区划数据...');
        
        try {
            // 禁用外键约束检查
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            
            // 清空行政区划表
            DB::table('administrative_institutions')->truncate();
            
            // 重新启用外键约束检查
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            
            $this->info('✅ 现有数据清空完成');
            
        } catch (Exception $e) {
            throw new Exception('清空数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 重新导入数据
     */
    private function reimportData()
    {
        $this->info('📥 正在重新导入行政区划数据...');
        
        // 读取JS文件内容
        $jsFilePath = database_path('area_format_object.level4.js');
        
        if (!file_exists($jsFilePath)) {
            throw new Exception('数据文件不存在: ' . $jsFilePath);
        }
        
        $jsContent = file_get_contents($jsFilePath);
        
        // 提取数据部分
        if (preg_match('/var data=({.*?});\s*return/s', $jsContent, $matches)) {
            $dataString = $matches[1];
        } elseif (preg_match('/var data=({.*?})\s*;/s', $jsContent, $matches)) {
            $dataString = $matches[1];
        } else {
            // 如果正则匹配失败，尝试直接查找数据结构
            $startPos = strpos($jsContent, 'var data={');
            if ($startPos === false) {
                throw new Exception('无法在JS文件中找到数据变量');
            }
            
            $startPos += 9; // 跳过 'var data='
            $braceCount = 0;
            $endPos = $startPos;
            
            for ($i = $startPos; $i < strlen($jsContent); $i++) {
                if ($jsContent[$i] === '{') {
                    $braceCount++;
                } elseif ($jsContent[$i] === '}') {
                    $braceCount--;
                    if ($braceCount === 0) {
                        $endPos = $i + 1;
                        break;
                    }
                }
            }
            
            $dataString = substr($jsContent, $startPos, $endPos - $startPos);
        }
        
        // 将JS对象转换为PHP数组
        $jsonString = $this->convertJsObjectToJson($dataString);
        $data = json_decode($jsonString, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON解析错误: ' . json_last_error_msg());
        }
        
        // 导入数据
        $this->importDataWithProgress($data);
    }
    
    /**
     * 将JS对象格式转换为JSON格式
     */
    private function convertJsObjectToJson($jsObject)
    {
        // 替换JS对象的键名格式
        $jsonString = preg_replace('/"(\d+)"\s*:\s*{/', '"$1":{', $jsObject);
        $jsonString = preg_replace('/"([ncy])"\s*:\s*"([^"]+)"/', '"$1":"$2"', $jsonString);
        
        return $jsonString;
    }
    
    /**
     * 带进度显示的数据导入
     */
    private function importDataWithProgress($data)
    {
        DB::beginTransaction();
        
        try {
            $totalProvinces = count($data);
            $currentProvince = 0;
            
            $this->info("开始导入 {$totalProvinces} 个省份的数据...");
            
            foreach ($data as $provinceCode => $provinceData) {
                $currentProvince++;
                $this->info("[{$currentProvince}/{$totalProvinces}] 正在导入: {$provinceData['n']}");
                
                // 检查省份是否已存在（防止重复）
                $existingProvince = AdministrativeInstitution::where('province_code', $provinceCode)
                    ->where('administrative_level', 'province')
                    ->first();
                    
                if ($existingProvince) {
                    $this->warn("省份 {$provinceData['n']} 已存在，跳过");
                    continue;
                }
                
                // 导入省级数据
                $province = $this->createAdministrativeInstitution([
                    'name' => $provinceData['n'],
                    'institution_code' => $provinceCode,
                    'province_code' => $provinceCode,
                    'administrative_level' => 'province',
                    'parent_id' => null,
                    'is_active' => true,
                    'sort_order' => (int)$provinceCode
                ]);
                
                if (isset($provinceData['c'])) {
                    foreach ($provinceData['c'] as $cityCode => $cityData) {
                        // 检查城市是否已存在
                        $existingCity = AdministrativeInstitution::where('city_code', $cityCode)
                            ->where('province_code', $provinceCode)
                            ->where('administrative_level', 'city')
                            ->first();
                            
                        if ($existingCity) {
                            continue;
                        }
                        
                        // 导入市级数据
                        $city = $this->createAdministrativeInstitution([
                            'name' => $cityData['n'],
                            'institution_code' => $cityCode,
                            'province_code' => $provinceCode,
                            'city_code' => $cityCode,
                            'administrative_level' => 'city',
                            'parent_id' => $province->id,
                            'is_active' => true,
                            'sort_order' => (int)substr($cityCode, -2)
                        ]);
                        
                        if (isset($cityData['c'])) {
                            foreach ($cityData['c'] as $districtCode => $districtData) {
                                // 检查区县是否已存在
                                $existingDistrict = AdministrativeInstitution::where('district_code', $districtCode)
                                    ->where('city_code', $cityCode)
                                    ->where('province_code', $provinceCode)
                                    ->where('administrative_level', 'district')
                                    ->first();
                                    
                                if ($existingDistrict) {
                                    continue;
                                }
                                
                                // 导入区县级数据
                                $district = $this->createAdministrativeInstitution([
                                    'name' => $districtData['n'],
                                    'institution_code' => $districtCode,
                                    'province_code' => $provinceCode,
                                    'city_code' => $cityCode,
                                    'district_code' => $districtCode,
                                    'administrative_level' => 'district',
                                    'parent_id' => $city->id,
                                    'is_active' => true,
                                    'sort_order' => (int)substr($districtCode, -2)
                                ]);
                                
                                if (isset($districtData['c'])) {
                                    foreach ($districtData['c'] as $townCode => $townData) {
                                        // 检查乡镇是否已存在
                                        $existingTown = AdministrativeInstitution::where('town_code', $townCode)
                                            ->where('district_code', $districtCode)
                                            ->where('city_code', $cityCode)
                                            ->where('province_code', $provinceCode)
                                            ->where('administrative_level', 'town')
                                            ->first();
                                            
                                        if ($existingTown) {
                                            continue;
                                        }
                                        
                                        // 导入乡镇级数据
                                        $this->createAdministrativeInstitution([
                                            'name' => $townData['n'],
                                            'institution_code' => $townCode,
                                            'province_code' => $provinceCode,
                                            'city_code' => $cityCode,
                                            'district_code' => $districtCode,
                                            'town_code' => $townCode,
                                            'administrative_level' => 'town',
                                            'parent_id' => $district->id,
                                            'is_active' => true,
                                            'sort_order' => (int)substr($townCode, -3)
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            DB::commit();
            
            $this->info('✅ 数据导入完成');
            
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('数据导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建行政机构记录
     */
    private function createAdministrativeInstitution($data)
    {
        return AdministrativeInstitution::create(array_merge($data, [
            'created_at' => now(),
            'updated_at' => now()
        ]));
    }
    
    /**
     * 验证数据完整性
     */
    private function validateData()
    {
        $this->info('🔍 正在验证数据完整性...');
        
        // 统计各级别数据数量
        $stats = DB::table('administrative_institutions')
            ->select('administrative_level', DB::raw('COUNT(*) as count'))
            ->groupBy('administrative_level')
            ->get();
            
        $this->info('📊 数据统计:');
        foreach ($stats as $stat) {
            $levelName = match($stat->administrative_level) {
                'province' => '省份',
                'city' => '城市',
                'district' => '区县',
                'town' => '乡镇',
                default => $stat->administrative_level
            };
            $this->info("   {$levelName}: {$stat->count} 条");
        }
        
        // 检查重复数据
        $this->checkDuplicates();
        
        $this->info('✅ 数据验证完成');
    }
    
    /**
     * 检查重复数据
     */
    private function checkDuplicates()
    {
        $this->info('🔍 检查重复数据...');
        
        // 检查省级重复
        $provinceDuplicates = DB::table('administrative_institutions')
            ->select('province_code', DB::raw('COUNT(*) as count'))
            ->where('administrative_level', 'province')
            ->groupBy('province_code')
            ->having('count', '>', 1)
            ->count();
            
        // 检查市级重复
        $cityDuplicates = DB::table('administrative_institutions')
            ->select('province_code', 'city_code', DB::raw('COUNT(*) as count'))
            ->where('administrative_level', 'city')
            ->groupBy('province_code', 'city_code')
            ->having('count', '>', 1)
            ->count();
            
        // 检查区县级重复
        $districtDuplicates = DB::table('administrative_institutions')
            ->select('province_code', 'city_code', 'district_code', DB::raw('COUNT(*) as count'))
            ->where('administrative_level', 'district')
            ->groupBy('province_code', 'city_code', 'district_code')
            ->having('count', '>', 1)
            ->count();
            
        // 检查乡镇级重复
        $townDuplicates = DB::table('administrative_institutions')
            ->select('province_code', 'city_code', 'district_code', 'town_code', DB::raw('COUNT(*) as count'))
            ->where('administrative_level', 'town')
            ->groupBy('province_code', 'city_code', 'district_code', 'town_code')
            ->having('count', '>', 1)
            ->count();
            
        $totalDuplicates = $provinceDuplicates + $cityDuplicates + $districtDuplicates + $townDuplicates;
        
        if ($totalDuplicates > 0) {
            $this->warn("⚠️  发现 {$totalDuplicates} 组重复数据:");
            $this->warn("   省级重复: {$provinceDuplicates}");
            $this->warn("   市级重复: {$cityDuplicates}");
            $this->warn("   区县级重复: {$districtDuplicates}");
            $this->warn("   乡镇级重复: {$townDuplicates}");
        } else {
            $this->info('✅ 未发现重复数据');
        }
    }
}