<?php
/**
 * 比对两个数据库结构的差异
 */

echo "🔍 数据库结构比对工具\n";
echo "====================\n\n";

$oldDbFile = 'storage/app/private/private/backups/123_178188_xyz8.sql';
$newDbFile = 'storage/app/private/private/backups/123_178188_xyz9.sql';

if (!file_exists($oldDbFile)) {
    echo "❌ 旧数据库文件不存在: $oldDbFile\n";
    exit(1);
}

if (!file_exists($newDbFile)) {
    echo "❌ 新数据库文件不存在: $newDbFile\n";
    exit(1);
}

echo "📂 正在分析数据库文件...\n";
echo "旧库: $oldDbFile\n";
echo "新库: $newDbFile\n\n";

// 读取文件内容
$oldContent = file_get_contents($oldDbFile);
$newContent = file_get_contents($newDbFile);

// 提取表名
function extractTables($content) {
    preg_match_all('/CREATE TABLE `([^`]+)`/', $content, $matches);
    return $matches[1];
}

// 提取AUTO_INCREMENT值
function extractAutoIncrements($content) {
    preg_match_all('/CREATE TABLE `([^`]+)`.*?AUTO_INCREMENT = (\d+)/s', $content, $matches);
    $result = [];
    for ($i = 0; $i < count($matches[1]); $i++) {
        $result[$matches[1][$i]] = (int)$matches[2][$i];
    }
    return $result;
}

// 提取表结构
function extractTableStructure($content, $tableName) {
    $pattern = '/CREATE TABLE `' . preg_quote($tableName) . '`\s*\((.*?)\) ENGINE/s';
    if (preg_match($pattern, $content, $matches)) {
        return trim($matches[1]);
    }
    return null;
}

$oldTables = extractTables($oldContent);
$newTables = extractTables($newContent);

$oldAutoIncrements = extractAutoIncrements($oldContent);
$newAutoIncrements = extractAutoIncrements($newContent);

echo "📊 表数量比对:\n";
echo "旧库表数量: " . count($oldTables) . "\n";
echo "新库表数量: " . count($newTables) . "\n\n";

// 比对表名差异
$onlyInOld = array_diff($oldTables, $newTables);
$onlyInNew = array_diff($newTables, $oldTables);
$commonTables = array_intersect($oldTables, $newTables);

if (!empty($onlyInOld)) {
    echo "❌ 只在旧库中存在的表:\n";
    foreach ($onlyInOld as $table) {
        echo "  - $table\n";
    }
    echo "\n";
}

if (!empty($onlyInNew)) {
    echo "✅ 只在新库中存在的表:\n";
    foreach ($onlyInNew as $table) {
        echo "  - $table\n";
    }
    echo "\n";
}

echo "🔄 AUTO_INCREMENT值比对:\n";
echo "表名\t\t\t旧库\t新库\t差异\n";
echo "----------------------------------------\n";

foreach ($commonTables as $table) {
    $oldAI = isset($oldAutoIncrements[$table]) ? $oldAutoIncrements[$table] : 0;
    $newAI = isset($newAutoIncrements[$table]) ? $newAutoIncrements[$table] : 0;
    $diff = $newAI - $oldAI;
    
    if ($diff != 0) {
        $diffStr = $diff > 0 ? "+$diff" : "$diff";
        echo sprintf("%-20s\t%d\t%d\t%s\n", $table, $oldAI, $newAI, $diffStr);
    }
}

echo "\n📋 重要数据变化:\n";

// 检查关键表的数据变化
$keyTables = [
    'users' => '用户',
    'articles' => '文章', 
    'comments' => '评论',
    'activities' => '活动',
    'activity_details' => '活动场次',
    'registrations' => '报名记录',
    'categories' => '分类',
    'tags' => '标签',
    'friendship_links' => '友情链接',
    'roles' => '角色',
    'permissions' => '权限'
];

foreach ($keyTables as $table => $name) {
    if (isset($oldAutoIncrements[$table]) && isset($newAutoIncrements[$table])) {
        $oldCount = $oldAutoIncrements[$table] - 1; // AUTO_INCREMENT通常比实际记录数多1
        $newCount = $newAutoIncrements[$table] - 1;
        $change = $newCount - $oldCount;
        
        if ($change != 0) {
            $changeStr = $change > 0 ? "+$change" : "$change";
            echo "  $name ($table): $oldCount → $newCount ($changeStr)\n";
        }
    }
}

echo "\n🔍 检查cache表数据:\n";

// 检查cache表是否有数据
if (strpos($oldContent, "Records of cache") !== false) {
    echo "✅ 旧库有cache数据\n";
} else {
    echo "❌ 旧库无cache数据\n";
}

if (strpos($newContent, "Records of cache") !== false) {
    echo "✅ 新库有cache数据\n";
} else {
    echo "❌ 新库无cache数据\n";
}

// 提取cache数据
function extractCacheData($content) {
    $pattern = '/-- Records of cache.*?INSERT INTO `cache` VALUES (.*?);/s';
    if (preg_match($pattern, $content, $matches)) {
        return $matches[1];
    }
    return null;
}

$oldCacheData = extractCacheData($oldContent);
$newCacheData = extractCacheData($newContent);

if ($oldCacheData && !$newCacheData) {
    echo "\n🔑 发现关键问题: 旧库有cache数据，新库没有！\n";
    echo "这可能是导致登录403问题的根本原因。\n";
    echo "cache表通常包含权限缓存数据。\n\n";
    
    echo "💡 建议解决方案:\n";
    echo "1. 从旧库恢复cache数据\n";
    echo "2. 或者重新生成权限缓存\n";
}

echo "\n📈 总结:\n";
echo "=======\n";
echo "表结构: " . (count($onlyInOld) == 0 && count($onlyInNew) == 0 ? "✅ 一致" : "❌ 有差异") . "\n";
echo "数据量: " . (array_sum($newAutoIncrements) > array_sum($oldAutoIncrements) ? "✅ 新库数据更多" : "⚠️ 需要检查") . "\n";
echo "Cache: " . ($oldCacheData && !$newCacheData ? "❌ 缺失关键缓存" : "✅ 正常") . "\n";

if ($oldCacheData && !$newCacheData) {
    echo "\n🚨 关键发现: cache数据缺失可能是403问题的根本原因！\n";
}

echo "\n✨ 分析完成！\n";
?>
