<?php

namespace App\Filament\Pages;

use Filament\Pages\Auth\Login as BaseLogin;
use Illuminate\Support\Facades\Validator;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;

class Login extends BaseLogin
{
    public static string $view = 'vendor.filament-panels.pages.auth.login';

    public function authenticate(): ?\Filament\Http\Responses\Auth\Contracts\LoginResponse
    {
        $formData = $this->form->getState();
        \Log::info('Login authenticate called', [
            'captcha_from_form' => $formData['captcha'] ?? null,
            'form' => $formData,
        ]);
        $validator = \Validator::make(
            ['captcha' => $formData['captcha'] ?? null],
            ['captcha' => 'required|captcha'],
            ['captcha.required' => '请输入验证码', 'captcha.captcha' => '验证码错误']
        );
        if ($validator->fails()) {
            \Filament\Notifications\Notification::make()
                ->title('验证码校验失败: ' . $validator->errors()->first('captcha'))
                ->danger()
                ->send();
            \Log::error('Captcha validation failed', ['error' => $validator->errors()->first('captcha')]);
            return null;
        }
        \Filament\Notifications\Notification::make()
            ->title('验证码校验通过，准备登录')
            ->success()
            ->send();
        return parent::authenticate();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('email')
                    ->label('邮箱地址')
                    ->required()
                    ->email(),
                TextInput::make('password')
                    ->label('密码')
                    ->password()
                    ->required(),
                TextInput::make('captcha')
                    ->label('验证码')
                    ->required()
                    ->extraInputAttributes([
                        'placeholder' => '请输入验证码',
                    ]),
            ]);
    }
} 