<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('activity_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_id')->constrained()->cascadeOnDelete();
            $table->string('theme');
            $table->timestamp('deadline');
            $table->integer('quota');
            $table->string('target_audience');
            $table->integer('current_count')->default(0);
            $table->timestamp('activity_time');
            $table->string('fee');
            $table->text('reminder')->nullable();
            $table->string('registration_method');
            $table->string('address');
            $table->text('sms_template');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('activity_details');
    }
}; 