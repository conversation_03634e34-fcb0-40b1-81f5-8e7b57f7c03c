<?php

namespace App\Services;

use App\Models\WechatAccount;
use App\Models\WechatMessage;
use App\Models\WechatReply;
use EasyWeChat\Factory;
use Illuminate\Support\Facades\Log;
use Exception;

class WechatMessageService
{
    /**
     * 处理微信消息
     */
    public function handleMessage(WechatAccount $account, array $message): array
    {
        try {
            // 保存消息记录
            $wechatMessage = $this->saveMessage($account, $message);
            
            // 处理消息并获取回复
            $reply = $this->processMessage($account, $wechatMessage, $message);
            
            if ($reply) {
                // 标记消息为已回复
                $wechatMessage->markAsReplied(
                    $reply['type'],
                    $reply['content'] ?? json_encode($reply),
                    $reply['media_id'] ?? null
                );
                
                Log::info('微信消息处理成功', [
                    'account_id' => $account->id,
                    'message_id' => $wechatMessage->id,
                    'openid' => $message['FromUserName'],
                    'msg_type' => $message['MsgType'],
                    'reply_type' => $reply['type']
                ]);
                
                return $reply;
            } else {
                Log::info('微信消息无需回复', [
                    'account_id' => $account->id,
                    'message_id' => $wechatMessage->id,
                    'openid' => $message['FromUserName'],
                    'msg_type' => $message['MsgType']
                ]);
                
                return [];
            }
        } catch (Exception $e) {
            Log::error('微信消息处理异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'message' => $message,
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    /**
     * 保存消息记录
     */
    private function saveMessage(WechatAccount $account, array $message): WechatMessage
    {
        $data = [
            'wechat_account_id' => $account->id,
            'openid' => $message['FromUserName'],
            'msg_id' => $message['MsgId'] ?? null,
            'msg_type' => strtolower($message['MsgType']),
            'created_time' => isset($message['CreateTime']) ? 
                now()->setTimestamp($message['CreateTime']) : now(),
            'status' => WechatMessage::STATUS_PENDING,
        ];

        // 根据消息类型保存不同字段
        switch (strtolower($message['MsgType'])) {
            case 'text':
                $data['content'] = $message['Content'];
                break;
            case 'image':
                $data['pic_url'] = $message['PicUrl'];
                $data['media_id'] = $message['MediaId'];
                break;
            case 'voice':
                $data['media_id'] = $message['MediaId'];
                $data['format'] = $message['Format'];
                $data['recognition'] = $message['Recognition'] ?? null;
                break;
            case 'video':
            case 'shortvideo':
                $data['media_id'] = $message['MediaId'];
                $data['thumb_media_id'] = $message['ThumbMediaId'];
                break;
            case 'location':
                $data['location_x'] = $message['Location_X'];
                $data['location_y'] = $message['Location_Y'];
                $data['scale'] = $message['Scale'];
                $data['label'] = $message['Label'];
                break;
            case 'link':
                $data['title'] = $message['Title'];
                $data['description'] = $message['Description'];
                $data['url'] = $message['Url'];
                break;
            case 'event':
                $data['event'] = $message['Event'];
                $data['event_key'] = $message['EventKey'] ?? null;
                $data['ticket'] = $message['Ticket'] ?? null;
                $data['latitude'] = $message['Latitude'] ?? null;
                $data['longitude'] = $message['Longitude'] ?? null;
                $data['precision'] = $message['Precision'] ?? null;
                break;
        }

        return WechatMessage::create($data);
    }

    /**
     * 处理消息并生成回复
     */
    private function processMessage(WechatAccount $account, WechatMessage $wechatMessage, array $message): ?array
    {
        $msgType = strtolower($message['MsgType']);
        
        // 处理事件消息
        if ($msgType === 'event') {
            return $this->handleEvent($account, $message);
        }
        
        // 处理文本消息（关键词回复）
        if ($msgType === 'text') {
            return $this->handleTextMessage($account, $message['Content']);
        }
        
        // 其他类型消息使用默认回复
        return $this->getDefaultReply($account);
    }

    /**
     * 处理事件消息
     */
    private function handleEvent(WechatAccount $account, array $message): ?array
    {
        $event = $message['Event'];
        
        switch ($event) {
            case 'subscribe':
                return $this->getSubscribeReply($account);
            case 'unsubscribe':
                // 取消关注事件，记录日志但不回复
                Log::info('用户取消关注', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName']
                ]);
                return null;
            case 'CLICK':
                return $this->handleMenuClick($account, $message['EventKey']);
            case 'VIEW':
                // 菜单跳转事件，记录日志但不回复
                Log::info('用户点击菜单跳转', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'event_key' => $message['EventKey']
                ]);
                return null;
            case 'SCAN':
                return $this->handleScan($account, $message['EventKey']);
            case 'LOCATION':
                // 地理位置事件，记录日志但不回复
                Log::info('用户上报地理位置', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'latitude' => $message['Latitude'],
                    'longitude' => $message['Longitude']
                ]);
                return null;
            default:
                return $this->getDefaultReply($account);
        }
    }

    /**
     * 处理文本消息（关键词回复）
     */
    private function handleTextMessage(WechatAccount $account, string $content): ?array
    {
        // 查找匹配的关键词回复
        $replies = WechatReply::where('wechat_account_id', $account->id)
            ->where('type', WechatReply::TYPE_KEYWORD)
            ->active()
            ->byPriority()
            ->get();

        foreach ($replies as $reply) {
            if ($reply->matchesKeyword($content)) {
                $reply->incrementUsage();
                return $reply->toWechatReply();
            }
        }

        // 没有匹配的关键词，返回默认回复
        return $this->getDefaultReply($account);
    }

    /**
     * 获取关注回复
     */
    private function getSubscribeReply(WechatAccount $account): ?array
    {
        $reply = WechatReply::where('wechat_account_id', $account->id)
            ->where('type', WechatReply::TYPE_SUBSCRIBE)
            ->active()
            ->first();

        if ($reply) {
            $reply->incrementUsage();
            return $reply->toWechatReply();
        }

        return $this->getDefaultReply($account);
    }

    /**
     * 处理菜单点击事件
     */
    private function handleMenuClick(WechatAccount $account, string $eventKey): ?array
    {
        // 查找对应的事件回复
        $reply = WechatReply::where('wechat_account_id', $account->id)
            ->where('type', WechatReply::TYPE_EVENT)
            ->where('trigger_type', WechatReply::TRIGGER_CLICK)
            ->whereJsonContains('keywords', $eventKey)
            ->active()
            ->first();

        if ($reply) {
            $reply->incrementUsage();
            return $reply->toWechatReply();
        }

        return $this->getDefaultReply($account);
    }

    /**
     * 处理扫码事件
     */
    private function handleScan(WechatAccount $account, string $eventKey): ?array
    {
        // 查找对应的扫码回复
        $reply = WechatReply::where('wechat_account_id', $account->id)
            ->where('type', WechatReply::TYPE_EVENT)
            ->where('trigger_type', WechatReply::TRIGGER_SCAN)
            ->whereJsonContains('keywords', $eventKey)
            ->active()
            ->first();

        if ($reply) {
            $reply->incrementUsage();
            return $reply->toWechatReply();
        }

        return $this->getDefaultReply($account);
    }

    /**
     * 获取默认回复
     */
    private function getDefaultReply(WechatAccount $account): ?array
    {
        $reply = WechatReply::where('wechat_account_id', $account->id)
            ->where('type', WechatReply::TYPE_DEFAULT)
            ->active()
            ->first();

        if ($reply) {
            $reply->incrementUsage();
            return $reply->toWechatReply();
        }

        return null;
    }

    /**
     * 发送客服消息
     */
    public function sendCustomMessage(WechatAccount $account, string $openid, array $message): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $result = $app->customer_service->message($message)->to($openid)->send();
            
            if ($result['errcode'] === 0) {
                Log::info('客服消息发送成功', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'message' => $message
                ]);
                
                return [
                    'success' => true,
                    'message' => '消息发送成功'
                ];
            } else {
                Log::error('客服消息发送失败', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'error' => $result
                ]);
                
                return [
                    'success' => false,
                    'message' => '消息发送失败：' . $result['errmsg']
                ];
            }
        } catch (Exception $e) {
            Log::error('客服消息发送异常', [
                'account_id' => $account->id,
                'openid' => $openid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '消息发送异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 群发消息
     */
    public function broadcastMessage(WechatAccount $account, array $message, ?array $openids = null): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            
            if ($openids) {
                // 发送给指定用户
                $result = $app->broadcasting->sendToUsers($openids, $message);
            } else {
                // 发送给所有用户
                $result = $app->broadcasting->sendToAll($message);
            }
            
            if ($result['errcode'] === 0) {
                Log::info('群发消息发送成功', [
                    'account_id' => $account->id,
                    'message' => $message,
                    'openids' => $openids
                ]);
                
                return [
                    'success' => true,
                    'message' => '群发消息发送成功',
                    'msg_id' => $result['msg_id']
                ];
            } else {
                Log::error('群发消息发送失败', [
                    'account_id' => $account->id,
                    'error' => $result
                ]);
                
                return [
                    'success' => false,
                    'message' => '群发消息发送失败：' . $result['errmsg']
                ];
            }
        } catch (Exception $e) {
            Log::error('群发消息发送异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '群发消息发送异常：' . $e->getMessage()
            ];
        }
    }
}