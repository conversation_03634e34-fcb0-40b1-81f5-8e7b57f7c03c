<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MediaFile>
 */
class MediaFileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fileName = $this->faker->unique()->word() . '.jpg';
        return [
            'name' => $this->faker->words(2, true),
            'file_name' => $fileName,
            'mime_type' => 'image/jpeg',
            'path' => 'uploads/' . $fileName,
            'disk' => 'public',
            'size' => $this->faker->numberBetween(10000, 500000),
            'alt' => $this->faker->words(2, true),
            'description' => $this->faker->sentence(),
            'meta_data' => [],
            'category_id' => 1,
            'is_public' => true,
            'download_count' => $this->faker->numberBetween(0, 100),
        ];
    }
}
