<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('操作人ID');
            $table->dateTime('operated_at')->nullable()->comment('操作时间');
            $table->string('operation_type', 32)->nullable()->comment('操作类型');
            $table->string('module', 64)->nullable()->comment('模块/功能名');
            $table->string('target_id', 64)->nullable()->comment('操作对象ID');
            $table->string('request_ip', 45)->nullable()->comment('请求IP');
            $table->string('request_url', 255)->nullable()->comment('请求URL');
            $table->json('request_data')->nullable()->comment('请求参数');
            $table->string('status', 16)->nullable()->comment('结果状态');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
