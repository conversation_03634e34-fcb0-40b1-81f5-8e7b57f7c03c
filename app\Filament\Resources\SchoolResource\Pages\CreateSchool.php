<?php

namespace App\Filament\Resources\SchoolResource\Pages;

use App\Filament\Resources\SchoolResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Cache;
use Filament\Notifications\Notification;

class CreateSchool extends CreateRecord
{
    protected static string $resource = SchoolResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('学校创建成功')
            ->body('学校信息已成功添加到系统中。');
    }

    protected function afterCreate(): void
    {
        // 清除相关缓存
        $this->clearSchoolCaches();
    }

    /**
     * 清除学校相关缓存
     */
    private function clearSchoolCaches(): void
    {
        // 清除统计缓存
        Cache::forget('schools_counts');
        
        // 清除级联筛选器缓存
        $this->clearCascadingFilterCaches();
    }

    /**
     * 清除级联筛选器缓存
     */
    private function clearCascadingFilterCaches(): void
    {
        // 清除省份缓存
        Cache::forget('schools_provinces');
        
        // 清除所有城市缓存
        if (function_exists('redis') && Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            try {
                $cacheKeys = Cache::getRedis()->keys('*schools_cities_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
                
                // 清除所有区县缓存
                $cacheKeys = Cache::getRedis()->keys('*schools_districts_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
                
                // 清除所有乡镇缓存
                $cacheKeys = Cache::getRedis()->keys('*schools_towns_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
            } catch (\Exception $e) {
                // 如果Redis不可用，使用备用清理方法
                $this->clearCascadingCachesFallback();
            }
        } else {
            $this->clearCascadingCachesFallback();
        }
    }

    /**
     * 备用缓存清理方法
     */
    private function clearCascadingCachesFallback(): void
    {
        // 获取所有省份代码并清除对应缓存
        $provinceCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'province')
            ->pluck('province_code')
            ->unique();
            
        foreach ($provinceCodes as $code) {
            Cache::forget("schools_cities_{$code}");
        }
        
        // 获取所有城市代码并清除对应缓存
        $cityCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'city')
            ->pluck('city_code')
            ->unique();
            
        foreach ($cityCodes as $code) {
            Cache::forget("schools_districts_{$code}");
        }
        
        // 获取所有区县代码并清除对应缓存
        $districtCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'district')
            ->pluck('district_code')
            ->unique();
            
        foreach ($districtCodes as $code) {
            Cache::forget("schools_towns_{$code}");
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // 确保排序字段有默认值
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = 0;
        }
        
        // 确保启用状态有默认值
        if (!isset($data['is_active'])) {
            $data['is_active'] = true;
        }
        
        // 自动生成完整地区名称
        $data['full_region_name'] = $this->generateFullRegionName($data);
        
        return $data;
    }
    
    /**
     * 生成完整地区名称
     */
    private function generateFullRegionName(array $data): string
    {
        $parts = array_filter([
            $data['province_name'] ?? null,
            $data['city_name'] ?? null,
            $data['district_name'] ?? null,
            $data['town_name'] ?? null,
        ]);
        
        return implode(' - ', $parts);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back')
                ->label('返回列表')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url($this->getResource()::getUrl('index')),
        ];
    }
}