<?php

namespace Database\Factories;

use App\Models\Registration;
use App\Models\ActivityDetail;
use Illuminate\Database\Eloquent\Factories\Factory;

class RegistrationFactory extends Factory
{
    protected $model = Registration::class;

    public function definition(): array
    {
        return [
            'activity_detail_id' => ActivityDetail::factory(),
            'name' => $this->faker->name(),
            'phone' => $this->faker->phoneNumber(),
            'organization' => $this->faker->company(),
            'grade' => $this->faker->randomElement(['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']),
            'gender' => $this->faker->randomElement(['男', '女']),
            'source' => $this->faker->randomElement(['微信', '网站', '电话', '现场']),
            'status' => true,
        ];
    }
} 