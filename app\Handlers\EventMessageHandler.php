<?php

namespace App\Handlers;

use Illuminate\Support\Facades\Log;

class EventMessageHandler extends WechatEventHandler
{
    /**
     * 处理事件消息
     */
    public function handle(): ?string
    {
        $event = $this->message['Event'] ?? '';
        $eventKey = $this->message['EventKey'] ?? '';
        $ticket = $this->message['Ticket'] ?? '';
        
        // 保存消息到数据库
        $message = $this->saveMessage('event', [
            'event_type' => $event,
            'event_key' => $eventKey,
            'event_ticket' => $ticket,
        ]);

        Log::info('收到事件消息', [
            'account_id' => $this->account->id,
            'openid' => $this->message['FromUserName'],
            'event' => $event,
            'event_key' => $eventKey,
            'message_id' => $message->id,
        ]);

        $reply = null;

        switch ($event) {
            case 'subscribe':
                $reply = $this->handleSubscribe($message);
                break;
            case 'unsubscribe':
                $reply = $this->handleUnsubscribe($message);
                break;
            case 'CLICK':
                $reply = $this->handleMenuClick($message, $eventKey);
                break;
            case 'VIEW':
                $reply = $this->handleMenuView($message, $eventKey);
                break;
            case 'SCAN':
                $reply = $this->handleScan($message, $eventKey, $ticket);
                break;
            case 'LOCATION':
                $reply = $this->handleLocation($message);
                break;
            default:
                Log::warning('未处理的事件类型', [
                    'event' => $event,
                    'account_id' => $this->account->id,
                ]);
                break;
        }

        if ($reply) {
            return $reply;
        }

        // 没有匹配的回复，标记为已处理但未回复
        $message->update(['status' => 'processed']);

        return null;
    }

    /**
     * 处理关注事件
     */
    private function handleSubscribe($message): ?string
    {
        // 获取关注回复
        $reply = $this->getAutoReply('subscribe', [
            'openid' => $this->message['FromUserName'],
        ]);

        if ($reply) {
            // 解析回复内容
            $replyData = json_decode($reply, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($replyData)) {
                $replyContent = $this->buildReplyFromData($replyData);
                $replyType = $replyData['type'] ?? 'text';
            } else {
                // 纯文本回复
                $replyContent = $this->buildTextReply($reply);
                $replyType = 'text';
            }

            // 保存回复记录
            $this->saveReply($message, $reply, $replyType);

            return $replyContent;
        }

        return null;
    }

    /**
     * 处理取消关注事件
     */
    private function handleUnsubscribe($message): ?string
    {
        // 取消关注事件通常不需要回复，只记录日志
        Log::info('用户取消关注', [
            'account_id' => $this->account->id,
            'openid' => $this->message['FromUserName'],
        ]);

        $message->update(['status' => 'processed']);
        
        return null;
    }

    /**
     * 处理菜单点击事件
     */
    private function handleMenuClick($message, string $eventKey): ?string
    {
        // 获取事件回复
        $reply = $this->getAutoReply('event', [
            'event_key' => $eventKey,
            'openid' => $this->message['FromUserName'],
        ]);

        if ($reply) {
            // 解析回复内容
            $replyData = json_decode($reply, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($replyData)) {
                $replyContent = $this->buildReplyFromData($replyData);
                $replyType = $replyData['type'] ?? 'text';
            } else {
                // 纯文本回复
                $replyContent = $this->buildTextReply($reply);
                $replyType = 'text';
            }

            // 保存回复记录
            $this->saveReply($message, $reply, $replyType);

            return $replyContent;
        }

        return null;
    }

    /**
     * 处理菜单跳转事件
     */
    private function handleMenuView($message, string $eventKey): ?string
    {
        // VIEW事件通常不需要回复，只记录日志
        Log::info('用户点击菜单跳转', [
            'account_id' => $this->account->id,
            'openid' => $this->message['FromUserName'],
            'url' => $eventKey,
        ]);

        $message->update(['status' => 'processed']);
        
        return null;
    }

    /**
     * 处理扫码事件
     */
    private function handleScan($message, string $eventKey, string $ticket): ?string
    {
        // 获取扫码回复
        $reply = $this->getAutoReply('event', [
            'event_key' => $eventKey,
            'ticket' => $ticket,
            'openid' => $this->message['FromUserName'],
        ]);

        if ($reply) {
            // 解析回复内容
            $replyData = json_decode($reply, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($replyData)) {
                $replyContent = $this->buildReplyFromData($replyData);
                $replyType = $replyData['type'] ?? 'text';
            } else {
                // 纯文本回复
                $replyContent = $this->buildTextReply($reply);
                $replyType = 'text';
            }

            // 保存回复记录
            $this->saveReply($message, $reply, $replyType);

            return $replyContent;
        }

        return null;
    }

    /**
     * 处理位置事件
     */
    private function handleLocation($message): ?string
    {
        // 位置事件通常不需要回复，只记录日志
        Log::info('收到位置信息', [
            'account_id' => $this->account->id,
            'openid' => $this->message['FromUserName'],
            'latitude' => $this->message['Latitude'] ?? '',
            'longitude' => $this->message['Longitude'] ?? '',
        ]);

        $message->update(['status' => 'processed']);
        
        return null;
    }

    /**
     * 根据回复数据构建回复内容
     */
    private function buildReplyFromData(array $replyData): string
    {
        $type = $replyData['type'] ?? 'text';

        switch ($type) {
            case 'text':
                return $this->buildTextReply($replyData['content'] ?? '');

            case 'image':
                return $this->buildImageReply($replyData['media_id'] ?? '');

            case 'voice':
                return $this->buildVoiceReply($replyData['media_id'] ?? '');

            case 'video':
                return $this->buildVideoReply(
                    $replyData['media_id'] ?? '',
                    $replyData['title'] ?? '',
                    $replyData['description'] ?? ''
                );

            case 'music':
                return $this->buildMusicReply(
                    $replyData['title'] ?? '',
                    $replyData['description'] ?? '',
                    $replyData['music_url'] ?? '',
                    $replyData['hq_music_url'] ?? '',
                    $replyData['thumb_media_id'] ?? ''
                );

            case 'news':
                return $this->buildNewsReply($replyData['articles'] ?? []);

            default:
                return $this->buildTextReply($replyData['content'] ?? '暂不支持该类型的回复');
        }
    }
}