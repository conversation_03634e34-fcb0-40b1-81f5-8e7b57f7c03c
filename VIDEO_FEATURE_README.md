# TipTap编辑器视频功能使用指南

## 功能概述

本系统已成功集成了本地视频上传和播放功能到TipTap编辑器中，支持用户直接上传视频文件并在文章中以HTML5视频播放器的形式展示。

## 新增功能

### 1. 视频上传工具
- 在TipTap编辑器工具栏中新增了视频图标按钮
- 支持本地视频文件上传（MP4, WebM, OGG, AVI, MOV格式）
- 支持外部视频URL输入
- 支持视频封面图设置
- 支持视频尺寸自定义

### 2. 视频播放功能
- 自动转换为HTML5视频播放器
- 支持视频控制条显示/隐藏
- 响应式设计，适配移动端
- 优雅的视频样式和交互效果

## 技术实现

### 文件结构
```
├── app/
│   ├── Filament/TiptapEditor/Actions/
│   │   └── VideoMediaAction.php          # 自定义视频上传Action
│   ├── Helpers/
│   │   └── TiptapHelper.php               # TipTap内容渲染辅助类
│   ├── Providers/
│   │   └── TiptapVideoServiceProvider.php # 视频功能服务提供者
│   └── View/Components/
│       └── VideoPlayer.php               # 视频播放器组件
├── resources/
│   ├── js/
│   │   ├── tiptap-video-extension.js     # TipTap视频扩展
│   │   └── VideoComponent.vue            # Vue视频组件
│   └── views/
│       ├── article/
│       │   └── show.blade.php            # 文章展示模板
│       └── components/
│           └── video-player.blade.php   # 视频播放器Blade组件
└── config/
    └── filament-tiptap-editor.php        # TipTap配置文件
```

### 核心组件说明

#### 1. VideoMediaAction.php
- 处理视频文件上传
- 支持本地文件和外部URL
- 提供视频属性配置界面
- 生成标准化的视频HTML标签

#### 2. TiptapHelper.php
- 提供内容渲染辅助方法
- 自动优化视频标签
- 确保视频正确显示

#### 3. tiptap-video-extension.js
- TipTap编辑器的视频扩展
- 定义视频节点的行为和属性
- 集成Vue组件进行视频渲染

## 使用方法

### 在编辑器中插入视频

1. 在TipTap编辑器工具栏中点击视频图标
2. 在弹出的对话框中选择以下方式之一：
   - **上传本地视频**：点击"上传视频文件"选择本地视频
   - **输入视频URL**：在"视频URL"字段输入外部视频链接
3. 可选配置：
   - 设置视频宽度和高度
   - 上传视频封面图
   - 选择是否显示控制条
4. 点击"插入"按钮完成视频插入

### 在前端显示视频内容

在Blade模板中使用TiptapHelper来渲染内容：

```php
{!! \App\Helpers\TiptapHelper::render($article->content) !!}
```

或者使用视频播放器组件：

```php
<x-video-player 
    src="{{ $videoUrl }}" 
    :controls="true" 
    width="100%" 
    height="auto" 
    poster="{{ $posterUrl }}" 
/>
```

## 配置说明

### 支持的视频格式
- MP4 (推荐)
- WebM
- OGG
- AVI
- MOV
- QuickTime

### 文件大小限制
- 默认最大文件大小：50MB
- 可在 `config/filament-tiptap-editor.php` 中修改 `max_file_size` 配置

### 存储配置
- 默认存储磁盘：`public`
- 默认存储目录：`uploads/media`
- 可在配置文件中自定义存储位置

## 样式定制

视频播放器的样式可以通过CSS进行定制：

```css
.video-wrapper {
    margin: 1.5rem 0;
    text-align: center;
}

.video-player {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.video-player:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}
```

## 注意事项

1. **性能考虑**：大视频文件会影响页面加载速度，建议：
   - 压缩视频文件
   - 使用适当的视频格式
   - 设置合理的文件大小限制

2. **浏览器兼容性**：
   - HTML5视频在现代浏览器中有良好支持
   - 建议提供多种格式以确保兼容性

3. **移动端优化**：
   - 视频会自动适配移动设备
   - 在移动网络下建议使用较小的视频文件

4. **SEO优化**：
   - 为视频添加适当的标题和描述
   - 使用视频封面图提升用户体验

## 故障排除

### 视频无法播放
1. 检查视频文件格式是否支持
2. 确认文件路径是否正确
3. 检查浏览器控制台是否有错误信息

### 视频显示为链接
1. 确认已正确使用 `TiptapHelper::render()` 方法
2. 检查服务提供者是否已注册
3. 清除缓存并重新编译前端资源

### 上传失败
1. 检查文件大小是否超出限制
2. 确认存储目录权限设置
3. 检查磁盘空间是否充足

## 更新日志

- **v1.0.0** (2025-01-22)
  - 初始版本发布
  - 支持本地视频上传
  - 支持HTML5视频播放
  - 集成TipTap编辑器
  - 响应式设计支持