<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->integer('points')->default(0)->comment('积分数量');
            $table->string('type')->comment('积分类型：login, browse, video');
            $table->string('source')->nullable()->comment('积分来源描述');
            $table->string('source_id')->nullable()->comment('来源ID（如文章ID、视频ID）');
            $table->date('earned_date')->comment('获得积分的日期');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'earned_date']);
            $table->index(['type', 'earned_date']);
            $table->index('earned_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_points');
    }
};
