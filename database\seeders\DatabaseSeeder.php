<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->call([
            AdminUserSeeder::class,
            TestDataSeeder::class,
            InitAllSeeder::class,
            PermissionSeeder::class,
            BasicContentSeeder::class,
            AchievementSeeder::class,
            ActivitySeeder::class,
            AuditLogSeeder::class,
            FriendshipLinkSeeder::class,
        ]);

        // 创建默认角色
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $editorRole = Role::firstOrCreate(['name' => 'editor']);

        // 创建默认权限
        $permissions = [
            'view_dashboard',
            'manage_users',
            'manage_roles',
            'manage_permissions',
            'manage_posts',
            'manage_categories',
            'manage_tags',
            'manage_comments',
            'manage_media',
            'manage_settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // 给管理员角色分配所有权限
        $adminRole->givePermissionTo($permissions);

        // 给编辑角色分配部分权限
        $editorRole->givePermissionTo([
            'view_dashboard',
            'manage_posts',
            'manage_categories',
            'manage_tags',
            'manage_comments',
            'manage_media',
        ]);

        // 创建默认管理员用户
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => bcrypt('password'),
                'is_active' => true,
            ]
        );

        // 确保管理员用户有管理员角色
        if (!$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }
    }
}
