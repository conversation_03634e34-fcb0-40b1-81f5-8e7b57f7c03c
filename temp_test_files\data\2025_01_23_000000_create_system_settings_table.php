<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->comment('设置键名');
            $table->text('value')->nullable()->comment('设置值');
            $table->string('description')->nullable()->comment('设置描述');
            $table->enum('type', ['string', 'integer', 'float', 'boolean', 'array', 'json'])
                  ->default('string')
                  ->comment('数据类型');
            $table->string('group')->default('general')->comment('设置分组');
            $table->timestamps();
            
            $table->index(['group']);
            $table->comment('系统设置表');
        });
        
        // 插入默认设置
        DB::table('system_settings')->insert([
            // 站点设置
            [
                'key' => 'site_name',
                'value' => '内容管理系统',
                'description' => '网站名称',
                'type' => 'string',
                'group' => 'site',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_description',
                'value' => '基于Laravel和Filament的内容管理系统',
                'description' => '网站描述',
                'type' => 'string',
                'group' => 'site',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_url',
                'value' => 'http://localhost',
                'description' => '网站地址',
                'type' => 'string',
                'group' => 'site',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_keywords',
                'value' => 'CMS,内容管理,Laravel,Filament',
                'description' => '网站关键词（用逗号分隔）',
                'type' => 'string',
                'group' => 'site',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_icp',
                'value' => '',
                'description' => '网站备案号',
                'type' => 'string',
                'group' => 'site',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 上传设置
            [
                'key' => 'upload_image_types',
                'value' => '["gif","jpg","jpeg","png","tiff","bmp"]',
                'description' => '允许上传的图片文件类型',
                'type' => 'json',
                'group' => 'upload',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'upload_media_types',
                'value' => '["mp3","wmv","wma","rmvb","rm","avi","flv","mp4","mov"]',
                'description' => '允许上传的多媒体文件类型',
                'type' => 'json',
                'group' => 'upload',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'upload_document_types',
                'value' => '["txt","doc","docx","xls","xlsx","ppt","pptx","zip","rar","pdf"]',
                'description' => '允许上传的文档文件类型',
                'type' => 'json',
                'group' => 'upload',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'upload_max_size',
                'value' => '10240',
                'description' => '文件上传最大大小（KB）',
                'type' => 'integer',
                'group' => 'upload',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 安全设置
            [
                'key' => 'user_registration_enabled',
                'value' => '1',
                'description' => '是否开放用户注册',
                'type' => 'boolean',
                'group' => 'security',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 统计设置
            [
                'key' => 'statistics_code',
                'value' => '',
                'description' => '统计代码（如百度统计、Google Analytics等）',
                'type' => 'string',
                'group' => 'statistics',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'webmaster_tools',
                'value' => '',
                'description' => '站长工具验证代码',
                'type' => 'string',
                'group' => 'statistics',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 审计设置
            [
                'key' => 'audit_log_retention_days',
                'value' => '90',
                'description' => '审计日志保留天数',
                'type' => 'integer',
                'group' => 'audit',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'audit_log_auto_cleanup',
                'value' => '1',
                'description' => '是否自动清理过期审计日志',
                'type' => 'boolean',
                'group' => 'audit',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_settings');
    }
};