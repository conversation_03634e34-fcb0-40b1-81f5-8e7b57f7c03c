<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use Illuminate\Support\Facades\DB;

echo "=== 调试模板数据生成 ===\n";

try {
    // 获取测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    $activityDetail = $registration->activityDetail;
    $activity = $activityDetail->activity;
    
    echo "原始数据:\n";
    echo "activity_time: '{$activityDetail->activity_time}'\n";
    echo "target_audience: '{$activityDetail->target_audience}'\n";
    
    // 模拟buildTemplateData的时间处理逻辑
    echo "\n=== 时间处理测试 ===\n";
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        echo "activity_time不为空，开始格式化\n";
        try {
            // 检查原始时间格式
            echo "原始时间: '{$activityDetail->activity_time}'\n";
            echo "strtotime结果: " . strtotime($activityDetail->activity_time) . "\n";
            
            $timestamp = strtotime($activityDetail->activity_time);
            if ($timestamp !== false) {
                $time = date("Y年m月d日 H:i", $timestamp);
                echo "格式化后时间: '{$time}'\n";
            } else {
                echo "strtotime失败，使用默认值\n";
                $time = "待定时间";
            }
        } catch (Exception $e) {
            echo "时间格式化异常: " . $e->getMessage() . "\n";
            $time = "待定时间";
        }
    } else {
        echo "activity_time为空，使用默认值\n";
        $time = "待定时间";
    }
    
    echo "最终时间值: '{$time}'\n";
    
    // 模拟对象处理逻辑
    echo "\n=== 对象处理测试 ===\n";
    $obj = "";
    if (!empty($activityDetail->target_audience)) {
        $obj = $activityDetail->target_audience;
        echo "使用target_audience: '{$obj}'\n";
    } elseif (!empty($activityDetail->target)) {
        $obj = $activityDetail->target;
        echo "使用target: '{$obj}'\n";
    } else {
        $obj = "全体人员";
        echo "使用默认值: '{$obj}'\n";
    }
    
    echo "最终对象值: '{$obj}'\n";
    
    // 检查是否有空值判断问题
    echo "\n=== 空值检查 ===\n";
    echo "empty(activity_time): " . (empty($activityDetail->activity_time) ? 'true' : 'false') . "\n";
    echo "empty(target_audience): " . (empty($activityDetail->target_audience) ? 'true' : 'false') . "\n";
    echo "is_null(activity_time): " . (is_null($activityDetail->activity_time) ? 'true' : 'false') . "\n";
    echo "is_null(target_audience): " . (is_null($activityDetail->target_audience) ? 'true' : 'false') . "\n";
    echo "activity_time === '': " . ($activityDetail->activity_time === '' ? 'true' : 'false') . "\n";
    echo "target_audience === '': " . ($activityDetail->target_audience === '' ? 'true' : 'false') . "\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 调试完成 ===\n";
