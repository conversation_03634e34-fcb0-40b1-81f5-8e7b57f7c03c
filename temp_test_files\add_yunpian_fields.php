<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== 添加云片网对应字段 ===\n\n";

try {
    // 添加topic字段
    if (!Schema::hasColumn('activity_details', 'topic')) {
        DB::statement('ALTER TABLE activity_details ADD COLUMN topic VARCHAR(255) NULL COMMENT "活动主题-对应云片网#topic#参数"');
        echo "✓ 添加 activity_details.topic 字段\n";
    } else {
        echo "- activity_details.topic 字段已存在\n";
    }
    
    // 添加target_audience字段
    if (!Schema::hasColumn('activity_details', 'target_audience')) {
        DB::statement('ALTER TABLE activity_details ADD COLUMN target_audience VARCHAR(255) NULL COMMENT "目标受众-对应云片网#obj#参数"');
        echo "✓ 添加 activity_details.target_audience 字段\n";
    } else {
        echo "- activity_details.target_audience 字段已存在\n";
    }
    
    // 迁移数据
    echo "\n迁移现有数据:\n";
    
    $themeCount = DB::update('UPDATE activity_details SET topic = theme WHERE theme IS NOT NULL AND theme != ""');
    echo "✓ 迁移了 {$themeCount} 条theme数据到topic字段\n";
    
    $targetCount = DB::update('UPDATE activity_details SET target_audience = target WHERE target IS NOT NULL AND target != ""');
    echo "✓ 迁移了 {$targetCount} 条target数据到target_audience字段\n";
    
    echo "\n✓ 字段添加和数据迁移完成\n";
    
} catch (Exception $e) {
    echo "\n✗ 操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 完成 ===\n";
