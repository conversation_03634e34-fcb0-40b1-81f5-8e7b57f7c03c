<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FriendshipLink;
use App\Models\FriendshipLinkCategory;
use Illuminate\Http\Request;

class FriendshipLinkController extends Controller
{
    /**
     * 获取所有启用的友情链接分类及其链接
     */
    public function index(Request $request)
    {
        $query = FriendshipLinkCategory::with(['friendshipLinks' => function ($query) {
            $query->active()->ordered();
        }])
        ->active()
        ->orderBy('sort_order', 'asc')
        ->orderBy('created_at', 'desc');

        // 如果指定了分类ID
        if ($request->has('category_id')) {
            $query->where('id', $request->category_id);
        }

        $categories = $query->get();

        return response()->json([
            'success' => true,
            'data' => $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'links' => $category->friendshipLinks->map(function ($link) {
                        return [
                            'id' => $link->id,
                            'name' => $link->name,
                            'url' => $link->url,
                            'image_url' => $link->image_url,
                            'description' => $link->description,
                            'title' => $link->title ?: $link->name,
                            'rel' => $link->rel,
                            'open_new_window' => $link->open_new_window,
                            'domain' => $link->domain,
                        ];
                    }),
                ];
            }),
        ]);
    }

    /**
     * 获取指定分类的友情链接
     */
    public function byCategory($categoryId)
    {
        $category = FriendshipLinkCategory::with(['friendshipLinks' => function ($query) {
            $query->active()->ordered();
        }])
        ->active()
        ->findOrFail($categoryId);

        return response()->json([
            'success' => true,
            'data' => [
                'category' => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                ],
                'links' => $category->friendshipLinks->map(function ($link) {
                    return [
                        'id' => $link->id,
                        'name' => $link->name,
                        'url' => $link->url,
                        'image_url' => $link->image_url,
                        'description' => $link->description,
                        'title' => $link->title ?: $link->name,
                        'rel' => $link->rel,
                        'open_new_window' => $link->open_new_window,
                        'domain' => $link->domain,
                    ];
                }),
            ],
        ]);
    }

    /**
     * 获取所有启用的友情链接（不分类）
     */
    public function links(Request $request)
    {
        $query = FriendshipLink::with('category')
            ->active()
            ->ordered();

        // 如果指定了分类
        if ($request->has('category_id')) {
            $query->byCategory($request->category_id);
        }

        // 分页
        $perPage = $request->get('per_page', 20);
        $links = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $links->items(),
            'pagination' => [
                'current_page' => $links->currentPage(),
                'last_page' => $links->lastPage(),
                'per_page' => $links->perPage(),
                'total' => $links->total(),
            ],
        ]);
    }

    /**
     * 获取友情链接分类树
     */
    public function categories()
    {
        $categories = FriendshipLinkCategory::active()
            ->withCount('friendshipLinks')
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'links_count' => $category->friendship_links_count,
                    'parent_id' => $category->parent_id,
                ];
            }),
        ]);
    }
}
