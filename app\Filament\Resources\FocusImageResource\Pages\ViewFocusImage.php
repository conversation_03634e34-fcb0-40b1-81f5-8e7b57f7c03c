<?php

namespace App\Filament\Resources\FocusImageResource\Pages;

use App\Filament\Resources\FocusImageResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFocusImage extends ViewRecord
{
    protected static string $resource = FocusImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}