<?php

namespace App\Filament\Resources\WechatArticleResource\Pages;

use App\Filament\Resources\WechatArticleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditWechatArticle extends EditRecord
{
    protected static string $resource = WechatArticleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('查看'),
            Actions\DeleteAction::make()
                ->label('删除'),
            Actions\ForceDeleteAction::make()
                ->label('强制删除'),
            Actions\RestoreAction::make()
                ->label('恢复'),
        ];
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}