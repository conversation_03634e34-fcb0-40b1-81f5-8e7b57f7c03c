<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            // 添加短信模板ID字段
            $table->foreignId('sms_template_id')->nullable()->constrained('sms_templates')->nullOnDelete()->after('sms_template');
        });
    }

    public function down(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            $table->dropForeign(['sms_template_id']);
            $table->dropColumn('sms_template_id');
        });
    }
};