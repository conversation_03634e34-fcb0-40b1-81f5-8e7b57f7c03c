<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AchievementCenterResource\Pages;
use App\Models\User;
use App\Services\PointService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AchievementCenterResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationGroup = '成就管理';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('用户信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('用户名')
                            ->disabled(),
                        Forms\Components\TextInput::make('real_name')
                            ->label('真实姓名')
                            ->disabled(),
                        Forms\Components\TextInput::make('region')
                            ->label('地区归属')
                            ->disabled(),
                        Forms\Components\TextInput::make('school')
                            ->label('学校')
                            ->disabled(),
                        Forms\Components\Toggle::make('is_verified')
                            ->label('实名认证')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('积分统计')
                    ->schema([
                        Forms\Components\Placeholder::make('total_points')
                            ->label('总积分')
                            ->content(fn ($record) => $record ? $record->total_points . ' 分' : '0 分'),
                        Forms\Components\Placeholder::make('month_points')
                            ->label('月积分')
                            ->content(fn ($record) => $record ? $record->month_points . ' 分' : '0 分'),
                        Forms\Components\Placeholder::make('quarter_points')
                            ->label('季度积分')
                            ->content(fn ($record) => $record ? $record->quarter_points . ' 分' : '0 分'),
                        Forms\Components\Placeholder::make('year_points')
                            ->label('年积分')
                            ->content(fn ($record) => $record ? $record->year_points . ' 分' : '0 分'),
                    ])->columns(4),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                User::query()
                    ->where('is_verified', true)
                    ->withSum('points as total_points', 'points')
                    ->withSum(['points as month_points' => function ($query) {
                        $query->whereYear('earned_date', now()->year)
                              ->whereMonth('earned_date', now()->month);
                    }], 'points')
                    ->withSum(['points as quarter_points' => function ($query) {
                        $quarter = ceil(now()->month / 3);
                        $startMonth = ($quarter - 1) * 3 + 1;
                        $endMonth = $quarter * 3;
                        $query->whereYear('earned_date', now()->year)
                              ->whereMonth('earned_date', '>=', $startMonth)
                              ->whereMonth('earned_date', '<=', $endMonth);
                    }], 'points')
                    ->withSum(['points as year_points' => function ($query) {
                        $query->whereYear('earned_date', now()->year);
                    }], 'points')
            )
            ->columns([
                Tables\Columns\TextColumn::make('rank')
                    ->label('排名')
                    ->getStateUsing(function ($record, $rowLoop) {
                        return $rowLoop->iteration;
                    })
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state == 1 => 'warning',
                        $state == 2 => 'gray',
                        $state == 3 => 'orange',
                        default => 'primary'
                    }),
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('头像')
                    ->circular()
                    ->defaultImageUrl('/images/default-avatar.png'),
                Tables\Columns\TextColumn::make('real_name')
                    ->label('教师')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('school')
                    ->label('学校')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('region')
                    ->label('地区')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_points')
                    ->label('总积分')
                    ->suffix(' 分')
                    ->sortable()
                    ->color('success'),
                Tables\Columns\TextColumn::make('month_points')
                    ->label('月积分')
                    ->suffix(' 分')
                    ->sortable(),
                Tables\Columns\TextColumn::make('quarter_points')
                    ->label('季度积分')
                    ->suffix(' 分')
                    ->sortable(),
                Tables\Columns\TextColumn::make('year_points')
                    ->label('年积分')
                    ->suffix(' 分')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('注册时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('region')
                    ->label('地区')
                    ->options(function () {
                        return User::where('is_verified', true)
                            ->whereNotNull('region')
                            ->distinct()
                            ->pluck('region', 'region')
                            ->toArray();
                    }),
                Tables\Filters\SelectFilter::make('school')
                    ->label('学校')
                    ->options(function () {
                        return User::where('is_verified', true)
                            ->whereNotNull('school')
                            ->distinct()
                            ->pluck('school', 'school')
                            ->toArray();
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->defaultSort('total_points', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAchievementCenter::route('/'),
            'view' => Pages\ViewAchievementCenter::route('/{record}'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '成就中心';
    }

    public static function getModelLabel(): string
    {
        return '用户成就';
    }

    public static function getPluralModelLabel(): string
    {
        return '用户成就';
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }
}
