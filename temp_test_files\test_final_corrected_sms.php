<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 测试修复后的SMS模板（最终修正版） ===\n";

try {
    // 直接查询数据库获取测试数据
    $registration = DB::table('registrations')
        ->select('id', 'name', 'activity_detail_id')
        ->first();
    
    if (!$registration) {
        echo "未找到报名记录\n";
        exit(1);
    }
    
    echo "测试报名记录 ID: {$registration->id}\n";
    echo "报名人姓名: {$registration->name}\n";
    
    // 获取活动详情
    $activityDetail = DB::table('activity_details')
        ->where('id', $registration->activity_detail_id)
        ->first();
    
    // 获取活动信息
    $activity = DB::table('activities')
        ->where('id', $activityDetail->activity_id)
        ->first();
    
    echo "活动名称: {$activity->title}\n";
    
    // 显示原始数据
    echo "\n原始数据:\n";
    echo "activity_detail.topic: '{$activityDetail->topic}'\n";
    echo "activity_detail.theme: '{$activityDetail->theme}'\n";
    echo "activity_detail.target_audience: '{$activityDetail->target_audience}'\n";
    echo "activity_detail.target: '{$activityDetail->target}'\n";
    echo "activity_detail.address: '{$activityDetail->address}'\n";
    
    // 检查时间字段
    $timeFields = ['activity_time', 'start_time', 'event_time', 'time', 'date'];
    $timeValue = null;
    foreach ($timeFields as $field) {
        if (property_exists($activityDetail, $field) && $activityDetail->$field) {
            $timeValue = $activityDetail->$field;
            echo "找到时间字段 {$field}: '{$timeValue}'\n";
            break;
        }
    }
    
    if (!$timeValue) {
        echo "未找到时间字段，使用默认值\n";
        $timeValue = '待定';
    }
    
    // 获取现场咨询通知模板
    $template = DB::table('sms_templates')
        ->where('name', '现场咨询通知')
        ->first();
    
    if (!$template) {
        echo "未找到现场咨询通知模板\n";
        exit(1);
    }
    
    echo "\n模板信息:\n";
    echo "模板名称: {$template->name}\n";
    echo "模板内容: {$template->description}\n";
    
    // 构建模板数据
    $templateData = [
        'name' => $registration->name,
        'topic' => $activityDetail->topic ?: ($activityDetail->theme ?: $activity->title),
        'time' => ($timeValue && $timeValue !== '待定') ? date('Y年m月d日 H:i', strtotime($timeValue)) : '待定',
        'address' => $activityDetail->address ?: '待定',
        'obj' => $activityDetail->target_audience ?: ($activityDetail->target ?: '全体人员')
    ];
    
    echo "\n模板数据:\n";
    foreach ($templateData as $key => $value) {
        echo "#{$key}#: '{$value}'\n";
    }
    
    // 手动替换占位符
    $finalContent = $template->description;
    foreach ($templateData as $key => $value) {
        $finalContent = str_replace("#{$key}#", $value, $finalContent);
    }
    
    echo "\n=== 最终短信内容 ===\n";
    echo $finalContent . "\n";
    
    // 检查是否还有未替换的占位符
    if (preg_match('/#\w+#/', $finalContent)) {
        echo "\n⚠️ 警告：仍有未替换的占位符\n";
        preg_match_all('/#\w+#/', $finalContent, $matches);
        echo "未替换的占位符: " . implode(', ', $matches[0]) . "\n";
    } else {
        echo "\n✓ 所有占位符已正确替换，时间和对象字段已修复！\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
