<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SmsTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class SmsTemplateController extends Controller
{
    /**
     * 显示短信模板列表
     */
    public function index()
    {
        $templates = SmsTemplate::orderBy('template_category')
                               ->orderBy('name')
                               ->get();
        
        return view('admin.sms-templates.index', compact('templates'));
    }

    /**
     * 显示创建短信模板表单
     */
    public function create()
    {
        $categories = SmsTemplate::getCategoryOptions();
        return view('admin.sms-templates.create', compact('categories'));
    }

    /**
     * 存储新的短信模板
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:100|unique:sms_templates,code',
            'yunpian_template_id' => 'required|string|max:50',
            'description' => 'required|string',
            'template_params' => 'required|array',
            'param_description' => 'nullable|string',
            'template_category' => 'required|string|max:50'
        ]);

        try {
            SmsTemplate::create($validated);
            return redirect()->route('admin.sms-templates.index')
                           ->with('success', '短信模板创建成功！');
        } catch (Exception $e) {
            Log::error('短信模板创建失败', ['error' => $e->getMessage()]);
            return back()->withInput()
                        ->with('error', '短信模板创建失败：' . $e->getMessage());
        }
    }

    /**
     * 显示短信模板详情
     */
    public function show(SmsTemplate $smsTemplate)
    {
        return view('admin.sms-templates.show', compact('smsTemplate'));
    }

    /**
     * 显示编辑短信模板表单
     */
    public function edit(SmsTemplate $smsTemplate)
    {
        $categories = SmsTemplate::getCategoryOptions();
        return view('admin.sms-templates.edit', compact('smsTemplate', 'categories'));
    }

    /**
     * 更新短信模板
     */
    public function update(Request $request, SmsTemplate $smsTemplate)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:100|unique:sms_templates,code,' . $smsTemplate->id,
            'yunpian_template_id' => 'required|string|max:50',
            'description' => 'required|string',
            'template_params' => 'required|array',
            'param_description' => 'nullable|string',
            'template_category' => 'required|string|max:50'
        ]);

        try {
            $smsTemplate->update($validated);
            return redirect()->route('admin.sms-templates.index')
                           ->with('success', '短信模板更新成功！');
        } catch (Exception $e) {
            Log::error('短信模板更新失败', ['error' => $e->getMessage()]);
            return back()->withInput()
                        ->with('error', '短信模板更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除短信模板
     */
    public function destroy(SmsTemplate $smsTemplate)
    {
        try {
            $smsTemplate->delete();
            return redirect()->route('admin.sms-templates.index')
                           ->with('success', '短信模板删除成功！');
        } catch (Exception $e) {
            Log::error('短信模板删除失败', ['error' => $e->getMessage()]);
            return back()->with('error', '短信模板删除失败：' . $e->getMessage());
        }
    }

    /**
     * 测试发送短信
     */
    public function testSend(Request $request, SmsTemplate $smsTemplate)
    {
        $validated = $request->validate([
            'mobile' => 'required|regex:/^1[3-9]\d{9}$/',
            'test_params' => 'required|array'
        ]);

        try {
            $smsService = app('App\Services\SmsService');
            $result = $smsService->sendSms(
                $validated['mobile'],
                $smsTemplate->code,
                $validated['test_params']
            );

            if ($result) {
                return back()->with('success', '测试短信发送成功！');
            } else {
                return back()->with('error', '测试短信发送失败！');
            }
        } catch (Exception $e) {
            Log::error('测试短信发送失败', ['error' => $e->getMessage()]);
            return back()->with('error', '测试短信发送失败：' . $e->getMessage());
        }
    }

    /**
     * 获取模板参数（AJAX）
     */
    public function getTemplateParams(SmsTemplate $smsTemplate)
    {
        return response()->json([
            'params' => $smsTemplate->getTemplateParamsList(),
            'description' => $smsTemplate->param_description
        ]);
    }
}