<?php

namespace App\Filament\Resources\FriendshipLinkResource\Pages;

use App\Filament\Resources\FriendshipLinkResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFriendshipLinks extends ListRecords
{
    protected static string $resource = FriendshipLinkResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return '友情链接';
    }
}
