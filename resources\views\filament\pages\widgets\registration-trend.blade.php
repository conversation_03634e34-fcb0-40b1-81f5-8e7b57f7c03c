@php
    $dates = array_keys($data);
    $counts = array_values($data);
@endphp

<div class="mt-2">
    <canvas id="registrationTrendChart" height="100"></canvas>
</div>

<div class="space-y-2">
    @foreach($data as $date => $count)
        <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">{{ $date }}</span>
            <span class="text-sm font-medium">{{ $count }} 人</span>
        </div>
    @endforeach
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('registrationTrendChart').getContext('2d');
    const data = @json($data);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: Object.keys(data).map(date => date),
            datasets: [{
                label: '报名人数',
                data: Object.values(data),
                borderColor: 'rgb(59, 130, 246)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>
@endpush 