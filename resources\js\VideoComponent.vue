<template>
  <node-view-wrapper class="video-wrapper">
    <div class="video-container" :class="{ 'selected': selected }">
      <video
        :src="node.attrs.src"
        :controls="node.attrs.controls"
        :width="node.attrs.width"
        :height="node.attrs.height"
        :poster="node.attrs.poster"
        class="video-player"
        @click="selectNode"
      >
        您的浏览器不支持视频播放。
      </video>
      
      <!-- 删除按钮 -->
      <button
        v-if="selected"
        class="delete-button"
        @click="deleteNode"
        title="删除视频"
      >
        ×
      </button>
      
      <!-- 视频信息显示 -->
      <div v-if="selected" class="video-info">
        <p class="video-src">{{ node.attrs.src }}</p>
      </div>
    </div>
  </node-view-wrapper>
</template>

<script>
import { NodeViewWrapper } from '@tiptap/vue-3'

export default {
  name: 'VideoComponent',
  components: {
    NodeViewWrapper,
  },
  props: {
    editor: {
      type: Object,
      required: true,
    },
    node: {
      type: Object,
      required: true,
    },
    decorations: {
      type: Array,
      required: true,
    },
    selected: {
      type: <PERSON>olean,
      required: true,
    },
    extension: {
      type: Object,
      required: true,
    },
    getPos: {
      type: Function,
      required: true,
    },
    updateAttributes: {
      type: Function,
      required: true,
    },
    deleteNode: {
      type: Function,
      required: true,
    },
  },
  methods: {
    selectNode() {
      this.editor.commands.setNodeSelection(this.getPos())
    },
  },
}
</script>

<style scoped>
.video-wrapper {
  position: relative;
  margin: 1rem 0;
}

.video-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.video-container.selected {
  box-shadow: 0 0 0 2px #3b82f6;
  border-radius: 8px;
}

.video-player {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  cursor: pointer;
}

.delete-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.2s ease;
}

.delete-button:hover {
  background: rgba(239, 68, 68, 1);
}

.video-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px;
  font-size: 12px;
}

.video-src {
  margin: 0;
  word-break: break-all;
}
</style>