<?php

namespace App\Filament\Resources\FriendshipLinkCategoryResource\Pages;

use App\Filament\Resources\FriendshipLinkCategoryResource;
use Filament\Resources\Pages\CreateRecord;

class CreateFriendshipLinkCategory extends CreateRecord
{
    protected static string $resource = FriendshipLinkCategoryResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
