<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Services\SmsService;

echo "=== 测试修复后的SMS模板（简化版） ===\n";

try {
    // 直接查询数据库获取测试数据
    $registration = DB::table('registrations')
        ->select('id', 'name', 'activity_id')
        ->first();
    
    if (!$registration) {
        echo "未找到报名记录\n";
        exit(1);
    }
    
    echo "测试报名记录 ID: {$registration->id}\n";
    echo "报名人姓名: {$registration->name}\n";
    
    // 获取活动信息
    $activity = DB::table('activities')
        ->where('id', $registration->activity_id)
        ->first();
    
    // 获取活动详情
    $activityDetail = DB::table('activity_details')
        ->where('activity_id', $registration->activity_id)
        ->first();
    
    echo "活动名称: {$activity->title}\n";
    
    // 获取现场咨询通知模板
    $template = DB::table('sms_templates')
        ->where('name', '现场咨询通知')
        ->first();
    
    if (!$template) {
        echo "未找到现场咨询通知模板\n";
        exit(1);
    }
    
    echo "\n模板信息:\n";
    echo "模板名称: {$template->name}\n";
    echo "模板内容: {$template->description}\n";
    
    // 构建模板数据
    $templateData = [
        'name' => $registration->name,
        'topic' => $activityDetail->topic ?: ($activityDetail->theme ?: $activity->title),
        'time' => $activity->activity_time ? date('Y年m月d日 H:i', strtotime($activity->activity_time)) : '待定',
        'address' => $activityDetail->address ?: '待定',
        'obj' => $activityDetail->target_audience ?: ($activityDetail->target ?: '全体人员')
    ];
    
    echo "\n模板数据:\n";
    foreach ($templateData as $key => $value) {
        echo "#{$key}#: '{$value}'\n";
    }
    
    // 手动替换占位符（模拟SmsService的逻辑）
    $finalContent = $template->description;
    foreach ($templateData as $key => $value) {
        $finalContent = str_replace("#{$key}#", $value, $finalContent);
    }
    
    echo "\n=== 最终短信内容 ===\n";
    echo $finalContent . "\n";
    
    // 检查是否还有未替换的占位符
    if (preg_match('/#\w+#/', $finalContent)) {
        echo "\n⚠️ 警告：仍有未替换的占位符\n";
        preg_match_all('/#\w+#/', $finalContent, $matches);
        echo "未替换的占位符: " . implode(', ', $matches[0]) . "\n";
    } else {
        echo "\n✓ 所有占位符已正确替换\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
