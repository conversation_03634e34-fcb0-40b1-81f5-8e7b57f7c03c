<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Kalnoy\Nestedset\NodeTrait;

class FriendshipLinkCategory extends Model
{
    use HasFactory, NodeTrait;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'parent_id',
        'sort_order',
        'is_active',
        '_lft',
        '_rgt',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    // 关联友情链接
    public function friendshipLinks()
    {
        return $this->hasMany(FriendshipLink::class);
    }

    // 父级分类
    public function parent()
    {
        return $this->belongsTo(FriendshipLinkCategory::class, 'parent_id');
    }

    // 子分类
    public function children()
    {
        return $this->hasMany(FriendshipLinkCategory::class, 'parent_id');
    }

    // 获取所有后代分类
    public function descendants()
    {
        return $this->hasMany(FriendshipLinkCategory::class, 'parent_id')->with('descendants');
    }

    // 获取分类路径
    public function getPathAttribute()
    {
        $path = collect([$this->name]);
        $parent = $this->parent;

        while ($parent) {
            $path->prepend($parent->name);
            $parent = $parent->parent;
        }

        return $path->implode(' > ');
    }

    // 检查是否为顶级分类
    public function isRoot()
    {
        return is_null($this->parent_id);
    }

    // 检查是否有子分类
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    // 获取启用的分类
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // 获取顶级分类
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }
}
