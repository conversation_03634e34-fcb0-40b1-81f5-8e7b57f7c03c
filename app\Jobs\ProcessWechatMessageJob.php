<?php

namespace App\Jobs;

use App\Models\WechatMessage;
use App\Services\WechatMessageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessWechatMessageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected WechatMessage $message;

    /**
     * Create a new job instance.
     */
    public function __construct(WechatMessage $message)
    {
        $this->message = $message;
    }

    /**
     * Execute the job.
     */
    public function handle(WechatMessageService $messageService): void
    {
        try {
            Log::info('开始处理微信消息', [
                'message_id' => $this->message->id,
                'openid' => $this->message->openid,
                'msg_type' => $this->message->msg_type,
            ]);

            // 处理消息
            $result = $messageService->processMessage($this->message);

            if ($result) {
                $this->message->update([
                    'status' => 'processed',
                ]);
                
                Log::info('微信消息处理成功', [
                    'message_id' => $this->message->id,
                ]);
            } else {
                $this->message->update([
                    'status' => 'failed',
                ]);
                
                Log::warning('微信消息处理失败', [
                    'message_id' => $this->message->id,
                ]);
            }
        } catch (\Exception $e) {
            $this->message->update([
                'status' => 'failed',
            ]);
            
            Log::error('微信消息处理异常', [
                'message_id' => $this->message->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('微信消息处理任务失败', [
            'message_id' => $this->message->id,
            'error' => $exception->getMessage(),
        ]);
        
        $this->message->update([
            'status' => 'failed',
        ]);
    }
}