<?php

return [

    /**
     * Image upload enabled
     * 
     * WARNING: Setting this to false will use CKEditor's default Base64 upload method which is HIGHLY INEFFICIENT.
     * https://ckeditor.com/docs/ckeditor5/latest/features/images/image-upload/image-upload.html#base64-adapter
     */
    'upload_enabled' => true,

    /**
     * Image URL to upload to if one is not specified on the form field's ->uploadUrl() method
     */
    'upload_url' => '/admin/ckeditor/upload',

    /**
     * CKEditor UI language
     */
    'language' => 'zh-cn',

    /**
     * Upload disk for media files
     */
    'upload_disk' => 'public',

    /**
     * Upload directory for media files
     */
    'upload_directory' => 'uploads/media',

    /**
     * Maximum file size for uploads (in KB)
     */
    'max_file_size' => 51200, // 50MB

    /**
     * Accepted file types for upload
     */
    'accepted_file_types' => [
        // Images
        'image/jpeg',
        'image/png', 
        'image/webp',
        'image/svg+xml',
        'image/gif',
        // Videos
        'video/mp4',
        'video/webm',
        'video/ogg',
        'video/avi',
        'video/mov',
        'video/wmv',
        'video/flv',
        // Audio
        'audio/mp3',
        'audio/wav',
        'audio/ogg',
        'audio/aac',
        'audio/flac',
        // Documents
        'application/pdf',
    ],

];
