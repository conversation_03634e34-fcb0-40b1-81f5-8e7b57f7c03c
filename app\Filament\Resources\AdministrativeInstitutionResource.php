<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AdministrativeInstitutionResource\Pages;
use App\Models\AdministrativeInstitution;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use App\Filament\Resources\AdministrativeInstitutionResource\Filters\CascadingRegionFilter;

class AdministrativeInstitutionResource extends Resource
{
    protected static ?string $model = AdministrativeInstitution::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationGroup = '组织架构管理';

    protected static ?string $navigationLabel = '行政机构管理';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        TextInput::make('name')
                        ->label('单位名称')
                        ->required()
                        ->maxLength(255),
                    // 机构代码字段已移除：未被实际使用，简化表结构
                        Select::make('administrative_level')
                            ->label('行政级别')
                            ->options([
                                'province' => '省级',
                                'city' => '市级',
                                'district' => '区县级',
                                'town' => '乡镇级',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set) => [
                                $set('parent_id', null),
                            ]),
                    ])
                    ->columns(2),
                    
                Section::make('地区信息')
                    ->schema([
                        Select::make('province_code')
                            ->label('省份')
                            ->options(fn () => static::getProvinceOptions())
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state, callable $get) {
                                $set('city_code', null);
                                $set('district_code', null);
                                $set('town_code', null);
                                $set('city_name', null);
                                $set('district_name', null);
                                $set('town_name', null);
                                if ($state) {
                                    $province = static::getProvinceByCode($state);
                                    $set('province_name', $province?->name);
                                    // 更新完整地区名称
                                    static::updateFullRegionName($set, $get);
                                } else {
                                    $set('province_name', null);
                                    $set('full_region_name', null);
                                }
                            }),
                        Select::make('city_code')
                            ->label('城市')
                            ->options(function (callable $get) {
                                $provinceCode = $get('province_code');
                                return $provinceCode ? self::getCityOptions($provinceCode) : [];
                            })
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $set('district_code', null);
                                $set('town_code', null);
                                $set('district_name', null);
                                $set('town_name', null);
                                if ($state) {
                                    $city = self::getCityByCode($get('province_code'), $state);
                                    $set('city_name', $city['name'] ?? null);
                                    // 更新完整地区名称
                                    static::updateFullRegionName($set, $get);
                                } else {
                                    $set('city_name', null);
                                    static::updateFullRegionName($set, $get);
                                }
                            }),
                        Select::make('district_code')
                            ->label('区县')
                            ->options(function (callable $get) {
                                $cityCode = $get('city_code');
                                return $cityCode ? static::getDistrictOptions($cityCode) : [];
                            })
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state, callable $get) {
                                $set('town_code', null);
                                $set('town_name', null);
                                if ($state) {
                                    $district = static::getDistrictByCode($state);
                                    $set('district_name', $district?->name);
                                    // 更新完整地区名称
                                    static::updateFullRegionName($set, $get);
                                } else {
                                    $set('district_name', null);
                                    static::updateFullRegionName($set, $get);
                                }
                            }),
                        Select::make('town_code')
                            ->label('乡镇')
                            ->options(function (callable $get) {
                                $districtCode = $get('district_code');
                                return $districtCode ? static::getTownOptions($districtCode) : [];
                            })
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state, callable $get) {
                                if ($state) {
                                    $town = static::getTownByCode($state);
                                    $set('town_name', $town?->name);
                                    // 更新完整地区名称
                                    static::updateFullRegionName($set, $get);
                                } else {
                                    $set('town_name', null);
                                    static::updateFullRegionName($set, $get);
                                }
                            }),
                    ])
                    ->columns(2),
                    
                Section::make('组织信息')
                    ->schema([
                        TextInput::make('host_unit')
                            ->label('主办单位')
                            ->maxLength(255),
                        SelectTree::make('parent_id')
                            ->label('组织归属')
                            ->relationship('parent', 'name', 'parent_id', function ($query) {
                                return $query->whereIn('administrative_level', ['province', 'city', 'district'])
                                    ->select(['id', 'name', 'parent_id', 'administrative_level'])
                                    ->limit(50)
                                    ->orderBy('administrative_level')
                                    ->orderBy('name');
                            })
                            ->searchable()
                            ->placeholder('选择上级机构（省级机构可留空）')
                            ->enableBranchNode()
                            ->defaultOpenLevel(1)
                            ->visible(fn (callable $get) => in_array($get('administrative_level'), ['city', 'district', 'town'])),
                        Select::make('binding_status')
                            ->label('组织绑定状态')
                            ->options([
                                'bound' => '已绑定',
                                'unbound' => '未绑定',
                            ])
                            ->default('unbound')
                            ->required(),
                    ])
                    ->columns(2),
                    
                Section::make('联系信息')
                    ->schema([
                        TextInput::make('contact_person')
                            ->label('联系人')
                            ->maxLength(255),
                        TextInput::make('contact_phone')
                            ->label('联系电话')
                            ->tel()
                            ->maxLength(255),
                        TextInput::make('contact_email')
                            ->label('联系邮箱')
                            ->email()
                            ->maxLength(255),
                        Textarea::make('address')
                            ->label('详细地址')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Section::make('其他设置')
                    ->schema([
                        Textarea::make('description')
                            ->label('机构描述')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('单位名称')
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                // 机构代码列已移除：字段未被实际使用
                TextColumn::make('full_region_name')
                    ->label('地区')
                    ->getStateUsing(function ($record) {
                        // 确保地区信息正确显示
                        $parts = array_filter([
                            $record->province_name,
                            $record->city_name,
                            $record->district_name,
                            $record->town_name
                        ]);
                        return implode(' - ', $parts) ?: '未设置';
                    })
                    ->searchable(['province_name', 'city_name', 'district_name', 'town_name'])
                    ->limit(40),
                BadgeColumn::make('administrative_level')
                    ->label('行政级别')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'province' => '省级',
                        'city' => '市级',
                        'district' => '区县级',
                        'town' => '乡镇级',
                        default => '未知',
                    })
                    ->colors([
                        'danger' => 'province',
                        'warning' => 'city',
                        'success' => 'district',
                        'primary' => 'town',
                    ])
                    ->sortable(),
                TextColumn::make('host_unit')
                    ->label('主办单位')
                    ->searchable()
                    ->limit(30)
                    ->toggleable(),
                TextColumn::make('parent.name')
                    ->label('组织归属')
                    ->limit(30)
                    ->toggleable()
                    ->placeholder('无上级机构'),
                BadgeColumn::make('binding_status')
                    ->label('绑定状态')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'bound' => '已绑定',
                        'unbound' => '未绑定',
                        default => '未知',
                    })
                    ->colors([
                        'success' => 'bound',
                        'danger' => 'unbound',
                    ])
                    ->toggleable(),
                BadgeColumn::make('is_active')
                    ->label('状态')
                    ->formatStateUsing(fn ($state) => $state ? '启用' : '禁用')
                    ->colors([
                        'success' => true,
                        'danger' => false,
                    ])
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('administrative_level')
                    ->label('行政级别')
                    ->options([
                        'province' => '省级',
                        'city' => '市级',
                        'district' => '区县级',
                        'town' => '乡镇级',
                    ])
                    ->multiple(),
                SelectFilter::make('binding_status')
                    ->label('绑定状态')
                    ->options([
                        'bound' => '已绑定',
                        'unbound' => '未绑定',
                    ]),
                // 级联地区筛选器
                CascadingRegionFilter::make('region')
                    ->label('地区筛选'),
                SelectFilter::make('is_active')
                    ->label('状态')
                    ->options([
                        1 => '启用',
                        0 => '禁用',
                    ]),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('查看'),
                Tables\Actions\EditAction::make()
                    ->label('编辑'),
                Tables\Actions\DeleteAction::make()
                    ->label('删除'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->defaultPaginationPageOption(25) // 设置默认分页大小
            ->paginationPageOptions([10, 25, 50, 100]) // 分页选项
            ->striped() // 斑马纹
            ->poll('60s'); // 减少轮询频率
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    /**
     * 更新完整地区名称
     */
    protected static function updateFullRegionName(callable $set, callable $get): void
    {
        $parts = array_filter([
            $get('province_name'),
            $get('city_name'),
            $get('district_name'),
            $get('town_name')
        ]);
        
        $fullRegionName = implode(' - ', $parts);
        $set('full_region_name', $fullRegionName ?: null);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdministrativeInstitutions::route('/'),
            'tree' => Pages\TreeAdministrativeInstitutions::route('/tree'),
            'create' => Pages\CreateAdministrativeInstitution::route('/create'),
            'view' => Pages\ViewAdministrativeInstitution::route('/{record}'),
            'edit' => Pages\EditAdministrativeInstitution::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return '行政机构管理';
    }

    public static function getPluralModelLabel(): string
    {
        return '行政机构管理';
    }

    /**
     * 获取省份选项（从数据库获取，优化内存使用）
     */
    private static function getProvinceOptions(): array
    {
        return AdministrativeInstitution::select(['name', 'province_code'])
            ->where('administrative_level', 'province')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(100) // 限制结果数量
            ->pluck('name', 'province_code')
            ->toArray();
    }

    /**
     * 获取城市选项（从数据库获取，优化内存使用）
     */
    private static function getCityOptions(string $provinceCode): array
    {
        if (empty($provinceCode)) {
            return [];
        }
        
        return AdministrativeInstitution::select(['name', 'city_code'])
            ->where('administrative_level', 'city')
            ->where('province_code', $provinceCode)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(200) // 限制结果数量
            ->pluck('name', 'city_code')
            ->toArray();
    }

    /**
     * 获取区县选项（从数据库获取，优化内存使用）
     */
    private static function getDistrictOptions(string $cityCode): array
    {
        if (empty($cityCode)) {
            return [];
        }
        
        return AdministrativeInstitution::select(['name', 'district_code'])
            ->where('administrative_level', 'district')
            ->where('city_code', $cityCode)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(300) // 限制结果数量
            ->pluck('name', 'district_code')
            ->toArray();
    }

    /**
     * 获取乡镇选项（从数据库获取，优化内存使用）
     */
    private static function getTownOptions(string $districtCode): array
    {
        if (empty($districtCode)) {
            return [];
        }
        
        return AdministrativeInstitution::select(['name', 'town_code'])
            ->where('administrative_level', 'town')
            ->where('district_code', $districtCode)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(500) // 限制结果数量
            ->pluck('name', 'town_code')
            ->toArray();
    }

    /**
     * 根据代码获取省份信息（优化内存使用）
     */
    private static function getProvinceByCode(string $code): ?array
    {
        if (empty($code)) {
            return null;
        }
        
        $province = AdministrativeInstitution::select(['name'])
            ->where('administrative_level', 'province')
            ->where('province_code', $code)
            ->where('is_active', true)
            ->first();
        
        return $province ? ['code' => $code, 'name' => $province->name] : null;
    }

    /**
     * 根据代码获取城市信息（优化内存使用）
     */
    private static function getCityByCode(string $provinceCode, string $cityCode): ?array
    {
        if (empty($provinceCode) || empty($cityCode)) {
            return null;
        }
        
        $city = AdministrativeInstitution::select(['name'])
            ->where('administrative_level', 'city')
            ->where('province_code', $provinceCode)
            ->where('city_code', $cityCode)
            ->where('is_active', true)
            ->first();
        
        return $city ? ['code' => $cityCode, 'name' => $city->name] : null;
    }

    /**
     * 根据代码获取区县信息（优化内存使用）
     */
    private static function getDistrictByCode(string $cityCode, string $districtCode): ?array
    {
        if (empty($cityCode) || empty($districtCode)) {
            return null;
        }
        
        $district = AdministrativeInstitution::select(['name'])
            ->where('administrative_level', 'district')
            ->where('city_code', $cityCode)
            ->where('district_code', $districtCode)
            ->where('is_active', true)
            ->first();
        
        return $district ? ['code' => $districtCode, 'name' => $district->name] : null;
    }

    /**
     * 根据代码获取乡镇信息（优化内存使用）
     */
    private static function getTownByCode(string $districtCode, string $townCode): ?array
    {
        if (empty($districtCode) || empty($townCode)) {
            return null;
        }
        
        $town = AdministrativeInstitution::select(['name'])
            ->where('administrative_level', 'town')
            ->where('district_code', $districtCode)
            ->where('town_code', $townCode)
            ->where('is_active', true)
            ->first();
        
        return $town ? ['code' => $townCode, 'name' => $town->name] : null;
    }
}