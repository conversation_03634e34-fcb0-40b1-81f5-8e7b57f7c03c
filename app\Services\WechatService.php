<?php

namespace App\Services;

use App\Models\WechatAccount;
use App\Models\WechatTemplate;
use Illuminate\Support\Facades\Log;
use EasyWeChat\OfficialAccount\Application;

class WechatService
{
    protected $app;
    protected $account;

    public function __construct(WechatAccount $account = null)
    {
        $this->account = $account ?: $this->getDefaultAccount();
        
        if ($this->account) {
            $collectConfig = $this->account->getCollectConfig();
            
            $config = [
                'app_id' => $this->account->app_id,
                'secret' => $this->account->app_secret,
                'token' => $collectConfig['token'] ?? $this->account->token ?? env('WECHAT_TOKEN'),
                'aes_key' => $collectConfig['encoding_aes_key'] ?? $this->account->aes_key ?? env('WECHAT_AES_KEY'),
                'response_type' => 'array',
            ];

            $this->app = new Application($config);
        }
    }
    
    /**
     * 获取默认的微信账号
     */
    protected function getDefaultAccount(): ?WechatAccount
    {
        return WechatAccount::where('is_active', true)->first();
    }
    
    /**
     * 设置当前使用的微信账号
     */
    public function setAccount(WechatAccount $account): self
    {
        $this->account = $account;
        $collectConfig = $account->getCollectConfig();
        
        $config = [
            'app_id' => $account->app_id,
            'secret' => $account->app_secret,
            'token' => $collectConfig['token'] ?? env('WECHAT_TOKEN'),
            'aes_key' => $collectConfig['encoding_aes_key'] ?? env('WECHAT_AES_KEY'),
            'response_type' => 'array',
        ];

        $this->app = Factory::officialAccount($config);
        
        return $this;
    }

    /**
     * 发送模板消息
     * @param string $openid 接收者openid
     * @param string $templateCode 模板代码
     * @param array $data 模板数据
     * @param string $url 点击跳转的URL
     * @return bool
     */
    public function sendTemplateMessage(string $openid, string $templateCode, array $data, string $url = ''): bool
    {
        try {
            if (!$this->account || !$this->app) {
                Log::error('微信账号未配置或初始化失败');
                return false;
            }
            
            $template = WechatTemplate::where('code', $templateCode)
                ->where('status', true)
                ->first();

            if (!$template) {
                Log::error('微信模板未找到', ['code' => $templateCode]);
                return false;
            }

            $result = $this->app->template_message->send([
                'touser' => $openid,
                'template_id' => $template->template_id,
                'url' => $url,
                'data' => $data,
            ]);

            if (isset($result['errcode']) && $result['errcode'] === 0) {
                Log::info('微信模板消息发送成功', [
                    'account_id' => $this->account->id,
                    'openid' => $openid,
                    'template' => $templateCode,
                    'result' => $result
                ]);
                return true;
            } else {
                Log::error('微信模板消息发送失败', [
                    'account_id' => $this->account->id,
                    'openid' => $openid,
                    'template' => $templateCode,
                    'result' => $result
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('微信模板消息发送异常', [
                'account_id' => $this->account?->id,
                'openid' => $openid,
                'template' => $templateCode,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取当前账号
     */
    public function getAccount(): ?WechatAccount
    {
        return $this->account;
    }
}