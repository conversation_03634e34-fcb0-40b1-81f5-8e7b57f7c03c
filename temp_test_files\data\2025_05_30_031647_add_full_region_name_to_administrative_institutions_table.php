<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('administrative_institutions', function (Blueprint $table) {
            $table->string('full_region_name')->nullable()->after('town_name')->comment('完整地区名称');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('administrative_institutions', function (Blueprint $table) {
            $table->dropColumn('full_region_name');
        });
    }
};
