<?php

namespace App\Filament\Resources\DatabaseBackupResource\Pages;

use App\Filament\Resources\DatabaseBackupResource;
use App\Services\DatabaseBackupService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;

class ListDatabaseBackups extends ListRecords
{
    protected static string $resource = DatabaseBackupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('cleanup_old_backups')
                ->label('清理过期备份')
                ->icon('heroicon-o-trash')
                ->color('warning')
                ->form([
                    \Filament\Forms\Components\TextInput::make('keep_days')
                        ->label('保留天数')
                        ->numeric()
                        ->default(30)
                        ->required()
                        ->helperText('删除指定天数之前的备份文件'),
                ])
                ->action(function (array $data) {
                    $backupService = app(DatabaseBackupService::class);
                    $deletedCount = $backupService->cleanupOldBackups($data['keep_days']);
                    
                    Notification::make()
                        ->title('清理完成')
                        ->body("已清理 {$deletedCount} 个过期备份")
                        ->success()
                        ->send();
                })
                ->requiresConfirmation()
                ->modalHeading('清理过期备份')
                ->modalDescription('这将删除指定天数之前的所有备份文件和记录，操作不可恢复。')
                ->modalSubmitActionLabel('确认清理'),
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DatabaseBackupResource\Widgets\BackupStatsOverview::class,
        ];
    }
}
