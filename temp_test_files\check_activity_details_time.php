<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 检查activity_details表的时间字段 ===\n";

try {
    // 查看activity_details表的实际数据，特别关注时间字段
    $activityDetails = DB::table('activity_details')
        ->limit(5)
        ->get();
    
    foreach ($activityDetails as $detail) {
        echo "\nactivity_detail ID: {$detail->id}\n";
        echo "activity_id: {$detail->activity_id}\n";
        
        // 显示所有字段，特别关注时间相关的
        foreach ($detail as $key => $value) {
            if (strpos($key, 'time') !== false || strpos($key, 'date') !== false || $key === 'activity_time') {
                echo "  {$key}: '{$value}'\n";
            }
        }
        
        // 检查是否有activity_time字段
        if (property_exists($detail, 'activity_time')) {
            echo "  ✓ 找到activity_time字段: '{$detail->activity_time}'\n";
        } else {
            echo "  ✗ 没有activity_time字段\n";
        }
    }
    
    // 直接查询activity_time字段
    echo "\n=== 直接查询activity_time字段 ===\n";
    try {
        $timeData = DB::table('activity_details')
            ->select('id', 'activity_time')
            ->whereNotNull('activity_time')
            ->limit(3)
            ->get();
        
        foreach ($timeData as $data) {
            echo "ID: {$data->id}, activity_time: '{$data->activity_time}'\n";
        }
    } catch (Exception $e) {
        echo "查询activity_time字段失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
