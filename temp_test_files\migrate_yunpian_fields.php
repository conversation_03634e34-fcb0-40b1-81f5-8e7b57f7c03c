<?php

echo "=== 数据库字段迁移以匹配云片网要求 ===\n\n";

// 数据库连接配置
$host = 'localhost';
$dbname = '123_178188_xyz';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host={$host};dbname={$dbname};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ 数据库连接成功\n\n";
    
    // 检查并添加topic字段
    $checkTopic = $pdo->query("SHOW COLUMNS FROM activity_details LIKE 'topic'");
    if ($checkTopic->rowCount() == 0) {
        $pdo->exec("ALTER TABLE activity_details ADD COLUMN topic VARCHAR(255) NULL COMMENT '活动主题-对应云片网#topic#参数'");
        echo "✓ 添加 activity_details.topic 字段\n";
    } else {
        echo "- activity_details.topic 字段已存在\n";
    }
    
    // 检查并添加target_audience字段
    $checkTarget = $pdo->query("SHOW COLUMNS FROM activity_details LIKE 'target_audience'");
    if ($checkTarget->rowCount() == 0) {
        $pdo->exec("ALTER TABLE activity_details ADD COLUMN target_audience VARCHAR(255) NULL COMMENT '目标受众-对应云片网#obj#参数'");
        echo "✓ 添加 activity_details.target_audience 字段\n";
    } else {
        echo "- activity_details.target_audience 字段已存在\n";
    }
    
    echo "\n迁移现有数据:\n";
    
    // 迁移theme数据到topic
    $themeStmt = $pdo->prepare("UPDATE activity_details SET topic = theme WHERE theme IS NOT NULL AND theme != '' AND (topic IS NULL OR topic = '')");
    $themeStmt->execute();
    $themeCount = $themeStmt->rowCount();
    echo "✓ 迁移了 {$themeCount} 条theme数据到topic字段\n";
    
    // 迁移target数据到target_audience
    $targetStmt = $pdo->prepare("UPDATE activity_details SET target_audience = target WHERE target IS NOT NULL AND target != '' AND (target_audience IS NULL OR target_audience = '')");
    $targetStmt->execute();
    $targetCount = $targetStmt->rowCount();
    echo "✓ 迁移了 {$targetCount} 条target数据到target_audience字段\n";
    
    echo "\n✓ 数据库字段迁移完成\n";
    
} catch (PDOException $e) {
    echo "✗ 数据库操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 迁移完成 ===\n";
