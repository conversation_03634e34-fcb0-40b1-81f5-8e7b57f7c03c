<?php

namespace App\Filament\Resources\SchoolResource\Filters;

use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use App\Models\AdministrativeInstitution;

class CascadingRegionFilter extends Filter
{
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->form([
            Grid::make(4)
                ->schema([
                    Select::make('province_code')
                        ->label('省份')
                        ->placeholder('选择省份')
                        ->options(fn () => $this->getProvinceOptions())
                        ->searchable()
                        ->live()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('city_code', null);
                            $set('district_code', null);
                            $set('town_code', null);
                        }),
                    
                    Select::make('city_code')
                        ->label('城市')
                        ->placeholder('选择城市')
                        ->options(function (callable $get) {
                            $provinceCode = $get('province_code');
                            if (!$provinceCode) {
                                return [];
                            }
                            return $this->getCityOptions($provinceCode);
                        })
                        ->searchable()
                        ->live()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('district_code', null);
                            $set('town_code', null);
                        }),
                    
                    Select::make('district_code')
                        ->label('区县')
                        ->placeholder('选择区县')
                        ->options(function (callable $get) {
                            $cityCode = $get('city_code');
                            if (!$cityCode) {
                                return [];
                            }
                            return $this->getDistrictOptions($cityCode);
                        })
                        ->searchable()
                        ->live()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('town_code', null);
                        }),
                    
                    Select::make('town_code')
                        ->label('乡镇')
                        ->placeholder('选择乡镇')
                        ->options(function (callable $get) {
                            $districtCode = $get('district_code');
                            if (!$districtCode) {
                                return [];
                            }
                            return $this->getTownOptions($districtCode);
                        })
                        ->searchable(),
                ])
        ]);
    }

    public function apply(Builder $query, array $data = []): Builder
    {
        return $query
            ->when(
                $data['province_code'] ?? null,
                fn (Builder $query, $value): Builder => $query->where('province_code', $value),
            )
            ->when(
                $data['city_code'] ?? null,
                fn (Builder $query, $value): Builder => $query->where('city_code', $value),
            )
            ->when(
                $data['district_code'] ?? null,
                fn (Builder $query, $value): Builder => $query->where('district_code', $value),
            )
            ->when(
                $data['town_code'] ?? null,
                fn (Builder $query, $value): Builder => $query->where('town_code', $value),
            );
    }

    /**
     * 获取省份选项
     */
    protected function getProvinceOptions(): array
    {
        return Cache::remember('schools_provinces', 300, function () {
            return AdministrativeInstitution::where('administrative_level', 'province')
                ->whereNotNull('province_code')
                ->whereNotNull('province_name')
                ->where('province_name', '!=', '')
                ->orderBy('province_name')
                ->pluck('province_name', 'province_code')
                ->filter()
                ->toArray();
        });
    }

    /**
     * 获取城市选项
     */
    protected function getCityOptions(string $provinceCode): array
    {
        return Cache::remember("schools_cities_{$provinceCode}", 300, function () use ($provinceCode) {
            return AdministrativeInstitution::where('administrative_level', 'city')
                ->where('province_code', $provinceCode)
                ->whereNotNull('city_code')
                ->whereNotNull('city_name')
                ->where('city_name', '!=', '')
                ->orderBy('city_name')
                ->pluck('city_name', 'city_code')
                ->filter()
                ->toArray();
        });
    }

    /**
     * 获取区县选项
     */
    protected function getDistrictOptions(string $cityCode): array
    {
        return Cache::remember("schools_districts_{$cityCode}", 300, function () use ($cityCode) {
            return AdministrativeInstitution::where('administrative_level', 'district')
                ->where('city_code', $cityCode)
                ->whereNotNull('district_code')
                ->whereNotNull('district_name')
                ->where('district_name', '!=', '')
                ->orderBy('district_name')
                ->pluck('district_name', 'district_code')
                ->filter()
                ->toArray();
        });
    }

    /**
     * 获取乡镇选项
     */
    protected function getTownOptions(string $districtCode): array
    {
        return Cache::remember("schools_towns_{$districtCode}", 300, function () use ($districtCode) {
            return AdministrativeInstitution::where('administrative_level', 'town')
                ->where('district_code', $districtCode)
                ->whereNotNull('town_code')
                ->whereNotNull('town_name')
                ->where('town_name', '!=', '')
                ->orderBy('town_name')
                ->pluck('town_name', 'town_code')
                ->filter()
                ->toArray();
        });
    }

    public static function getDefaultName(): ?string
    {
        return 'school_cascading_region';
    }
}