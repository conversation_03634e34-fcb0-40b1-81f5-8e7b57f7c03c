<x-filament-panels::page>
    <div class="space-y-6">
        <!-- 筛选表单 -->
        <div class="bg-white rounded-lg shadow p-6">
            <form wire:submit="getViewData">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {{ $this->form }}
                </div>
                <div class="mt-4">
                    <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                        应用筛选
                    </button>
                </div>
            </form>
        </div>

        <!-- 报名趋势图 -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">报名趋势</h3>
                <button wire:click="exportTrend" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                    导出数据
                </button>
            </div>
            <canvas id="trendChart" height="100"></canvas>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 报名来源分布 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">报名来源分布</h3>
                    <button wire:click="exportSource" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                        导出数据
                    </button>
                </div>
                <canvas id="sourceChart" height="300"></canvas>
            </div>

            <!-- 性别比例 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">性别比例</h3>
                    <button wire:click="exportGender" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                        导出数据
                    </button>
                </div>
                <canvas id="genderChart" height="300"></canvas>
            </div>

            <!-- 年级分布 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">年级分布（Top 10）</h3>
                    <button wire:click="exportGrade" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                        导出数据
                    </button>
                </div>
                <canvas id="gradeChart" height="300"></canvas>
            </div>

            <!-- 单位分布 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">单位分布（Top 10）</h3>
                    <button wire:click="exportOrganization" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                        导出数据
                    </button>
                </div>
                <canvas id="organizationChart" height="300"></canvas>
            </div>
        </div>

        <!-- 活动报名情况 -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">活动报名情况（Top 10）</h3>
                <button wire:click="exportActivity" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                    导出数据
                </button>
            </div>
            <canvas id="activityChart" height="300"></canvas>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 报名趋势图
        new Chart(document.getElementById('trendChart'), {
            type: 'line',
            data: {
                labels: {!! json_encode($trendData->pluck('date')) !!},
                datasets: [{
                    label: '报名人数',
                    data: {!! json_encode($trendData->pluck('count')) !!},
                    borderColor: 'rgb(59, 130, 246)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // 报名来源分布
        new Chart(document.getElementById('sourceChart'), {
            type: 'pie',
            data: {
                labels: {!! json_encode($sourceData->pluck('source')) !!},
                datasets: [{
                    data: {!! json_encode($sourceData->pluck('count')) !!},
                    backgroundColor: [
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)'
                    ]
                }]
            }
        });

        // 性别比例
        new Chart(document.getElementById('genderChart'), {
            type: 'pie',
            data: {
                labels: {!! json_encode($genderData->pluck('gender')->map(function($gender) {
                    return $gender === 'male' ? '男' : '女';
                })) !!},
                datasets: [{
                    data: {!! json_encode($genderData->pluck('count')) !!},
                    backgroundColor: [
                        'rgb(59, 130, 246)',
                        'rgb(236, 72, 153)'
                    ]
                }]
            }
        });

        // 年级分布
        new Chart(document.getElementById('gradeChart'), {
            type: 'bar',
            data: {
                labels: {!! json_encode($gradeData->pluck('grade')) !!},
                datasets: [{
                    label: '报名人数',
                    data: {!! json_encode($gradeData->pluck('count')) !!},
                    backgroundColor: 'rgb(59, 130, 246)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // 单位分布
        new Chart(document.getElementById('organizationChart'), {
            type: 'bar',
            data: {
                labels: {!! json_encode($organizationData->pluck('organization')) !!},
                datasets: [{
                    label: '报名人数',
                    data: {!! json_encode($organizationData->pluck('count')) !!},
                    backgroundColor: 'rgb(16, 185, 129)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // 活动报名情况
        new Chart(document.getElementById('activityChart'), {
            type: 'bar',
            data: {
                labels: {!! json_encode($activityData->map(function($item) { return $item->activity && $item->activity->title ? $item->activity->title : $item->theme; })) !!},
                datasets: [{
                    label: '报名人数',
                    data: {!! json_encode($activityData->pluck('registrations_count')) !!},
                    backgroundColor: 'rgb(245, 158, 11)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    </script>
    @endpush
</x-filament-panels::page>