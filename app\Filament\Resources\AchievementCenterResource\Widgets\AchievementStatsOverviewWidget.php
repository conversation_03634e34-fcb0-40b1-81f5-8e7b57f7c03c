<?php

namespace App\Filament\Resources\AchievementCenterResource\Widgets;

use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\User;
use App\Models\UserPoint;

class AchievementStatsOverviewWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $totalUsers = User::where('is_verified', true)->count();
        $totalPoints = UserPoint::sum('points');
        $todayPoints = UserPoint::whereDate('earned_date', today())->sum('points');
        $activeUsers = User::where('is_verified', true)
            ->whereHas('points', function ($query) {
                $query->whereDate('earned_date', today());
            })->count();

        return [
            Stat::make('实名用户总数', $totalUsers)
                ->description('已完成实名认证的用户')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),
            
            Stat::make('总积分', $totalPoints)
                ->description('系统累计发放积分')
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),
            
            Stat::make('今日积分', $todayPoints)
                ->description('今天获得的积分')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info'),
            
            Stat::make('今日活跃用户', $activeUsers)
                ->description('今天获得积分的用户数')
                ->descriptionIcon('heroicon-m-fire')
                ->color('danger'),
        ];
    }
}
