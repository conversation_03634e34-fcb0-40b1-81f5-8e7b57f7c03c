<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('database_backups', function (Blueprint $table) {
            $table->unsignedBigInteger('schedule_id')->nullable()->after('created_by')->comment('关联的计划ID');
            $table->foreign('schedule_id')->references('id')->on('backup_schedules')->onDelete('set null');
            $table->index('schedule_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('database_backups', function (Blueprint $table) {
            $table->dropForeign(['schedule_id']);
            $table->dropIndex(['schedule_id']);
            $table->dropColumn('schedule_id');
        });
    }
};
