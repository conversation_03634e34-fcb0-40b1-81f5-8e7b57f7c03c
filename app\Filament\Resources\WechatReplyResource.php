<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WechatReplyResource\Pages;
use App\Models\WechatAccount;
use App\Models\WechatReply;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WechatReplyResource extends Resource
{
    protected static ?string $model = WechatReply::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';

    protected static ?string $navigationLabel = '自动回复';

    protected static ?string $modelLabel = '自动回复';

    protected static ?string $pluralModelLabel = '自动回复';

    protected static ?string $navigationGroup = '微信推文管理';

    protected static ?int $navigationSort = 23;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\Select::make('wechat_account_id')
                            ->label('微信账号')
                            ->options(WechatAccount::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable(),
                        
                        Forms\Components\TextInput::make('name')
                            ->label('规则名称')
                            ->required()
                            ->maxLength(64)
                            ->helperText('便于管理的规则名称'),
                        
                        Forms\Components\Select::make('type')
                            ->label('回复类型')
                            ->options([
                                'subscribe' => '关注回复',
                                'default' => '默认回复',
                                'keyword' => '关键词回复',
                                'event' => '事件回复',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state !== 'keyword') {
                                    $set('keywords', null);
                                    $set('match_type', 'exact');
                                }
                                if ($state !== 'event') {
                                    $set('trigger_type', $state);
                                }
                            }),
                        
                        Forms\Components\Select::make('trigger_type')
                            ->label('触发类型')
                            ->options([
                                'subscribe' => '关注触发',
                                'keyword' => '关键词触发',
                                'default' => '默认触发',
                                'click' => '菜单点击',
                                'scan' => '扫码触发',
                            ])
                            ->required()
                            ->visible(fn (callable $get) => $get('type') === 'event'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('触发条件')
                    ->schema([
                        Forms\Components\TagsInput::make('keywords')
                            ->label('关键词列表')
                            ->placeholder('输入关键词后按回车添加')
                            ->helperText('支持多个关键词，用户发送任一关键词都会触发回复')
                            ->visible(fn (callable $get) => $get('type') === 'keyword')
                            ->required(fn (callable $get) => $get('type') === 'keyword'),
                        
                        Forms\Components\Select::make('match_type')
                            ->label('匹配类型')
                            ->options([
                                'exact' => '完全匹配',
                                'partial' => '部分匹配',
                                'regex' => '正则匹配',
                            ])
                            ->default('exact')
                            ->helperText('完全匹配：用户消息必须完全等于关键词；部分匹配：用户消息包含关键词即可；正则匹配：使用正则表达式匹配')
                            ->visible(fn (callable $get) => $get('type') === 'keyword'),
                        
                        Forms\Components\TextInput::make('priority')
                            ->label('优先级')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越大优先级越高，当多个规则匹配时，优先级高的先执行'),
                    ])
                    ->columns(2)
                    ->visible(fn (callable $get) => in_array($get('type'), ['keyword', 'event'])),
                
                Forms\Components\Section::make('回复内容')
                    ->schema([
                        Forms\Components\Select::make('reply_type')
                            ->label('回复内容类型')
                            ->options([
                                'text' => '文本回复',
                                'image' => '图片回复',
                                'voice' => '语音回复',
                                'video' => '视频回复',
                                'music' => '音乐回复',
                                'news' => '图文回复',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                $set('reply_content', null);
                                $set('reply_media_id', null);
                                $set('reply_url', null);
                                $set('reply_title', null);
                                $set('reply_description', null);
                                $set('reply_thumb_media_id', null);
                            }),
                        
                        Forms\Components\Textarea::make('reply_content')
                            ->label('回复内容')
                            ->rows(4)
                            ->required(fn (callable $get) => $get('reply_type') === 'text')
                            ->visible(fn (callable $get) => in_array($get('reply_type'), ['text', 'music', 'news']))
                            ->helperText(function (callable $get) {
                                $type = $get('reply_type');
                                return match ($type) {
                                    'text' => '直接输入要回复的文本内容',
                                    'music' => '音乐描述信息',
                                    'news' => '图文消息内容（JSON格式）',
                                    default => '',
                                };
                            }),
                        
                        Forms\Components\TextInput::make('reply_media_id')
                            ->label('媒体文件ID')
                            ->maxLength(128)
                            ->required(fn (callable $get) => in_array($get('reply_type'), ['image', 'voice', 'video']))
                            ->visible(fn (callable $get) => in_array($get('reply_type'), ['image', 'voice', 'video', 'music', 'news']))
                            ->helperText('从微信素材库获取的媒体文件ID'),
                        
                        Forms\Components\TextInput::make('reply_url')
                            ->label('链接地址')
                            ->url()
                            ->maxLength(512)
                            ->visible(fn (callable $get) => in_array($get('reply_type'), ['music', 'news']))
                            ->helperText('音乐链接或图文消息跳转链接'),
                        
                        Forms\Components\TextInput::make('reply_title')
                            ->label('标题')
                            ->maxLength(256)
                            ->visible(fn (callable $get) => in_array($get('reply_type'), ['music', 'news']))
                            ->helperText('音乐标题或图文消息标题'),
                        
                        Forms\Components\Textarea::make('reply_description')
                            ->label('描述')
                            ->rows(2)
                            ->visible(fn (callable $get) => in_array($get('reply_type'), ['music', 'news']))
                            ->helperText('音乐描述或图文消息描述'),
                        
                        Forms\Components\TextInput::make('reply_thumb_media_id')
                            ->label('缩略图媒体ID')
                            ->maxLength(128)
                            ->visible(fn (callable $get) => in_array($get('reply_type'), ['music', 'news']))
                            ->helperText('音乐或图文消息的缩略图媒体ID'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('其他设置')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true)
                            ->helperText('关闭后该回复规则将不会生效'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('wechatAccount.name')
                    ->label('微信账号')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('规则名称')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('type')
                    ->label('回复类型')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'subscribe' => '关注回复',
                        'default' => '默认回复',
                        'keyword' => '关键词回复',
                        'event' => '事件回复',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'subscribe' => 'success',
                        'default' => 'warning',
                        'keyword' => 'primary',
                        'event' => 'info',
                        default => 'gray',
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('keywords')
                    ->label('关键词')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return implode(', ', array_slice($state, 0, 3)) . (count($state) > 3 ? '...' : '');
                        }
                        return $state ?? '-';
                    })
                    ->placeholder('-')
                    ->limit(30),
                
                Tables\Columns\TextColumn::make('reply_type')
                    ->label('回复类型')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'text' => '文本',
                        'image' => '图片',
                        'voice' => '语音',
                        'video' => '视频',
                        'music' => '音乐',
                        'news' => '图文',
                        default => $state,
                    })
                    ->badge()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('reply_content')
                    ->label('回复内容')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                
                Tables\Columns\TextColumn::make('priority')
                    ->label('优先级')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('usage_count')
                    ->label('使用次数')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('last_used_at')
                    ->label('最后使用')
                    ->dateTime()
                    ->placeholder('-')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('wechat_account_id')
                    ->label('微信账号')
                    ->options(WechatAccount::active()->pluck('name', 'id')),
                
                Tables\Filters\SelectFilter::make('type')
                    ->label('回复类型')
                    ->options([
                        'subscribe' => '关注回复',
                        'default' => '默认回复',
                        'keyword' => '关键词回复',
                        'event' => '事件回复',
                    ]),
                
                Tables\Filters\SelectFilter::make('reply_type')
                    ->label('回复内容类型')
                    ->options([
                        'text' => '文本',
                        'image' => '图片',
                        'voice' => '语音',
                        'video' => '视频',
                        'music' => '音乐',
                        'news' => '图文',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态'),
                
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('test_reply')
                    ->label('测试回复')
                    ->icon('heroicon-o-play')
                    ->color('info')
                    ->form([
                        Forms\Components\TextInput::make('test_message')
                            ->label('测试消息')
                            ->required()
                            ->helperText('输入要测试的消息内容'),
                    ])
                    ->action(function (WechatReply $record, array $data) {
                        // 这里可以添加测试回复的逻辑
                        // 暂时只显示成功消息
                        \Filament\Notifications\Notification::make()
                            ->title('测试完成')
                            ->body('测试消息：' . $data['test_message'])
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('priority', 'desc')
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWechatReplies::route('/'),
            'create' => Pages\CreateWechatReply::route('/create'),
            'view' => Pages\ViewWechatReply::route('/{record}'),
            'edit' => Pages\EditWechatReply::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}