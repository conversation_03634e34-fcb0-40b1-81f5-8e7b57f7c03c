<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RegistrationResource\Pages;
use App\Models\Registration;
use App\Services\SmsService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Select;
use App\Models\SmsTemplate;

class RegistrationResource extends Resource
{
    protected static ?string $model = Registration::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = '报名记录';

    protected static ?string $navigationGroup = '报名系统';

    protected static ?int $navigationGroupSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('activity_detail_id')
                    ->label('活动场次')
                    ->relationship('activityDetail', 'topic')
                    ->getOptionLabelFromRecordUsing(fn ($record) => !empty($record->topic) ? $record->topic : '未设置主题')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->label('姓名')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->label('手机号')
                    ->tel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('organization')
                    ->label('单位')
                    ->maxLength(255),
                Forms\Components\TextInput::make('grade')
                    ->label('年级')
                    ->maxLength(255),
                Forms\Components\Select::make('gender')
                    ->label('性别')
                    ->options([
                        'male' => '男',
                        'female' => '女',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('source')
                    ->label('来源')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('status')
                    ->label('状态')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('activityDetail.topic')
                    ->label('活动场次')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('姓名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('手机号')
                    ->searchable(),
                Tables\Columns\TextColumn::make('organization')
                    ->label('单位')
                    ->searchable(),
                Tables\Columns\TextColumn::make('grade')
                    ->label('年级')
                    ->searchable(),
                Tables\Columns\TextColumn::make('gender')
                    ->label('性别')
                    ->badge()
                    ->color(fn ($state) => $state === 'male' ? 'primary' : ($state === 'female' ? 'warning' : 'secondary'))
                    ->formatStateUsing(function ($state) {
                        return $state === 'male' ? '男' : ($state === 'female' ? '女' : '-');
                    }),
                Tables\Columns\TextColumn::make('source')
                    ->label('来源')
                    ->searchable(),
                Tables\Columns\IconColumn::make('status')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('activityDetail')
                    ->relationship('activityDetail', 'topic')
                    ->getOptionLabelFromRecordUsing(fn ($record) => $record->topic ?? '未设置主题'),
                Tables\Filters\SelectFilter::make('gender')
                    ->options([
                        'male' => '男',
                        'female' => '女',
                    ]),
                Tables\Filters\TernaryFilter::make('status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Action::make('sendSms')
                    ->label('发送短信')
                    ->icon('heroicon-o-chat-bubble-left')
                    ->form([
                        Select::make('template_code')
                            ->label('短信模板')
                            ->options(SmsTemplate::all()->mapWithKeys(fn($tpl) => [$tpl->code => $tpl->name . '（' . $tpl->description . '）']))
                            ->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $smsService = app(\App\Services\SmsService::class);
                        $sceneCode = $data['template_code'];
                        $tpl = SmsTemplate::where('code', $sceneCode)->first();
                        $vars = [];
                        if ($tpl && $tpl->description) {
                            preg_match_all('/#(\w+)#/', $tpl->description, $matches);
                            foreach ($matches[1] as $var) {
                                $vars[$var] = $record->$var ?? ($record->activityDetail->$var ?? '');
                            }
                        }
                        try {
                            $success = $smsService->sendSms($record->phone, $sceneCode, $vars);
                            if ($success) {
                                Notification::make()
                                    ->title('短信发送成功')
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('短信发送失败')
                                    ->danger()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('短信发送异常')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->modalHeading('发送短信')
                    ->modalSubmitActionLabel('发送')
                    ->modalCancelActionLabel('取消'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('export')
                        ->label('导出选中')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function (Collection $records) {
                            return response()->streamDownload(function () use ($records) {
                                $csv = fopen('php://output', 'w');
                                
                                // 添加 UTF-8 BOM
                                fprintf($csv, chr(0xEF).chr(0xBB).chr(0xBF));
                                
                                // 添加表头
                                fputcsv($csv, [
                                    '序号',
                                    '活动主题',
                                    '姓名',
                                    '手机号',
                                    '单位',
                                    '年级',
                                    '性别',
                                    '来源',
                                    '报名时间',
                                    '状态'
                                ]);
                                
                                // 添加数据
                                $records->each(function ($record, $index) use ($csv) {
                                    fputcsv($csv, [
                                        $index + 1,
                                        $record->activityDetail->topic,
                                        $record->name,
                                        $record->phone,
                                        $record->organization,
                                        $record->grade,
                                        $record->gender === 'male' ? '男' : '女',
                                        $record->source,
                                        $record->created_at->format('Y-m-d H:i:s'),
                                        $record->status ? '有效' : '无效'
                                    ]);
                                });
                                
                                fclose($csv);
                            }, '报名数据.csv');
                        }),
                    BulkAction::make('sendSms')
                        ->label('发送短信通知')
                        ->icon('heroicon-o-chat-bubble-left-right')
                        ->form([
                            Select::make('template_code')
                                ->label('短信模板')
                                ->options(SmsTemplate::all()->mapWithKeys(fn($tpl) => [$tpl->code => $tpl->name . '（' . $tpl->description . '）']))
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data) {
                            $smsService = app(SmsService::class);
                            $sceneCode = $data['template_code'];
                            $tpl = SmsTemplate::where('code', $sceneCode)->first();
                            $successCount = 0;
                            $failCount = 0;
                            foreach ($records as $record) {
                                try {
                                    $vars = [];
                                    if ($tpl && $tpl->description) {
                                        preg_match_all('/#(\w+)#/', $tpl->description, $matches);
                                        foreach ($matches[1] as $var) {
                                            $vars[$var] = $record->$var ?? ($record->activityDetail->$var ?? '');
                                        }
                                    }
                                    $ok = $smsService->sendSms($record->phone, $sceneCode, $vars);
                                    if ($ok) {
                                        $successCount++;
                                    } else {
                                        $failCount++;
                                    }
                                } catch (\Exception $e) {
                                    $failCount++;
                                }
                            }
                            Notification::make()
                                ->title('短信发送完成')
                                ->body("成功发送 {$successCount} 条，失败 {$failCount} 条")
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->modalHeading('发送短信通知')
                        ->modalDescription('确定要向选中的报名者发送短信通知吗？')
                        ->modalSubmitActionLabel('发送')
                        ->modalCancelActionLabel('取消'),
                ]),
            ])
            ->headerActions([
                Action::make('exportAll')
                    ->label('导出全部')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function () {
                        return response()->streamDownload(function () {
                            $csv = fopen('php://output', 'w');
                            
                            // 添加 UTF-8 BOM
                            fprintf($csv, chr(0xEF).chr(0xBB).chr(0xBF));
                            
                            // 添加表头
                            fputcsv($csv, [
                                '序号',
                                '活动主题',
                                '姓名',
                                '手机号',
                                '单位',
                                '年级',
                                '性别',
                                '来源',
                                '报名时间',
                                '状态'
                            ]);
                            
                            // 添加数据
                            Registration::with('activityDetail')->chunk(100, function ($records) use ($csv) {
                                $records->each(function ($record, $index) use ($csv) {
                                    fputcsv($csv, [
                                        $index + 1,
                                        $record->activityDetail->topic,
                                        $record->name,
                                        $record->phone,
                                        $record->organization,
                                        $record->grade,
                                        $record->gender === 'male' ? '男' : '女',
                                        $record->source,
                                        $record->created_at->format('Y-m-d H:i:s'),
                                        $record->status ? '有效' : '无效'
                                    ]);
                                });
                            });
                            
                            fclose($csv);
                        }, '报名数据.csv');
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRegistrations::route('/'),
            'create' => Pages\CreateRegistration::route('/create'),
            'edit' => Pages\EditRegistration::route('/{record}/edit'),
        ];
    }
}