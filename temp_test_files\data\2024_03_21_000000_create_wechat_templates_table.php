<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('wechat_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('模板名称');
            $table->string('template_id')->comment('模板ID');
            $table->string('title')->comment('模板标题');
            $table->text('content')->comment('模板内容');
            $table->json('data')->nullable()->comment('模板数据');
            $table->string('url')->nullable()->comment('跳转链接');
            $table->string('miniprogram')->nullable()->comment('小程序信息');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wechat_templates');
    }
}; 