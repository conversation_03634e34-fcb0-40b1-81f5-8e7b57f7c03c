<?php

namespace App\Filament\Resources\WechatAccountResource\Pages;

use App\Filament\Resources\WechatAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditWechatAccount extends EditRecord
{
    protected static string $resource = WechatAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('查看'),
            Actions\DeleteAction::make()
                ->label('删除'),
            Actions\ForceDeleteAction::make()
                ->label('强制删除'),
            Actions\RestoreAction::make()
                ->label('恢复'),
        ];
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}