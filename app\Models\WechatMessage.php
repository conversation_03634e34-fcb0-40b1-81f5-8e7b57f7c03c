<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class WechatMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'wechat_account_id',
        'openid',
        'msg_id',
        'msg_type',
        'content',
        'media_id',
        'pic_url',
        'format',
        'recognition',
        'thumb_media_id',
        'location_x',
        'location_y',
        'scale',
        'label',
        'title',
        'description',
        'url',
        'event',
        'event_key',
        'ticket',
        'latitude',
        'longitude',
        'precision',
        'status',
        'reply_content',
        'reply_type',
        'reply_media_id',
        'replied_at',
        'created_time',
    ];

    protected $casts = [
        'location_x' => 'float',
        'location_y' => 'float',
        'scale' => 'integer',
        'latitude' => 'float',
        'longitude' => 'float',
        'precision' => 'float',
        'replied_at' => 'datetime',
        'created_time' => 'datetime',
    ];

    /**
     * 消息类型常量
     */
    const MSG_TYPE_TEXT = 'text';
    const MSG_TYPE_IMAGE = 'image';
    const MSG_TYPE_VOICE = 'voice';
    const MSG_TYPE_VIDEO = 'video';
    const MSG_TYPE_SHORTVIDEO = 'shortvideo';
    const MSG_TYPE_LOCATION = 'location';
    const MSG_TYPE_LINK = 'link';
    const MSG_TYPE_EVENT = 'event';

    /**
     * 事件类型常量
     */
    const EVENT_SUBSCRIBE = 'subscribe';
    const EVENT_UNSUBSCRIBE = 'unsubscribe';
    const EVENT_SCAN = 'SCAN';
    const EVENT_LOCATION = 'LOCATION';
    const EVENT_CLICK = 'CLICK';
    const EVENT_VIEW = 'VIEW';

    /**
     * 处理状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSED = 'processed';
    const STATUS_FAILED = 'failed';

    /**
     * 回复类型常量
     */
    const REPLY_TYPE_TEXT = 'text';
    const REPLY_TYPE_IMAGE = 'image';
    const REPLY_TYPE_VOICE = 'voice';
    const REPLY_TYPE_VIDEO = 'video';
    const REPLY_TYPE_MUSIC = 'music';
    const REPLY_TYPE_NEWS = 'news';

    /**
     * 关联微信账号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAccount::class);
    }

    /**
     * 获取消息类型标签
     */
    public function getMsgTypeLabel(): string
    {
        return match($this->msg_type) {
            self::MSG_TYPE_TEXT => '文本消息',
            self::MSG_TYPE_IMAGE => '图片消息',
            self::MSG_TYPE_VOICE => '语音消息',
            self::MSG_TYPE_VIDEO => '视频消息',
            self::MSG_TYPE_SHORTVIDEO => '小视频消息',
            self::MSG_TYPE_LOCATION => '位置消息',
            self::MSG_TYPE_LINK => '链接消息',
            self::MSG_TYPE_EVENT => '事件消息',
            default => '未知类型'
        };
    }

    /**
     * 获取事件类型标签
     */
    public function getEventLabel(): string
    {
        if ($this->msg_type !== self::MSG_TYPE_EVENT) {
            return '';
        }

        return match($this->event) {
            self::EVENT_SUBSCRIBE => '关注事件',
            self::EVENT_UNSUBSCRIBE => '取消关注事件',
            self::EVENT_SCAN => '扫描二维码事件',
            self::EVENT_LOCATION => '上报地理位置事件',
            self::EVENT_CLICK => '菜单点击事件',
            self::EVENT_VIEW => '菜单跳转链接事件',
            default => '未知事件'
        };
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => '待处理',
            self::STATUS_PROCESSED => '已处理',
            self::STATUS_FAILED => '处理失败',
            default => '未知状态'
        };
    }

    /**
     * 检查是否为事件消息
     */
    public function isEvent(): bool
    {
        return $this->msg_type === self::MSG_TYPE_EVENT;
    }

    /**
     * 检查是否已回复
     */
    public function isReplied(): bool
    {
        return !is_null($this->replied_at);
    }

    /**
     * 标记为已回复
     */
    public function markAsReplied(string $replyType, string $replyContent, ?string $replyMediaId = null): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSED,
            'reply_type' => $replyType,
            'reply_content' => $replyContent,
            'reply_media_id' => $replyMediaId,
            'replied_at' => now(),
        ]);
    }

    /**
     * 标记为处理失败
     */
    public function markAsFailed(): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
        ]);
    }

    /**
     * 作用域：待处理的消息
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：已处理的消息
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', self::STATUS_PROCESSED);
    }

    /**
     * 作用域：事件消息
     */
    public function scopeEvents($query)
    {
        return $query->where('msg_type', self::MSG_TYPE_EVENT);
    }

    /**
     * 作用域：文本消息
     */
    public function scopeTextMessages($query)
    {
        return $query->where('msg_type', self::MSG_TYPE_TEXT);
    }
}