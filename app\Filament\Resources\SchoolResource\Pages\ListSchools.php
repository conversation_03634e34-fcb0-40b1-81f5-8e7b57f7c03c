<?php

namespace App\Filament\Resources\SchoolResource\Pages;

use App\Filament\Resources\SchoolResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use App\Models\School;

class ListSchools extends ListRecords
{
    protected static string $resource = SchoolResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('添加学校')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        $counts = $this->getSchoolCounts();
        
        return [
            'all' => Tab::make('全部')
                ->badge($counts['all'])
                ->badgeColor('primary'),
            
            'kindergarten' => Tab::make('幼儿园')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('school_type', 'kindergarten'))
                ->badge($counts['kindergarten'])
                ->badgeColor('warning'),
            
            'primary_secondary' => Tab::make('中小学')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('school_type', 'primary_secondary'))
                ->badge($counts['primary_secondary'])
                ->badgeColor('success'),
            
            'public' => Tab::make('公办学校')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('school_nature', 'public'))
                ->badge($counts['public'])
                ->badgeColor('info'),
            
            'private' => Tab::make('民办学校')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('school_nature', 'private'))
                ->badge($counts['private'])
                ->badgeColor('gray'),
            
            'bound' => Tab::make('已绑定')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('binding_status', 'bound'))
                ->badge($counts['bound'])
                ->badgeColor('success'),
            
            'unbound' => Tab::make('未绑定')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('binding_status', 'unbound'))
                ->badge($counts['unbound'])
                ->badgeColor('danger'),
        ];
    }

    /**
     * 获取学校统计数据
     */
    protected function getSchoolCounts(): array
    {
        return Cache::remember('schools_counts', 300, function () {
            $baseQuery = School::query();
            
            return [
                'all' => (clone $baseQuery)->count(),
                'kindergarten' => (clone $baseQuery)->where('school_type', 'kindergarten')->count(),
                'primary_secondary' => (clone $baseQuery)->where('school_type', 'primary_secondary')->count(),
                'public' => (clone $baseQuery)->where('school_nature', 'public')->count(),
                'private' => (clone $baseQuery)->where('school_nature', 'private')->count(),
                'bound' => (clone $baseQuery)->where('binding_status', 'bound')->count(),
                'unbound' => (clone $baseQuery)->where('binding_status', 'unbound')->count(),
            ];
        });
    }

    /**
     * 清除统计缓存
     */
    public function clearCountsCache(): void
    {
        Cache::forget('schools_counts');
        $this->clearCascadingFilterCaches();
    }

    /**
     * 清除级联筛选器缓存
     */
    private function clearCascadingFilterCaches(): void
    {
        // 清除省份缓存
        Cache::forget('schools_provinces');
        
        // 清除所有城市缓存
        if (function_exists('redis') && Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            try {
                $cacheKeys = Cache::getRedis()->keys('*schools_cities_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
                
                // 清除所有区县缓存
                $cacheKeys = Cache::getRedis()->keys('*schools_districts_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
                
                // 清除所有乡镇缓存
                $cacheKeys = Cache::getRedis()->keys('*schools_towns_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
            } catch (\Exception $e) {
                // 如果Redis不可用，使用备用清理方法
                $this->clearCascadingCachesFallback();
            }
        } else {
            $this->clearCascadingCachesFallback();
        }
    }

    /**
     * 备用缓存清理方法
     */
    private function clearCascadingCachesFallback(): void
    {
        // 获取所有省份代码并清除对应缓存
        $provinceCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'province')
            ->pluck('province_code')
            ->unique();
            
        foreach ($provinceCodes as $code) {
            Cache::forget("schools_cities_{$code}");
        }
        
        // 获取所有城市代码并清除对应缓存
        $cityCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'city')
            ->pluck('city_code')
            ->unique();
            
        foreach ($cityCodes as $code) {
            Cache::forget("schools_districts_{$code}");
        }
        
        // 获取所有区县代码并清除对应缓存
        $districtCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'district')
            ->pluck('district_code')
            ->unique();
            
        foreach ($districtCodes as $code) {
            Cache::forget("schools_towns_{$code}");
        }
    }

    /**
     * 页面挂载时清除缓存
     */
    public function mount(): void
    {
        parent::mount();
        
        // 如果有必要，可以在这里清除过期缓存
        // $this->clearCountsCache();
    }
}