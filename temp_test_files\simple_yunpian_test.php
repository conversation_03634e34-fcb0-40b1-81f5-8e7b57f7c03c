<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use Illuminate\Support\Facades\DB;

echo "=== 云片网字段映射简化测试 ===\n";

try {
    // 获取一个测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到测试注册记录\n";
        exit(1);
    }
    
    echo "测试注册记录ID: {$registration->id}\n";
    echo "用户姓名: {$registration->name}\n";
    
    $activityDetail = $registration->activityDetail;
    $activity = $activityDetail->activity;
    
    echo "\n=== 字段映射测试 ===\n";
    
    // 模拟buildTemplateData方法的逻辑
    // #name# - 用户姓名
    $name = !empty($registration->name) ? $registration->name : "用户";
    echo "#name#: '{$name}'\n";
    
    // #topic# - 活动主题 (优先使用新的topic字段)
    $topic = "";
    if (!empty($activityDetail->topic)) {
        $topic = $activityDetail->topic;  // 新增的直观字段
        echo "#topic# (来源: activity_details.topic): '{$topic}'\n";
    } elseif (!empty($activity->title)) {
        $topic = $activity->title;
        echo "#topic# (来源: activities.title): '{$topic}'\n";
    } elseif (!empty($activityDetail->theme)) {
        $topic = $activityDetail->theme;  // 兼容旧字段
        echo "#topic# (来源: activity_details.theme): '{$topic}'\n";
    } else {
        $topic = "活动通知";
        echo "#topic# (默认值): '{$topic}'\n";
    }
    
    // #time# - 活动时间
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        try {
            $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
            echo "#time#: '{$time}'\n";
        } catch (Exception $e) {
            $time = "待定时间";
            echo "#time# (格式化失败): '{$time}'\n";
        }
    } else {
        $time = "待定时间";
        echo "#time# (默认值): '{$time}'\n";
    }
    
    // #address# - 活动地址
    $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
    echo "#address#: '{$address}'\n";
    
    // #obj# - 目标对象 (优先使用新的target_audience字段)
    $obj = "";
    if (!empty($activityDetail->target_audience)) {
        $obj = $activityDetail->target_audience;  // 新增的直观字段
        echo "#obj# (来源: activity_details.target_audience): '{$obj}'\n";
    } elseif (!empty($activityDetail->target)) {
        $obj = $activityDetail->target;  // 兼容旧字段
        echo "#obj# (来源: activity_details.target): '{$obj}'\n";
    } else {
        $obj = "全体人员";
        echo "#obj# (默认值): '{$obj}'\n";
    }
    
    $templateData = [
        "name" => $name,
        "topic" => $topic,
        "time" => $time,
        "address" => $address,
        "obj" => $obj
    ];
    
    echo "\n=== 最终模板数据 ===\n";
    foreach ($templateData as $key => $value) {
        echo "#{$key}#: '{$value}'\n";
    }
    
    // 检查字段完整性
    $defaultValues = ['待定时间', '待定地点', '全体人员', '活动通知', '用户'];
    $hasDefaults = false;
    foreach ($templateData as $key => $value) {
        if (in_array($value, $defaultValues)) {
            $hasDefaults = true;
            break;
        }
    }
    
    echo "\n=== 结果评估 ===\n";
    if (!$hasDefaults) {
        echo "✓ 所有字段都有真实数据，云片网字段映射修复成功！\n";
    } else {
        echo "⚠ 部分字段使用默认值，但字段映射逻辑正确\n";
    }
    
} catch (Exception $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
