<?php

namespace App\Services;

use App\Models\WechatAccount;
use App\Models\WechatUser;
use EasyWeChat\Factory;
use Illuminate\Support\Facades\Log;
use Exception;
use Carbon\Carbon;

class WechatUserService
{
    /**
     * 获取用户信息
     */
    public function getUserInfo(WechatAccount $account, string $openid): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            $userInfo = $user->get($openid);

            if (isset($userInfo['errcode']) && $userInfo['errcode'] !== 0) {
                Log::error('获取微信用户信息失败', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'error' => $userInfo,
                ]);

                return [
                    'success' => false,
                    'message' => '获取用户信息失败: ' . ($userInfo['errmsg'] ?? '未知错误'),
                    'error_code' => $userInfo['errcode'] ?? -1,
                ];
            }

            // 保存或更新用户信息到数据库
            $this->saveUserToDatabase($account, $userInfo);

            Log::info('获取微信用户信息成功', [
                'account_id' => $account->id,
                'openid' => $openid,
            ]);

            return [
                'success' => true,
                'message' => '获取用户信息成功',
                'data' => $userInfo,
            ];
        } catch (Exception $e) {
            Log::error('获取微信用户信息异常', [
                'account_id' => $account->id,
                'openid' => $openid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取用户信息异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 批量获取用户信息
     */
    public function batchGetUserInfo(WechatAccount $account, array $openids): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            // 微信API限制每次最多100个用户
            $chunks = array_chunk($openids, 100);
            $allUsers = [];
            $errors = [];

            foreach ($chunks as $chunk) {
                $userList = [];
                foreach ($chunk as $openid) {
                    $userList[] = ['openid' => $openid, 'lang' => 'zh_CN'];
                }

                $result = $user->select($userList);

                if (isset($result['user_info_list'])) {
                    foreach ($result['user_info_list'] as $userInfo) {
                        if (!isset($userInfo['errcode'])) {
                            // 保存用户信息到数据库
                            $this->saveUserToDatabase($account, $userInfo);
                            $allUsers[] = $userInfo;
                        } else {
                            $errors[] = [
                                'openid' => $userInfo['openid'] ?? 'unknown',
                                'error' => $userInfo['errmsg'] ?? '未知错误',
                            ];
                        }
                    }
                } else {
                    $errors[] = [
                        'chunk' => $chunk,
                        'error' => $result['errmsg'] ?? '批量获取失败',
                    ];
                }
            }

            Log::info('批量获取微信用户信息完成', [
                'account_id' => $account->id,
                'total_requested' => count($openids),
                'success_count' => count($allUsers),
                'error_count' => count($errors),
            ]);

            return [
                'success' => true,
                'message' => '批量获取用户信息完成',
                'data' => [
                    'users' => $allUsers,
                    'errors' => $errors,
                    'total' => count($openids),
                    'success_count' => count($allUsers),
                    'error_count' => count($errors),
                ],
            ];
        } catch (Exception $e) {
            Log::error('批量获取微信用户信息异常', [
                'account_id' => $account->id,
                'openids_count' => count($openids),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '批量获取用户信息异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取用户列表
     */
    public function getUserList(WechatAccount $account, string $nextOpenid = null): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            $result = $user->list($nextOpenid);

            if (isset($result['errcode']) && $result['errcode'] !== 0) {
                Log::error('获取微信用户列表失败', [
                    'account_id' => $account->id,
                    'next_openid' => $nextOpenid,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '获取用户列表失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }

            Log::info('获取微信用户列表成功', [
                'account_id' => $account->id,
                'total' => $result['total'] ?? 0,
                'count' => $result['count'] ?? 0,
            ]);

            return [
                'success' => true,
                'message' => '获取用户列表成功',
                'data' => $result,
            ];
        } catch (Exception $e) {
            Log::error('获取微信用户列表异常', [
                'account_id' => $account->id,
                'next_openid' => $nextOpenid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取用户列表异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 同步所有用户信息
     */
    public function syncAllUsers(WechatAccount $account): array
    {
        try {
            $allOpenids = [];
            $nextOpenid = null;
            $totalCount = 0;

            // 获取所有用户的openid
            do {
                $result = $this->getUserList($account, $nextOpenid);

                if (!$result['success']) {
                    return $result;
                }

                $data = $result['data'];
                $totalCount = $data['total'] ?? 0;

                if (isset($data['data']['openid'])) {
                    $allOpenids = array_merge($allOpenids, $data['data']['openid']);
                }

                $nextOpenid = $data['next_openid'] ?? null;
            } while ($nextOpenid);

            if (empty($allOpenids)) {
                return [
                    'success' => true,
                    'message' => '没有用户需要同步',
                    'data' => [
                        'total' => 0,
                        'synced' => 0,
                        'errors' => 0,
                    ],
                ];
            }

            // 批量获取用户详细信息
            $batchResult = $this->batchGetUserInfo($account, $allOpenids);

            if (!$batchResult['success']) {
                return $batchResult;
            }

            $syncData = $batchResult['data'];

            Log::info('同步微信用户信息完成', [
                'account_id' => $account->id,
                'total_users' => $totalCount,
                'openids_found' => count($allOpenids),
                'synced_users' => $syncData['success_count'],
                'errors' => $syncData['error_count'],
            ]);

            return [
                'success' => true,
                'message' => '用户信息同步完成',
                'data' => [
                    'total' => $totalCount,
                    'synced' => $syncData['success_count'],
                    'errors' => $syncData['error_count'],
                    'error_details' => $syncData['errors'],
                ],
            ];
        } catch (Exception $e) {
            Log::error('同步微信用户信息异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '同步用户信息异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 保存用户信息到数据库
     */
    private function saveUserToDatabase(WechatAccount $account, array $userInfo): void
    {
        try {
            $data = [
                'wechat_account_id' => $account->id,
                'openid' => $userInfo['openid'],
                'nickname' => $userInfo['nickname'] ?? '',
                'sex' => $userInfo['sex'] ?? 0,
                'city' => $userInfo['city'] ?? '',
                'country' => $userInfo['country'] ?? '',
                'province' => $userInfo['province'] ?? '',
                'language' => $userInfo['language'] ?? '',
                'headimgurl' => $userInfo['headimgurl'] ?? '',
                'subscribe' => $userInfo['subscribe'] ?? 0,
                'subscribe_time' => isset($userInfo['subscribe_time']) ? 
                    Carbon::createFromTimestamp($userInfo['subscribe_time']) : null,
                'unionid' => $userInfo['unionid'] ?? null,
                'remark' => $userInfo['remark'] ?? '',
                'groupid' => $userInfo['groupid'] ?? 0,
                'tagid_list' => isset($userInfo['tagid_list']) ? 
                    json_encode($userInfo['tagid_list']) : null,
                'subscribe_scene' => $userInfo['subscribe_scene'] ?? '',
                'qr_scene' => $userInfo['qr_scene'] ?? 0,
                'qr_scene_str' => $userInfo['qr_scene_str'] ?? '',
                'last_sync_at' => now(),
            ];

            WechatUser::updateOrCreate(
                [
                    'wechat_account_id' => $account->id,
                    'openid' => $userInfo['openid'],
                ],
                $data
            );
        } catch (Exception $e) {
            Log::error('保存微信用户信息到数据库失败', [
                'account_id' => $account->id,
                'openid' => $userInfo['openid'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 设置用户备注名
     */
    public function updateUserRemark(WechatAccount $account, string $openid, string $remark): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            $result = $user->remark($openid, $remark);

            if ($result['errcode'] === 0) {
                // 更新数据库中的备注
                WechatUser::where('wechat_account_id', $account->id)
                    ->where('openid', $openid)
                    ->update(['remark' => $remark]);

                Log::info('设置微信用户备注成功', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'remark' => $remark,
                ]);

                return [
                    'success' => true,
                    'message' => '设置用户备注成功',
                ];
            } else {
                Log::error('设置微信用户备注失败', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'remark' => $remark,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '设置用户备注失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('设置微信用户备注异常', [
                'account_id' => $account->id,
                'openid' => $openid,
                'remark' => $remark,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '设置用户备注异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取用户分组列表
     */
    public function getUserGroups(WechatAccount $account): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $userGroup = $app->user_group;

            $result = $userGroup->list();

            if (isset($result['groups'])) {
                Log::info('获取微信用户分组成功', [
                    'account_id' => $account->id,
                    'groups_count' => count($result['groups']),
                ]);

                return [
                    'success' => true,
                    'message' => '获取用户分组成功',
                    'data' => $result['groups'],
                ];
            } else {
                Log::error('获取微信用户分组失败', [
                    'account_id' => $account->id,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '获取用户分组失败',
                    'error' => $result,
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信用户分组异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取用户分组异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 移动用户到指定分组
     */
    public function moveUserToGroup(WechatAccount $account, string $openid, int $groupId): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $userGroup = $app->user_group;

            $result = $userGroup->moveUser($openid, $groupId);

            if ($result['errcode'] === 0) {
                // 更新数据库中的分组信息
                WechatUser::where('wechat_account_id', $account->id)
                    ->where('openid', $openid)
                    ->update(['groupid' => $groupId]);

                Log::info('移动微信用户到分组成功', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'group_id' => $groupId,
                ]);

                return [
                    'success' => true,
                    'message' => '移动用户到分组成功',
                ];
            } else {
                Log::error('移动微信用户到分组失败', [
                    'account_id' => $account->id,
                    'openid' => $openid,
                    'group_id' => $groupId,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '移动用户到分组失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('移动微信用户到分组异常', [
                'account_id' => $account->id,
                'openid' => $openid,
                'group_id' => $groupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '移动用户到分组异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取黑名单用户列表
     */
    public function getBlacklist(WechatAccount $account, string $beginOpenid = null): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            $result = $user->blacklist($beginOpenid);

            if (isset($result['data'])) {
                Log::info('获取微信黑名单成功', [
                    'account_id' => $account->id,
                    'total' => $result['total'] ?? 0,
                    'count' => $result['count'] ?? 0,
                ]);

                return [
                    'success' => true,
                    'message' => '获取黑名单成功',
                    'data' => $result,
                ];
            } else {
                Log::error('获取微信黑名单失败', [
                    'account_id' => $account->id,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '获取黑名单失败',
                    'error' => $result,
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信黑名单异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取黑名单异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 拉黑用户
     */
    public function blockUser(WechatAccount $account, array $openids): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            $result = $user->block($openids);

            if ($result['errcode'] === 0) {
                Log::info('拉黑微信用户成功', [
                    'account_id' => $account->id,
                    'openids' => $openids,
                ]);

                return [
                    'success' => true,
                    'message' => '拉黑用户成功',
                ];
            } else {
                Log::error('拉黑微信用户失败', [
                    'account_id' => $account->id,
                    'openids' => $openids,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '拉黑用户失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('拉黑微信用户异常', [
                'account_id' => $account->id,
                'openids' => $openids,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '拉黑用户异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 取消拉黑用户
     */
    public function unblockUser(WechatAccount $account, array $openids): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $user = $app->user;

            $result = $user->unblock($openids);

            if ($result['errcode'] === 0) {
                Log::info('取消拉黑微信用户成功', [
                    'account_id' => $account->id,
                    'openids' => $openids,
                ]);

                return [
                    'success' => true,
                    'message' => '取消拉黑用户成功',
                ];
            } else {
                Log::error('取消拉黑微信用户失败', [
                    'account_id' => $account->id,
                    'openids' => $openids,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '取消拉黑用户失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('取消拉黑微信用户异常', [
                'account_id' => $account->id,
                'openids' => $openids,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '取消拉黑用户异常: ' . $e->getMessage(),
            ];
        }
    }
}