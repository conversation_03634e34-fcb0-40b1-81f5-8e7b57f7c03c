<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DatabaseBackupResource\Pages;
use App\Models\DatabaseBackup;
use App\Services\DatabaseBackupService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Storage;

class DatabaseBackupResource extends Resource
{
    protected static ?string $model = DatabaseBackup::class;

    protected static ?string $navigationIcon = 'heroicon-o-server-stack';

    protected static ?string $navigationGroup = '数据备份';

    protected static ?string $navigationLabel = '备份记录';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('备份信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('备份名称')
                            ->required()
                            ->maxLength(255)
                            ->default(fn () => 'backup_' . now()->format('Y-m-d_H-i-s')),
                        Forms\Components\Select::make('backup_type')
                            ->label('备份类型')
                            ->options(DatabaseBackup::getBackupTypes())
                            ->required()
                            ->default(DatabaseBackup::TYPE_FULL),
                        Forms\Components\Textarea::make('description')
                            ->label('备份描述')
                            ->maxLength(500)
                            ->rows(3)
                            ->placeholder('请输入备份描述信息'),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('备份名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('backup_type')
                    ->label('备份类型')
                    ->badge()
                    ->color(fn (DatabaseBackup $record): string => $record->backup_type_color)
                    ->formatStateUsing(fn (string $state): string => DatabaseBackup::getBackupTypes()[$state] ?? $state),
                Tables\Columns\TextColumn::make('status')
                    ->label('状态')
                    ->badge()
                    ->color(fn (DatabaseBackup $record): string => $record->status_color)
                    ->formatStateUsing(fn (string $state): string => DatabaseBackup::getStatuses()[$state] ?? $state),
                Tables\Columns\TextColumn::make('formatted_file_size')
                    ->label('文件大小')
                    ->sortable('file_size'),
                Tables\Columns\TextColumn::make('duration')
                    ->label('耗时')
                    ->placeholder('-'),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('创建者')
                    ->default('系统'),
                Tables\Columns\TextColumn::make('backup_started_at')
                    ->label('开始时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('backup_completed_at')
                    ->label('完成时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('backup_type')
                    ->label('备份类型')
                    ->options(DatabaseBackup::getBackupTypes()),
                Tables\Filters\SelectFilter::make('status')
                    ->label('状态')
                    ->options(DatabaseBackup::getStatuses()),
            ])
            ->actions([
                Action::make('download')
                    ->label('下载')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function (DatabaseBackup $record) {
                        if (!$record->fileExists()) {
                            Notification::make()
                                ->title('文件不存在')
                                ->body('备份文件已被删除或移动')
                                ->danger()
                                ->send();
                            return;
                        }

                        return Storage::disk('local')->download($record->file_path, $record->name . '.sql');
                    })
                    ->visible(fn (DatabaseBackup $record): bool =>
                        $record->status === DatabaseBackup::STATUS_COMPLETED && $record->fileExists()
                    ),
                Action::make('retry')
                    ->label('重试')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->action(function (DatabaseBackup $record) {
                        $backupService = app(DatabaseBackupService::class);

                        try {
                            $backupService->executeBackup($record);

                            Notification::make()
                                ->title('备份重试成功')
                                ->body('备份任务已重新开始执行')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('备份重试失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (DatabaseBackup $record): bool =>
                        $record->status === DatabaseBackup::STATUS_FAILED
                    ),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (DatabaseBackup $record): bool =>
                        $record->status === DatabaseBackup::STATUS_PENDING
                    ),
                Tables\Actions\DeleteAction::make()
                    ->before(function (DatabaseBackup $record) {
                        // 删除备份文件
                        $record->deleteFile();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (Collection $records) {
                            // 删除所有备份文件
                            foreach ($records as $record) {
                                $record->deleteFile();
                            }
                        }),
                    BulkAction::make('cleanup_files')
                        ->label('清理文件')
                        ->icon('heroicon-o-trash')
                        ->color('warning')
                        ->action(function (Collection $records) {
                            $deletedCount = 0;
                            foreach ($records as $record) {
                                if ($record->deleteFile()) {
                                    $deletedCount++;
                                }
                            }

                            Notification::make()
                                ->title('文件清理完成')
                                ->body("已清理 {$deletedCount} 个备份文件")
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->modalHeading('清理备份文件')
                        ->modalDescription('确定要清理选中的备份文件吗？这将删除物理文件但保留记录。')
                        ->modalSubmitActionLabel('确认清理'),
                ]),
            ])

            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDatabaseBackups::route('/'),
            'create' => Pages\CreateDatabaseBackup::route('/create'),
            'view' => Pages\ViewDatabaseBackup::route('/{record}'),
            'edit' => Pages\EditDatabaseBackup::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '备份记录';
    }

    public static function getModelLabel(): string
    {
        return '数据库备份';
    }

    public static function getPluralModelLabel(): string
    {
        return '数据库备份';
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::completed()->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
