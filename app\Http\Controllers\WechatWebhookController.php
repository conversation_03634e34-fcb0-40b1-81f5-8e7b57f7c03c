<?php

namespace App\Http\Controllers;

use App\Models\WechatAccount;
use App\Services\WechatMessageService;
use EasyWeChat\Factory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Exception;

class WechatWebhookController extends Controller
{
    protected WechatMessageService $messageService;

    public function __construct(WechatMessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    /**
     * 处理微信服务器验证和消息接收
     */
    public function handle(Request $request, string $accountId)
    {
        try {
            // 查找微信账号
            $account = WechatAccount::where('id', $accountId)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                Log::error('微信账号不存在或未启用', [
                    'account_id' => $accountId,
                    'request_data' => $request->all()
                ]);
                return response('账号不存在或未启用', 404);
            }

            // 创建EasyWeChat应用实例
            $app = Factory::officialAccount($account->getWechatConfig());
            $server = $app->server;

            // 设置消息处理器
            $this->setMessageHandlers($server, $account);

            // 处理请求
            $response = $server->serve();

            return response($response->getContent())
                ->withHeaders($response->getHeaders());

        } catch (Exception $e) {
            Log::error('微信Webhook处理异常', [
                'account_id' => $accountId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response('服务器错误', 500);
        }
    }

    /**
     * 设置消息处理器
     */
    private function setMessageHandlers($server, WechatAccount $account): void
    {
        // 处理文本消息
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'text') {
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理图片消息
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'image') {
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理语音消息
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'voice') {
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理视频消息
        $server->push(function ($message) use ($account) {
            if (in_array($message['MsgType'], ['video', 'shortvideo'])) {
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理位置消息
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'location') {
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理链接消息
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'link') {
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理关注事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && $message['Event'] === 'subscribe') {
                Log::info('用户关注公众号', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'event_key' => $message['EventKey'] ?? null
                ]);
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理取消关注事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && $message['Event'] === 'unsubscribe') {
                Log::info('用户取消关注公众号', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName']
                ]);
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理菜单点击事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && $message['Event'] === 'CLICK') {
                Log::info('用户点击菜单', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'event_key' => $message['EventKey']
                ]);
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理菜单跳转事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && $message['Event'] === 'VIEW') {
                Log::info('用户点击菜单跳转', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'event_key' => $message['EventKey']
                ]);
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理扫码事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && in_array($message['Event'], ['SCAN', 'subscribe'])) {
                if (isset($message['EventKey']) && !empty($message['EventKey'])) {
                    Log::info('用户扫码', [
                        'account_id' => $account->id,
                        'openid' => $message['FromUserName'],
                        'event' => $message['Event'],
                        'event_key' => $message['EventKey'],
                        'ticket' => $message['Ticket'] ?? null
                    ]);
                }
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理地理位置事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && $message['Event'] === 'LOCATION') {
                Log::info('用户上报地理位置', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'latitude' => $message['Latitude'],
                    'longitude' => $message['Longitude'],
                    'precision' => $message['Precision']
                ]);
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 处理扫码推事件
        $server->push(function ($message) use ($account) {
            if ($message['MsgType'] === 'event' && in_array($message['Event'], [
                'scancode_push', 'scancode_waitmsg',
                'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin',
                'location_select'
            ])) {
                Log::info('用户触发扫码推事件', [
                    'account_id' => $account->id,
                    'openid' => $message['FromUserName'],
                    'event' => $message['Event'],
                    'event_key' => $message['EventKey']
                ]);
                return $this->messageService->handleMessage($account, $message);
            }
        });

        // 默认消息处理器
        $server->push(function ($message) use ($account) {
            Log::info('收到未处理的消息类型', [
                'account_id' => $account->id,
                'message' => $message
            ]);
            return $this->messageService->handleMessage($account, $message);
        });
    }

    /**
     * 获取微信服务器IP列表（用于安全验证）
     */
    public function getWechatIps(Request $request, string $accountId)
    {
        try {
            $account = WechatAccount::find($accountId);
            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => '账号不存在'
                ], 404);
            }

            $app = Factory::officialAccount($account->getWechatConfig());
            $result = $app->base->getValidIps();

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('获取微信服务器IP失败', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试Webhook连接
     */
    public function testWebhook(Request $request, string $accountId)
    {
        try {
            $account = WechatAccount::find($accountId);
            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => '账号不存在'
                ], 404);
            }

            // 模拟一个测试消息
            $testMessage = [
                'ToUserName' => $account->app_id,
                'FromUserName' => 'test_openid',
                'CreateTime' => time(),
                'MsgType' => 'text',
                'Content' => '测试消息',
                'MsgId' => 'test_' . time()
            ];

            $reply = $this->messageService->handleMessage($account, $testMessage);

            return response()->json([
                'success' => true,
                'message' => 'Webhook测试成功',
                'reply' => $reply
            ]);
        } catch (Exception $e) {
            Log::error('Webhook测试失败', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '测试失败：' . $e->getMessage()
            ], 500);
        }
    }
}