<?php

namespace App\Services;

use App\Models\WechatAccount;
use App\Models\WechatMedia;
use EasyWeChat\Factory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;
use Carbon\Carbon;

class WechatMediaService
{
    /**
     * 上传临时素材
     */
    public function uploadTempMedia(WechatAccount $account, string $type, string $filePath): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $media = $app->media;

            // 检查文件是否存在
            if (!file_exists($filePath)) {
                return [
                    'success' => false,
                    'message' => '文件不存在: ' . $filePath,
                ];
            }

            // 验证媒体类型
            if (!in_array($type, ['image', 'voice', 'video', 'thumb'])) {
                return [
                    'success' => false,
                    'message' => '不支持的媒体类型: ' . $type,
                ];
            }

            $result = $media->upload($type, $filePath);

            if (isset($result['media_id'])) {
                // 保存媒体信息到数据库
                $mediaRecord = $this->saveMediaToDatabase($account, [
                    'type' => $type,
                    'media_id' => $result['media_id'],
                    'file_path' => $filePath,
                    'is_permanent' => false,
                    'created_at' => $result['created_at'] ?? time(),
                ]);

                Log::info('上传微信临时素材成功', [
                    'account_id' => $account->id,
                    'type' => $type,
                    'media_id' => $result['media_id'],
                    'file_path' => $filePath,
                ]);

                return [
                    'success' => true,
                    'message' => '上传临时素材成功',
                    'data' => array_merge($result, ['record_id' => $mediaRecord->id]),
                ];
            } else {
                Log::error('上传微信临时素材失败', [
                    'account_id' => $account->id,
                    'type' => $type,
                    'file_path' => $filePath,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '上传临时素材失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('上传微信临时素材异常', [
                'account_id' => $account->id,
                'type' => $type,
                'file_path' => $filePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '上传临时素材异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 上传永久素材
     */
    public function uploadPermanentMedia(WechatAccount $account, string $type, string $filePath, array $description = []): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            // 检查文件是否存在
            if (!file_exists($filePath)) {
                return [
                    'success' => false,
                    'message' => '文件不存在: ' . $filePath,
                ];
            }

            // 验证媒体类型
            if (!in_array($type, ['image', 'voice', 'video', 'thumb'])) {
                return [
                    'success' => false,
                    'message' => '不支持的媒体类型: ' . $type,
                ];
            }

            // 上传永久素材
            if ($type === 'video' && !empty($description)) {
                $result = $material->uploadVideo($filePath, $description['title'] ?? '', $description['introduction'] ?? '');
            } else {
                $result = $material->upload($type, $filePath);
            }

            if (isset($result['media_id'])) {
                // 保存媒体信息到数据库
                $mediaRecord = $this->saveMediaToDatabase($account, [
                    'type' => $type,
                    'media_id' => $result['media_id'],
                    'file_path' => $filePath,
                    'is_permanent' => true,
                    'url' => $result['url'] ?? null,
                    'title' => $description['title'] ?? null,
                    'introduction' => $description['introduction'] ?? null,
                ]);

                Log::info('上传微信永久素材成功', [
                    'account_id' => $account->id,
                    'type' => $type,
                    'media_id' => $result['media_id'],
                    'file_path' => $filePath,
                ]);

                return [
                    'success' => true,
                    'message' => '上传永久素材成功',
                    'data' => array_merge($result, ['record_id' => $mediaRecord->id]),
                ];
            } else {
                Log::error('上传微信永久素材失败', [
                    'account_id' => $account->id,
                    'type' => $type,
                    'file_path' => $filePath,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '上传永久素材失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('上传微信永久素材异常', [
                'account_id' => $account->id,
                'type' => $type,
                'file_path' => $filePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '上传永久素材异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取临时素材
     */
    public function getTempMedia(WechatAccount $account, string $mediaId, string $savePath = null): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $media = $app->media;

            $stream = $media->get($mediaId);

            if ($stream instanceof \Psr\Http\Message\ResponseInterface) {
                $content = $stream->getBody()->getContents();

                if ($savePath) {
                    // 确保目录存在
                    $directory = dirname($savePath);
                    if (!is_dir($directory)) {
                        mkdir($directory, 0755, true);
                    }

                    file_put_contents($savePath, $content);
                }

                Log::info('获取微信临时素材成功', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'save_path' => $savePath,
                    'content_length' => strlen($content),
                ]);

                return [
                    'success' => true,
                    'message' => '获取临时素材成功',
                    'data' => [
                        'content' => $content,
                        'save_path' => $savePath,
                        'content_length' => strlen($content),
                    ],
                ];
            } else {
                Log::error('获取微信临时素材失败', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'response' => $stream,
                ]);

                return [
                    'success' => false,
                    'message' => '获取临时素材失败',
                    'error' => $stream,
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信临时素材异常', [
                'account_id' => $account->id,
                'media_id' => $mediaId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取临时素材异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取永久素材
     */
    public function getPermanentMedia(WechatAccount $account, string $mediaId, string $savePath = null): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            $result = $material->get($mediaId);

            if ($result instanceof \Psr\Http\Message\ResponseInterface) {
                $content = $result->getBody()->getContents();

                if ($savePath) {
                    // 确保目录存在
                    $directory = dirname($savePath);
                    if (!is_dir($directory)) {
                        mkdir($directory, 0755, true);
                    }

                    file_put_contents($savePath, $content);
                }

                Log::info('获取微信永久素材成功', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'save_path' => $savePath,
                    'content_length' => strlen($content),
                ]);

                return [
                    'success' => true,
                    'message' => '获取永久素材成功',
                    'data' => [
                        'content' => $content,
                        'save_path' => $savePath,
                        'content_length' => strlen($content),
                    ],
                ];
            } elseif (is_array($result) && isset($result['news_item'])) {
                // 图文消息
                Log::info('获取微信永久图文素材成功', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'news_count' => count($result['news_item']),
                ]);

                return [
                    'success' => true,
                    'message' => '获取永久图文素材成功',
                    'data' => $result,
                ];
            } else {
                Log::error('获取微信永久素材失败', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'response' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '获取永久素材失败',
                    'error' => $result,
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信永久素材异常', [
                'account_id' => $account->id,
                'media_id' => $mediaId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取永久素材异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 删除永久素材
     */
    public function deletePermanentMedia(WechatAccount $account, string $mediaId): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            $result = $material->delete($mediaId);

            if ($result['errcode'] === 0) {
                // 删除数据库记录
                WechatMedia::where('wechat_account_id', $account->id)
                    ->where('media_id', $mediaId)
                    ->delete();

                Log::info('删除微信永久素材成功', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                ]);

                return [
                    'success' => true,
                    'message' => '删除永久素材成功',
                ];
            } else {
                Log::error('删除微信永久素材失败', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '删除永久素材失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('删除微信永久素材异常', [
                'account_id' => $account->id,
                'media_id' => $mediaId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '删除永久素材异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取素材总数
     */
    public function getMediaCount(WechatAccount $account): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            $result = $material->stats();

            if (isset($result['voice_count'])) {
                Log::info('获取微信素材总数成功', [
                    'account_id' => $account->id,
                    'stats' => $result,
                ]);

                return [
                    'success' => true,
                    'message' => '获取素材总数成功',
                    'data' => $result,
                ];
            } else {
                Log::error('获取微信素材总数失败', [
                    'account_id' => $account->id,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '获取素材总数失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信素材总数异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取素材总数异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取素材列表
     */
    public function getMediaList(WechatAccount $account, string $type, int $offset = 0, int $count = 20): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            // 验证媒体类型
            if (!in_array($type, ['image', 'voice', 'video', 'news'])) {
                return [
                    'success' => false,
                    'message' => '不支持的媒体类型: ' . $type,
                ];
            }

            $result = $material->list($type, $offset, $count);

            if (isset($result['item'])) {
                Log::info('获取微信素材列表成功', [
                    'account_id' => $account->id,
                    'type' => $type,
                    'offset' => $offset,
                    'count' => $count,
                    'total_count' => $result['total_count'] ?? 0,
                    'item_count' => $result['item_count'] ?? 0,
                ]);

                return [
                    'success' => true,
                    'message' => '获取素材列表成功',
                    'data' => $result,
                ];
            } else {
                Log::error('获取微信素材列表失败', [
                    'account_id' => $account->id,
                    'type' => $type,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '获取素材列表失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信素材列表异常', [
                'account_id' => $account->id,
                'type' => $type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '获取素材列表异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 上传图文消息
     */
    public function uploadNews(WechatAccount $account, array $articles): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            $result = $material->uploadArticle($articles);

            if (isset($result['media_id'])) {
                // 保存媒体信息到数据库
                $mediaRecord = $this->saveMediaToDatabase($account, [
                    'type' => 'news',
                    'media_id' => $result['media_id'],
                    'is_permanent' => true,
                    'title' => $articles[0]['title'] ?? '图文消息',
                    'content' => json_encode($articles),
                ]);

                Log::info('上传微信图文消息成功', [
                    'account_id' => $account->id,
                    'media_id' => $result['media_id'],
                    'articles_count' => count($articles),
                ]);

                return [
                    'success' => true,
                    'message' => '上传图文消息成功',
                    'data' => array_merge($result, ['record_id' => $mediaRecord->id]),
                ];
            } else {
                Log::error('上传微信图文消息失败', [
                    'account_id' => $account->id,
                    'articles_count' => count($articles),
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '上传图文消息失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('上传微信图文消息异常', [
                'account_id' => $account->id,
                'articles_count' => count($articles),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '上传图文消息异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 更新图文消息
     */
    public function updateNews(WechatAccount $account, string $mediaId, int $index, array $article): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $material = $app->material;

            $result = $material->updateArticle($mediaId, $article, $index);

            if ($result['errcode'] === 0) {
                Log::info('更新微信图文消息成功', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'index' => $index,
                ]);

                return [
                    'success' => true,
                    'message' => '更新图文消息成功',
                ];
            } else {
                Log::error('更新微信图文消息失败', [
                    'account_id' => $account->id,
                    'media_id' => $mediaId,
                    'index' => $index,
                    'error' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => '更新图文消息失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'error_code' => $result['errcode'] ?? -1,
                ];
            }
        } catch (Exception $e) {
            Log::error('更新微信图文消息异常', [
                'account_id' => $account->id,
                'media_id' => $mediaId,
                'index' => $index,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => '更新图文消息异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 保存媒体信息到数据库
     */
    private function saveMediaToDatabase(WechatAccount $account, array $mediaData): WechatMedia
    {
        return WechatMedia::create([
            'wechat_account_id' => $account->id,
            'type' => $mediaData['type'],
            'media_id' => $mediaData['media_id'],
            'file_path' => $mediaData['file_path'] ?? null,
            'url' => $mediaData['url'] ?? null,
            'title' => $mediaData['title'] ?? null,
            'introduction' => $mediaData['introduction'] ?? null,
            'content' => $mediaData['content'] ?? null,
            'is_permanent' => $mediaData['is_permanent'] ?? false,
            'expires_at' => isset($mediaData['created_at']) && !($mediaData['is_permanent'] ?? false) ?
                Carbon::createFromTimestamp($mediaData['created_at'])->addDays(3) : null,
        ]);
    }

    /**
     * 清理过期的临时素材记录
     */
    public function cleanExpiredTempMedia(): int
    {
        $count = WechatMedia::where('is_permanent', false)
            ->where('expires_at', '<', now())
            ->delete();

        Log::info('清理过期临时素材记录', [
            'deleted_count' => $count,
        ]);

        return $count;
    }

    /**
     * 从本地文件上传到微信
     */
    public function uploadFromStorage(WechatAccount $account, string $storagePath, string $type, bool $permanent = false, array $description = []): array
    {
        try {
            // 检查文件是否存在于存储中
            if (!Storage::exists($storagePath)) {
                return [
                    'success' => false,
                    'message' => '存储文件不存在: ' . $storagePath,
                ];
            }

            // 获取文件的真实路径
            $realPath = Storage::path($storagePath);

            if ($permanent) {
                return $this->uploadPermanentMedia($account, $type, $realPath, $description);
            } else {
                return $this->uploadTempMedia($account, $type, $realPath);
            }
        } catch (Exception $e) {
            Log::error('从存储上传媒体文件异常', [
                'account_id' => $account->id,
                'storage_path' => $storagePath,
                'type' => $type,
                'permanent' => $permanent,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '从存储上传媒体文件异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 验证媒体文件
     */
    public function validateMediaFile(string $filePath, string $type): array
    {
        $errors = [];

        if (!file_exists($filePath)) {
            $errors[] = '文件不存在';
            return ['valid' => false, 'errors' => $errors];
        }

        $fileSize = filesize($filePath);
        $fileInfo = pathinfo($filePath);
        $extension = strtolower($fileInfo['extension'] ?? '');

        // 根据类型验证文件
        switch ($type) {
            case 'image':
                if (!in_array($extension, ['jpg', 'jpeg', 'png'])) {
                    $errors[] = '图片格式只支持 JPG、PNG';
                }
                if ($fileSize > 10 * 1024 * 1024) { // 10MB
                    $errors[] = '图片文件大小不能超过 10MB';
                }
                break;

            case 'voice':
                if (!in_array($extension, ['mp3', 'wma', 'wav', 'amr'])) {
                    $errors[] = '语音格式只支持 MP3、WMA、WAV、AMR';
                }
                if ($fileSize > 5 * 1024 * 1024) { // 5MB
                    $errors[] = '语音文件大小不能超过 5MB';
                }
                break;

            case 'video':
                if (!in_array($extension, ['mp4'])) {
                    $errors[] = '视频格式只支持 MP4';
                }
                if ($fileSize > 20 * 1024 * 1024) { // 20MB
                    $errors[] = '视频文件大小不能超过 20MB';
                }
                break;

            case 'thumb':
                if (!in_array($extension, ['jpg', 'jpeg'])) {
                    $errors[] = '缩略图格式只支持 JPG';
                }
                if ($fileSize > 64 * 1024) { // 64KB
                    $errors[] = '缩略图文件大小不能超过 64KB';
                }
                break;

            default:
                $errors[] = '不支持的媒体类型: ' . $type;
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'file_size' => $fileSize,
            'extension' => $extension,
        ];
    }
}