<?php

namespace App\Services;

use App\Models\WechatReply;
use Illuminate\Support\Facades\Log;

class WechatReplyService
{
    /**
     * 获取关键词回复
     */
    public function getKeywordReply(int $accountId, string $content): ?WechatReply
    {
        $replies = WechatReply::where('wechat_account_id', $accountId)
            ->where('reply_type', 'keyword')
            ->where('is_active', true)
            ->orderBy('priority', 'desc')
            ->get();

        foreach ($replies as $reply) {
            if ($this->matchesKeyword($reply, $content)) {
                Log::info('找到匹配的关键词回复', [
                    'account_id' => $accountId,
                    'reply_id' => $reply->id,
                    'keyword' => $content,
                ]);
                return $reply;
            }
        }

        return null;
    }

    /**
     * 获取关注回复
     */
    public function getSubscribeReply(int $accountId): ?WechatReply
    {
        return WechatReply::where('wechat_account_id', $accountId)
            ->where('reply_type', 'subscribe')
            ->where('is_active', true)
            ->first();
    }

    /**
     * 获取默认回复
     */
    public function getDefaultReply(int $accountId): ?WechatReply
    {
        return WechatReply::where('wechat_account_id', $accountId)
            ->where('reply_type', 'default')
            ->where('is_active', true)
            ->first();
    }

    /**
     * 获取事件回复
     */
    public function getEventReply(int $accountId, string $eventType, string $eventKey = ''): ?WechatReply
    {
        $query = WechatReply::where('wechat_account_id', $accountId)
            ->where('reply_type', 'event')
            ->where('trigger_type', $eventType)
            ->where('is_active', true);

        if (!empty($eventKey)) {
            $query->where(function ($q) use ($eventKey) {
                $q->whereJsonContains('keywords', $eventKey)
                  ->orWhere('keywords', 'like', '%' . $eventKey . '%');
            });
        }

        return $query->orderBy('priority', 'desc')->first();
    }

    /**
     * 检查关键词是否匹配
     */
    private function matchesKeyword(WechatReply $reply, string $content): bool
    {
        $keywords = $reply->keywords;
        if (empty($keywords)) {
            return false;
        }

        // 如果keywords是JSON字符串，解析为数组
        if (is_string($keywords)) {
            $keywords = json_decode($keywords, true) ?: [$keywords];
        }

        if (!is_array($keywords)) {
            $keywords = [$keywords];
        }

        $matchType = $reply->match_type ?? 'exact';

        foreach ($keywords as $keyword) {
            if (empty($keyword)) {
                continue;
            }

            switch ($matchType) {
                case 'exact':
                    // 完全匹配
                    if (trim($content) === trim($keyword)) {
                        return true;
                    }
                    break;

                case 'partial':
                    // 部分匹配
                    if (strpos($content, $keyword) !== false) {
                        return true;
                    }
                    break;

                case 'regex':
                    // 正则匹配
                    if (@preg_match($keyword, $content)) {
                        return true;
                    }
                    break;

                default:
                    // 默认完全匹配
                    if (trim($content) === trim($keyword)) {
                        return true;
                    }
                    break;
            }
        }

        return false;
    }

    /**
     * 创建回复规则
     */
    public function createReply(array $data): WechatReply
    {
        // 处理关键词数据
        if (isset($data['keywords']) && is_array($data['keywords'])) {
            $data['keywords'] = json_encode($data['keywords']);
        }

        return WechatReply::create($data);
    }

    /**
     * 更新回复规则
     */
    public function updateReply(WechatReply $reply, array $data): bool
    {
        // 处理关键词数据
        if (isset($data['keywords']) && is_array($data['keywords'])) {
            $data['keywords'] = json_encode($data['keywords']);
        }

        return $reply->update($data);
    }

    /**
     * 删除回复规则
     */
    public function deleteReply(WechatReply $reply): bool
    {
        return $reply->delete();
    }

    /**
     * 测试回复规则
     */
    public function testReply(WechatReply $reply, string $testContent = ''): array
    {
        try {
            $result = [
                'success' => true,
                'reply_data' => [
                    'id' => $reply->id,
                    'name' => $reply->name,
                    'reply_type' => $reply->reply_type,
                    'content_type' => $reply->content_type,
                    'content' => $reply->content,
                    'media_id' => $reply->media_id,
                    'title' => $reply->title,
                    'description' => $reply->description,
                    'url' => $reply->url,
                    'pic_url' => $reply->pic_url,
                    'thumb_media_id' => $reply->thumb_media_id,
                    'hq_music_url' => $reply->hq_music_url,
                ],
            ];

            // 如果是关键词回复，测试关键词匹配
            if ($reply->reply_type === 'keyword' && !empty($testContent)) {
                $matches = $this->matchesKeyword($reply, $testContent);
                $result['keyword_match'] = $matches;
                $result['test_content'] = $testContent;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('测试回复规则失败', [
                'reply_id' => $reply->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '测试失败: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取回复统计信息
     */
    public function getReplyStats(int $accountId, array $filters = []): array
    {
        $query = WechatReply::where('wechat_account_id', $accountId);

        // 应用过滤条件
        if (isset($filters['reply_type'])) {
            $query->where('reply_type', $filters['reply_type']);
        }

        if (isset($filters['content_type'])) {
            $query->where('content_type', $filters['content_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        $replies = $query->get();

        return [
            'total' => $replies->count(),
            'active' => $replies->where('is_active', true)->count(),
            'inactive' => $replies->where('is_active', false)->count(),
            'by_reply_type' => $replies->groupBy('reply_type')->map->count(),
            'by_content_type' => $replies->groupBy('content_type')->map->count(),
            'total_usage' => $replies->sum('usage_count'),
        ];
    }

    /**
     * 批量更新回复状态
     */
    public function batchUpdateStatus(array $replyIds, bool $isActive): int
    {
        return WechatReply::whereIn('id', $replyIds)
            ->update(['is_active' => $isActive]);
    }

    /**
     * 批量删除回复
     */
    public function batchDelete(array $replyIds): int
    {
        return WechatReply::whereIn('id', $replyIds)->delete();
    }

    /**
     * 复制回复规则
     */
    public function duplicateReply(WechatReply $reply, string $newName = null): WechatReply
    {
        $data = $reply->toArray();
        unset($data['id'], $data['created_at'], $data['updated_at']);
        
        $data['name'] = $newName ?: $reply->name . ' (副本)';
        $data['is_active'] = false; // 复制的规则默认为非激活状态
        $data['usage_count'] = 0; // 重置使用次数

        return WechatReply::create($data);
    }

    /**
     * 导入回复规则
     */
    public function importReplies(int $accountId, array $repliesData): array
    {
        $imported = 0;
        $failed = 0;
        $errors = [];

        foreach ($repliesData as $index => $replyData) {
            try {
                $replyData['wechat_account_id'] = $accountId;
                
                // 处理关键词数据
                if (isset($replyData['keywords']) && is_array($replyData['keywords'])) {
                    $replyData['keywords'] = json_encode($replyData['keywords']);
                }

                WechatReply::create($replyData);
                $imported++;
            } catch (\Exception $e) {
                $failed++;
                $errors[] = "第 {$index} 条记录导入失败: " . $e->getMessage();
            }
        }

        return [
            'imported' => $imported,
            'failed' => $failed,
            'errors' => $errors,
        ];
    }

    /**
     * 导出回复规则
     */
    public function exportReplies(int $accountId, array $filters = []): array
    {
        $query = WechatReply::where('wechat_account_id', $accountId);

        // 应用过滤条件
        if (isset($filters['reply_type'])) {
            $query->where('reply_type', $filters['reply_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        $replies = $query->get();

        return $replies->map(function ($reply) {
            $data = $reply->toArray();
            unset($data['id'], $data['wechat_account_id'], $data['created_at'], $data['updated_at']);
            
            // 解析关键词JSON
            if (!empty($data['keywords'])) {
                $keywords = json_decode($data['keywords'], true);
                if (is_array($keywords)) {
                    $data['keywords'] = $keywords;
                }
            }
            
            return $data;
        })->toArray();
    }
}