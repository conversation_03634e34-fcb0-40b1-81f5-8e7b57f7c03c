<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AuditLogResource\Pages;
use App\Filament\Resources\AuditLogResource\RelationManagers;
use App\Models\AuditLog;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Support\Enums\MaxWidth;

class AuditLogResource extends Resource
{
    protected static ?string $model = AuditLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '系统管理';

    protected static ?int $navigationSort = 8;

    protected static ?int $navigationGroupSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('user_id')->label('操作人ID'),
                Forms\Components\DateTimePicker::make('operated_at')->label('操作时间'),
                Forms\Components\TextInput::make('operation_type')->label('操作类型'),
                Forms\Components\TextInput::make('module')->label('模块/功能名'),
                Forms\Components\TextInput::make('target_id')->label('操作对象ID'),
                Forms\Components\TextInput::make('request_ip')->label('请求IP'),
                Forms\Components\TextInput::make('request_url')->label('请求URL'),
                Forms\Components\Textarea::make('request_data')->label('请求参数'),
                Forms\Components\TextInput::make('status')->label('结果状态'),
                Forms\Components\Textarea::make('error_message')->label('错误信息'),
                Forms\Components\Textarea::make('remark')->label('备注'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('操作人')
                    ->sortable()
                    ->searchable()
                    ->default('未知用户'),
                Tables\Columns\TextColumn::make('operated_at')
                    ->label('操作时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('operation_type_text')
                    ->label('操作类型')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '创建' => 'success',
                        '更新' => 'warning',
                        '删除' => 'danger',
                        '登录' => 'info',
                        '登出' => 'gray',
                        '导出' => 'primary',
                        default => 'secondary',
                    }),
                Tables\Columns\TextColumn::make('module')
                    ->label('模块/功能名')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('target_id')
                    ->label('操作对象ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('request_ip')
                    ->label('请求IP')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status_text')
                    ->label('结果状态')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '成功' => 'success',
                        '失败' => 'danger',
                        '错误' => 'warning',
                        default => 'secondary',
                    }),
                Tables\Columns\TextColumn::make('request_url')
                    ->label('请求URL')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) > 50) {
                            return $state;
                        }
                        return null;
                    }),
                Tables\Columns\TextColumn::make('remark')
                    ->label('备注')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) > 30) {
                            return $state;
                        }
                        return null;
                    }),
            ])
            ->filters([
                SelectFilter::make('user_id')
                    ->label('操作人')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('operation_type')
                    ->label('操作类型')
                    ->options([
                        'CREATE' => '创建',
                        'UPDATE' => '更新',
                        'DELETE' => '删除',
                        'VIEW' => '查看',
                        'LOGIN' => '登录',
                        'LOGOUT' => '登出',
                        'EXPORT' => '导出',
                        'IMPORT' => '导入',
                        'CLEANUP' => '清理',
                    ]),
                SelectFilter::make('module')
                    ->label('模块')
                    ->options([
                        '用户管理' => '用户管理',
                        '文章管理' => '文章管理',
                        '分类管理' => '分类管理',
                        '帖子管理' => '帖子管理',
                        '评论管理' => '评论管理',
                        '活动管理' => '活动管理',
                        '报名管理' => '报名管理',
                        '审计日志' => '审计日志',
                        '系统设置' => '系统设置',
                        '媒体文件' => '媒体文件',
                        '友情链接' => '友情链接',
                        '标签管理' => '标签管理',
                        '角色管理' => '角色管理',
                        '权限管理' => '权限管理',
                        '系统登录' => '系统登录',
                        '系统登出' => '系统登出',
                    ]),
                SelectFilter::make('status')
                    ->label('状态')
                    ->options([
                        'SUCCESS' => '成功',
                        'FAILED' => '失败',
                        'ERROR' => '错误',
                    ]),
                Filter::make('operated_at')
                    ->form([
                        DatePicker::make('operated_from')
                            ->label('开始时间'),
                        DatePicker::make('operated_until')
                            ->label('结束时间'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['operated_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('operated_at', '>=', $date),
                            )
                            ->when(
                                $data['operated_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('operated_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['operated_from'] ?? null) {
                            $indicators['operated_from'] = '开始时间: ' . \Carbon\Carbon::parse($data['operated_from'])->toDateString();
                        }
                        if ($data['operated_until'] ?? null) {
                            $indicators['operated_until'] = '结束时间: ' . \Carbon\Carbon::parse($data['operated_until'])->toDateString();
                        }
                        return $indicators;
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(4)
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth(MaxWidth::FiveExtraLarge),
            ])
            ->headerActions([
                Tables\Actions\ExportAction::make()
                    ->label('导出日志')
                    ->color('success')
                    ->icon('heroicon-o-arrow-down-tray'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('删除审计日志')
                        ->modalDescription('确定要删除选中的审计日志吗？此操作不可撤销。'),
                ]),
            ])
            ->defaultSort('operated_at', 'desc')
            ->searchable();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAuditLogs::route('/'),
            'create' => Pages\CreateAuditLog::route('/create'),
            'edit' => Pages\EditAuditLog::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '审计日志';
    }

    public static function getModelLabel(): string
    {
        return '审计日志';
    }

    public static function getPluralModelLabel(): string
    {
        return '审计日志';
    }
}
