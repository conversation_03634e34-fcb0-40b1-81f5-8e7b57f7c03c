<?php

namespace App\Services;

use App\Models\WechatAccount;
use App\Models\WechatArticle;
use App\Models\Article;
use EasyWeChat\OfficialAccount\Application;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Exception;

class WechatArticleCollectService
{
    /**
     * 采集指定公众号的文章
     */
    public function collectFromAccount(WechatAccount $account, int $count = 20): array
    {
        try {
            $app = new Application($account->getWechatConfig());
            $material = $app->material;
            
            // 获取图文素材列表
            $result = $material->list('news', 0, $count);
            
            if (!isset($result['item'])) {
                throw new Exception('获取素材列表失败: ' . json_encode($result));
            }
            
            $collected = [];
            
            foreach ($result['item'] as $item) {
                $newsItem = $item['content']['news_item'][0] ?? null;
                if (!$newsItem) {
                    continue;
                }
                
                // 检查是否已存在
                $existingArticle = WechatArticle::where('wechat_account_id', $account->id)
                    ->where('media_id', $item['media_id'])
                    ->first();
                    
                if ($existingArticle) {
                    continue;
                }
                
                // 创建微信文章记录
                $wechatArticle = $this->createWechatArticle($account, $item, $newsItem);
                
                // 下载封面图片
                if ($newsItem['thumb_url']) {
                    $this->downloadThumbImage($wechatArticle, $newsItem['thumb_url']);
                }
                
                $collected[] = $wechatArticle;
            }
            
            // 更新最后采集时间
            $account->updateLastCollectTime();
            
            Log::info("成功采集公众号文章", [
                'account_id' => $account->id,
                'account_name' => $account->name,
                'collected_count' => count($collected)
            ]);
            
            return $collected;
            
        } catch (Exception $e) {
            Log::error("采集公众号文章失败", [
                'account_id' => $account->id,
                'account_name' => $account->name,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 通过爬虫采集公众号文章
     */
    public function collectFromUrl(string $url, ?WechatAccount $account = null): WechatArticle
    {
        try {
            // 使用简单的HTTP请求获取页面内容
            $content = file_get_contents($url);
            if (!$content) {
                throw new Exception('无法获取页面内容');
            }
            
            // 解析页面内容
            $articleData = $this->parseWechatArticlePage($content, $url);
            
            // 检查是否已存在
            $existingArticle = WechatArticle::where('url', $url)->first();
            if ($existingArticle) {
                return $existingArticle;
            }
            
            // 创建微信文章记录
            $wechatArticle = WechatArticle::create([
                'wechat_account_id' => $account?->id,
                'title' => $articleData['title'],
                'digest' => $articleData['digest'],
                'content' => $articleData['content'],
                'url' => $url,
                'thumb_url' => $articleData['thumb_url'],
                'author' => $articleData['author'],
                'collect_type' => 'crawler',
                'collect_source' => $url,
                'published_at' => $articleData['published_at'],
                'collected_at' => now(),
                'status' => 'pending',
            ]);
            
            // 下载封面图片
            if ($articleData['thumb_url']) {
                $this->downloadThumbImage($wechatArticle, $articleData['thumb_url']);
            }
            
            Log::info("成功爬取微信文章", [
                'url' => $url,
                'title' => $articleData['title']
            ]);
            
            return $wechatArticle;
            
        } catch (Exception $e) {
            Log::error("爬取微信文章失败", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 将微信文章转换为本站文章
     */
    public function convertToArticle(WechatArticle $wechatArticle, array $options = []): Article
    {
        try {
            // 检查是否已转换
            if ($wechatArticle->article_id) {
                return $wechatArticle->article;
            }
            
            // 验证必要字段
            if (empty($wechatArticle->content)) {
                throw new Exception('微信文章内容为空，无法转换');
            }
            
            $processedContent = $this->processContent($wechatArticle->content);
            if (empty($processedContent)) {
                throw new Exception('处理后的文章内容为空，无法转换');
            }
            
            $article = Article::create([
                'title' => $wechatArticle->title,
                'short_title' => Str::limit($wechatArticle->title, 50),
                'content' => ['html' => $processedContent], // 转换为数组格式
                'summary' => $wechatArticle->digest ?: Str::limit(strip_tags($wechatArticle->content), 200),
                'thumb_image' => $wechatArticle->getLocalThumbUrl(),
                'author' => $wechatArticle->author ?: '微信采集',
                'source' => $wechatArticle->wechatAccount?->name ?: '微信公众号',
                'user_id' => auth()->id() ?? 1, // 添加必需的user_id字段
                'status' => $options['auto_publish'] ? 'published' : 'draft',
                'published_at' => $options['auto_publish'] ? now() : null,
                'hot_score' => 0.0, // 添加hot_score默认值
            ]);
            
            // 关联标签
            if (!empty($options['tags'])) {
                $article->tags()->sync($options['tags']);
            }
            
            // 更新微信文章状态
            $wechatArticle->markAsPublished($article->id);
            
            Log::info("成功转换微信文章为本站文章", [
                'wechat_article_id' => $wechatArticle->id,
                'article_id' => $article->id,
                'title' => $article->title
            ]);
            
            return $article;
            
        } catch (Exception $e) {
            $wechatArticle->markAsFailed('转换失败: ' . $e->getMessage());
            
            Log::error("转换微信文章失败", [
                'wechat_article_id' => $wechatArticle->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 创建微信文章记录
     */
    private function createWechatArticle(WechatAccount $account, array $item, array $newsItem): WechatArticle
    {
        return WechatArticle::create([
            'wechat_account_id' => $account->id,
            'media_id' => $item['media_id'],
            'title' => $newsItem['title'],
            'digest' => $newsItem['digest'] ?? '',
            'content' => $newsItem['content'],
            'content_source_url' => $newsItem['content_source_url'] ?? '',
            'url' => $newsItem['url'] ?? '',
            'thumb_media_id' => $newsItem['thumb_media_id'] ?? '',
            'thumb_url' => $newsItem['thumb_url'] ?? '',
            'author' => $newsItem['author'] ?? '',
            'show_cover_pic' => $newsItem['show_cover_pic'] ?? true,
            'collect_type' => 'api',
            'collect_source' => $account->name,
            'published_at' => isset($item['update_time']) ? Carbon::createFromTimestamp($item['update_time']) : null,
            'collected_at' => now(),
            'status' => 'pending',
        ]);
    }
    
    /**
     * 下载封面图片
     */
    private function downloadThumbImage(WechatArticle $wechatArticle, string $thumbUrl): void
    {
        try {
            $imageContent = file_get_contents($thumbUrl);
            if (!$imageContent) {
                return;
            }
            
            $extension = pathinfo(parse_url($thumbUrl, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
            $filename = 'wechat/thumbs/' . date('Y/m/d') . '/' . Str::random(32) . '.' . $extension;
            
            Storage::disk('public')->put($filename, $imageContent);
            
            $wechatArticle->update(['local_thumb_path' => $filename]);
            
        } catch (Exception $e) {
            Log::warning("下载封面图片失败", [
                'wechat_article_id' => $wechatArticle->id,
                'thumb_url' => $thumbUrl,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 解析微信文章页面
     */
    private function parseWechatArticlePage(string $content, string $url): array
    {
        // 简单的正则表达式解析，实际项目中可能需要更复杂的解析逻辑
        $title = '';
        $digest = '';
        $articleContent = '';
        $thumbUrl = '';
        $author = '';
        $publishedAt = null;
        
        // 提取标题
        if (preg_match('/<h1[^>]*id="activity-name"[^>]*>([^<]+)<\/h1>/', $content, $matches)) {
            $title = trim($matches[1]);
        }
        
        // 提取作者
        if (preg_match('/<span[^>]*class="rich_media_meta_text"[^>]*>([^<]+)<\/span>/', $content, $matches)) {
            $author = trim($matches[1]);
        }
        
        // 提取内容
        if (preg_match('/<div[^>]*id="js_content"[^>]*>(.*?)<\/div>/s', $content, $matches)) {
            $articleContent = $matches[1];
        }
        
        // 提取封面图片
        if (preg_match('/<img[^>]*class="rich_media_thumb"[^>]*src="([^"]+)"/', $content, $matches)) {
            $thumbUrl = $matches[1];
        }
        
        return [
            'title' => $title ?: '未知标题',
            'digest' => $digest,
            'content' => $articleContent,
            'thumb_url' => $thumbUrl,
            'author' => $author,
            'published_at' => $publishedAt,
        ];
    }
    
    /**
     * 处理文章内容
     */
    private function processContent(?string $content): string
    {
        // 如果内容为空，返回空字符串
        if (empty($content)) {
            return '';
        }
        
        // 清理和处理内容
        $content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $content);
        $content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $content);
        
        // 处理图片链接
        $content = preg_replace_callback('/<img[^>]*src="([^"]+)"[^>]*>/', function($matches) {
            $imgUrl = $matches[1];
            // 这里可以下载图片到本地并替换链接
            return $matches[0];
        }, $content);
        
        // 去除多余的空白字符
        $content = trim($content);
        
        return $content;
    }
}