# 短信模板系统使用指南

## 概述

本系统支持云片网短信模板的多变量自定义设置，可以灵活创建和管理各种类型的短信模板。

## 功能特性

### 1. 多模板支持
- 验证码模板（嘉定幸福课程、心灵嘉园、通用验证码）
- 账号管理模板（审核通过、审核未通过、账号开通）
- 活动通知模板（报名确认、现场咨询、暑期培训）
- 自定义模板（支持任意参数组合）

### 2. 参数自定义
- 每个模板可配置不同的参数列表
- 支持参数验证，确保发送时参数完整
- 参数说明功能，便于理解和使用

### 3. 分类管理
- verification（验证码类）
- account（账号管理类）
- registration（报名类）
- consultation（咨询类）
- notification（通知类）

## 安装和配置

### 1. 运行数据库迁移
```bash
php artisan migrate
```

### 2. 导入短信模板数据
```bash
# 运行批量脚本
update_comprehensive_sms_templates.bat

# 或者手动运行
php artisan db:seed --class=ComprehensiveSmsTemplateSeeder
```

### 3. 配置云片网API
在 `.env` 文件中添加：
```env
YUNPIAN_API_KEY=your_yunpian_api_key
```

在 `config/services.php` 中添加：
```php
'yunpian' => [
    'api_key' => env('YUNPIAN_API_KEY'),
],
```

## 使用方法

### 1. 在活动详情中配置短信模板

```php
// 创建活动详情时指定短信模板
$activityDetail = ActivityDetail::create([
    'activity_id' => $activity->id,
    'sms_template_id' => $smsTemplate->id, // 指定短信模板
    // ... 其他字段
]);
```

### 2. 发送短信

系统会自动根据配置的模板发送短信：

```php
// 在RegistrationController中，系统会自动：
// 1. 检查ActivityDetail是否配置了短信模板
// 2. 根据模板类型构建相应的参数数据
// 3. 调用SmsService发送短信

$this->sendSmsNotification($registration, $activityDetail);
```

### 3. 手动发送短信

```php
use App\Services\SmsService;

$smsService = new SmsService();

// 使用模板代码发送
$result = $smsService->sendSms(
    '13800138000', // 手机号
    'activity_registration_confirm', // 模板代码
    [
        'name' => '张三',
        'topic' => '测试活动',
        'time' => '2024-01-20 14:00',
        'address' => '上海市嘉定区'
    ]
);
```

## 模板列表

### 验证码类模板

1. **嘉定幸福课程身份验证** (`jiading_verification`)
   - 参数：`code`（验证码）
   - 内容：【上海市嘉定区教育学院】验证码#code#，您正在进行嘉定幸福课程身份验证，感谢关注

2. **通用验证码** (`general_verification`)
   - 参数：`code`（验证码）
   - 内容：【上海市嘉定区教育学院】您的验证码是#code#。如非本人操作，请忽略本短信

3. **心灵嘉园身份验证** (`xinling_verification`)
   - 参数：`code`（验证码）
   - 内容：【上海市嘉定区教育学院】验证码#code#，您正在进行心灵嘉园身份验证，感谢关注

### 账号管理类模板

4. **账号审核通过** (`account_approved`)
   - 参数：`name`（用户姓名）
   - 内容：【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已通过审核，感谢关注

5. **账号审核未通过** (`account_rejected`)
   - 参数：`name`（用户姓名）
   - 内容：【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号未能通过审核，可能的原因是您不是本课程的适用对象，感谢关注！

6. **账号开通通知** (`account_activated`)
   - 参数：`name`（用户姓名）、`pwd`（登录密码）
   - 内容：【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已开通，请用微信关注公众号嘉定区幸福课程，并用手机号登录，密码为#pwd#

### 活动通知类模板

7. **现场咨询通知** (`consultation_notice`)
   - 参数：`topic`（咨询主题）、`time`（咨询时间）、`address`（咨询地点）、`obj`（咨询对象）
   - 内容：【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。

8. **暑期大培训报名成功** (`summer_training_success`)
   - 参数：`name`（课程名称）
   - 内容：【上海市嘉定区教育学院】您好，您报的暑期大培训课程"#name#"已报名成功，请关注开课时间，准时参加！

9. **活动报名确认** (`activity_registration_confirm`)
   - 参数：`name`（参与者姓名）、`topic`（活动主题）、`time`（活动时间）、`address`（活动地点）
   - 内容：【上海市嘉定区教育学院】尊敬的#name#，您已成功报名参加"#topic#"活动，时间：#time#，地点：#address#，请准时参加！

## 管理界面

### 1. 短信模板管理

可以通过 `SmsTemplateController` 管理短信模板：
- 查看模板列表
- 创建新模板
- 编辑现有模板
- 删除模板
- 测试发送

### 2. 测试功能

运行测试脚本验证系统功能：
```bash
php test_sms_template_system.php
```

## 扩展开发

### 1. 添加新模板

```php
// 在数据库中添加新模板
SmsTemplate::create([
    'name' => '新模板名称',
    'code' => 'new_template_code',
    'yunpian_template_id' => '云片网模板ID',
    'description' => '模板描述',
    'template_params' => [
        'param1' => '参数1说明',
        'param2' => '参数2说明'
    ],
    'param_description' => '参数说明文本',
    'template_category' => 'custom'
]);
```

### 2. 自定义参数构建逻辑

在 `RegistrationController` 的 `buildTemplateData` 方法中添加新的模板处理逻辑：

```php
case 'new_template_code':
    return [
        'param1' => $someValue,
        'param2' => $anotherValue
    ];
```

### 3. 添加新的分类

在 `SmsTemplate` 模型的 `getCategoryOptions` 方法中添加新分类。

## 注意事项

1. **云片网模板ID**：确保在云片网后台创建对应的模板，并获取正确的模板ID
2. **参数格式**：云片网使用 `#变量名#` 格式，系统会自动处理
3. **参数验证**：发送前会验证参数完整性，缺少参数会导致发送失败
4. **向后兼容**：系统保持对旧短信发送方式的兼容
5. **错误处理**：所有短信发送都有完整的错误日志记录

## 故障排除

### 1. 短信发送失败
- 检查云片网API密钥配置
- 验证模板ID是否正确
- 确认参数是否完整
- 查看日志文件获取详细错误信息

### 2. 模板不存在
- 确认模板代码是否正确
- 检查数据库中是否有对应记录
- 重新运行种子数据

### 3. 参数验证失败
- 检查传入的参数是否包含模板要求的所有字段
- 确认参数名称是否与模板配置一致

## 技术支持

如有问题，请查看：
1. 系统日志文件
2. 运行测试脚本进行诊断
3. 检查云片网后台的发送记录