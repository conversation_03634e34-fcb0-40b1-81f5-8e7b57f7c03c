<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\Contracts\View\View;

class VideoPlayer extends Component
{
    public string $src;
    public bool $controls;
    public string $width;
    public string $height;
    public ?string $poster;

    /**
     * Create a new component instance.
     */
    public function __construct(
        string $src,
        bool $controls = true,
        string $width = '100%',
        string $height = 'auto',
        ?string $poster = null
    ) {
        $this->src = $src;
        $this->controls = $controls;
        $this->width = $width;
        $this->height = $height;
        $this->poster = $poster;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('components.video-player');
    }
}