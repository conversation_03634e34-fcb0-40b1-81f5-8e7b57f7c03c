<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WechatAccountResource\Pages;
use App\Models\WechatAccount;
use App\Services\WechatArticleCollectService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WechatAccountResource extends Resource
{
    protected static ?string $model = WechatAccount::class;

    protected static ?string $navigationGroup = '微信推文管理';

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    
    protected static ?string $navigationLabel = '微信公众号';
    
    protected static ?string $slug = 'wechat-accounts';
    
    protected static ?int $navigationSort = 10;
    
    protected static ?int $navigationGroupSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('公众号名称')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\Select::make('account_type')
                            ->label('账号类型')
                            ->options([
                                'subscription' => '订阅号',
                                'service' => '服务号',
                                'enterprise' => '企业号',
                            ])
                            ->required(),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('描述')
                            ->rows(3)
                            ->columnSpanFull(),
                        
                        Forms\Components\FileUpload::make('avatar')
                            ->label('头像')
                            ->image()
                            ->directory('wechat/avatars')
                            ->visibility('public'),
                        
                        Forms\Components\FileUpload::make('qr_code')
                            ->label('二维码')
                            ->image()
                            ->directory('wechat/qrcodes')
                            ->visibility('public'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('接口配置')
                    ->schema([
                        Forms\Components\TextInput::make('app_id')
                            ->label('AppID')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('app_secret')
                            ->label('AppSecret')
                            ->required()
                            ->password()
                            ->revealable()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('token')
                            ->label('Token')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('aes_key')
                            ->label('EncodingAESKey')
                            ->maxLength(255),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('采集设置')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true),
                        
                        Forms\Components\Toggle::make('auto_collect')
                            ->label('自动采集')
                            ->default(false)
                            ->live(),
                        
                        Forms\Components\TextInput::make('collect_interval')
                            ->label('采集间隔（分钟）')
                            ->numeric()
                            ->default(60)
                            ->minValue(5)
                            ->visible(fn (Forms\Get $get) => $get('auto_collect')),
                        
                        Forms\Components\KeyValue::make('collect_config')
                            ->label('采集配置')
                            ->keyLabel('配置项')
                            ->valueLabel('配置值')
                            ->columnSpanFull(),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('头像')
                    ->circular()
                    ->size(40),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('公众号名称')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('account_type')
                    ->label('账号类型')
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'subscription' => '订阅号',
                        'service' => '服务号',
                        'enterprise' => '企业号',
                        default => '未知类型'
                    })
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'subscription' => 'info',
                        'service' => 'success',
                        'enterprise' => 'warning',
                        default => 'gray'
                    }),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('启用状态')
                    ->boolean(),
                
                Tables\Columns\IconColumn::make('auto_collect')
                    ->label('自动采集')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('collect_interval')
                    ->label('采集间隔')
                    ->formatStateUsing(fn (?int $state): string => $state ? $state . ' 分钟' : '-')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('wechat_articles_count')
                    ->label('文章数量')
                    ->counts('wechatArticles')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('last_collect_at')
                    ->label('最后采集时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('account_type')
                    ->label('账号类型')
                    ->options([
                        'subscription' => '订阅号',
                        'service' => '服务号',
                        'enterprise' => '企业号',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('启用状态'),
                
                Tables\Filters\TernaryFilter::make('auto_collect')
                    ->label('自动采集'),
                
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('collect')
                    ->label('立即采集')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->requiresConfirmation()
                    ->action(function (WechatAccount $record) {
                        try {
                            $service = app(\App\Services\WechatArticleCollectService::class);
                            $articles = $service->collectFromAccount($record);
                            
                            \Filament\Notifications\Notification::make()
                                ->title('采集成功')
                                ->body('成功采集 ' . count($articles) . ' 篇文章')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('采集失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (WechatAccount $record) => $record->is_active),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
    
    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        ImageEntry::make('avatar')
                            ->label('头像')
                            ->circular()
                            ->size(80),
                        
                        TextEntry::make('name')
                            ->label('公众号名称'),
                        
                        TextEntry::make('account_type')
                            ->label('账号类型')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'subscription' => '订阅号',
                                'service' => '服务号',
                                'enterprise' => '企业号',
                                default => '未知类型'
                            }),
                        
                        TextEntry::make('description')
                            ->label('描述')
                            ->columnSpanFull(),
                    ])
                    ->columns(3),
                
                Section::make('接口配置')
                    ->schema([
                        TextEntry::make('app_id')
                            ->label('AppID'),
                        
                        TextEntry::make('token')
                            ->label('Token'),
                    ])
                    ->columns(2),
                
                Section::make('采集设置')
                    ->schema([
                        TextEntry::make('is_active')
                            ->label('启用状态')
                            ->formatStateUsing(fn (bool $state): string => $state ? '已启用' : '已禁用')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                        
                        TextEntry::make('auto_collect')
                            ->label('自动采集')
                            ->formatStateUsing(fn (bool $state): string => $state ? '已启用' : '已禁用')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'success' : 'gray'),
                        
                        TextEntry::make('collect_interval')
                            ->label('采集间隔')
                            ->formatStateUsing(fn (?int $state): string => $state ? $state . ' 分钟' : '-'),
                        
                        TextEntry::make('last_collect_at')
                            ->label('最后采集时间')
                            ->dateTime('Y-m-d H:i:s'),
                    ])
                    ->columns(2),
                
                Section::make('统计信息')
                    ->schema([
                        TextEntry::make('wechat_articles_count')
                            ->label('文章总数')
                            ->state(fn (WechatAccount $record) => $record->wechatArticles()->count()),
                        
                        TextEntry::make('pending_articles_count')
                            ->label('待处理文章')
                            ->state(fn (WechatAccount $record) => $record->wechatArticles()->pending()->count()),
                        
                        TextEntry::make('published_articles_count')
                            ->label('已发布文章')
                            ->state(fn (WechatAccount $record) => $record->wechatArticles()->published()->count()),
                    ])
                    ->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWechatAccounts::route('/'),
            'create' => Pages\CreateWechatAccount::route('/create'),
            'view' => Pages\ViewWechatAccount::route('/{record}'),
            'edit' => Pages\EditWechatAccount::route('/{record}/edit'),
        ];
    }
    
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}