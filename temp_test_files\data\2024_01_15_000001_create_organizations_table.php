<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('组织名称');
            $table->string('code', 100)->unique()->comment('组织代码');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('上级组织ID');
            $table->enum('type', ['department', 'division', 'team', 'company', 'branch'])
                  ->default('department')
                  ->comment('组织类型');
            $table->text('description')->nullable()->comment('组织描述');
            $table->string('contact_person')->nullable()->comment('联系人');
            $table->string('contact_phone')->nullable()->comment('联系电话');
            $table->string('contact_email')->nullable()->comment('联系邮箱');
            $table->text('address')->nullable()->comment('办公地址');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['parent_id']);
            $table->index(['type']);
            $table->index(['is_active']);
            $table->index(['sort_order']);
            
            // 外键约束
            $table->foreign('parent_id')->references('id')->on('organizations')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};