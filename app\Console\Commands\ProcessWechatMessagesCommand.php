<?php

namespace App\Console\Commands;

use App\Jobs\ProcessWechatMessageJob;
use App\Models\WechatMessage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessWechatMessagesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wechat:process-messages 
                            {--account= : 指定微信账号ID}
                            {--limit=100 : 每次处理的消息数量}
                            {--status=pending : 处理指定状态的消息}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理待处理的微信消息';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $accountId = $this->option('account');
        $limit = (int) $this->option('limit');
        $status = $this->option('status');

        $this->info('开始处理微信消息...');
        
        $query = WechatMessage::where('status', $status)
            ->orderBy('created_at', 'asc')
            ->limit($limit);

        if ($accountId) {
            $query->where('wechat_account_id', $accountId);
            $this->info("处理账号ID: {$accountId} 的消息");
        }

        $messages = $query->get();
        
        if ($messages->isEmpty()) {
            $this->info('没有找到待处理的消息');
            return self::SUCCESS;
        }

        $this->info("找到 {$messages->count()} 条待处理消息");
        
        $progressBar = $this->output->createProgressBar($messages->count());
        $progressBar->start();

        $processed = 0;
        $failed = 0;

        foreach ($messages as $message) {
            try {
                // 将消息加入队列处理
                ProcessWechatMessageJob::dispatch($message);
                $processed++;
                
                Log::info('微信消息已加入处理队列', [
                    'message_id' => $message->id,
                    'openid' => $message->openid,
                ]);
            } catch (\Exception $e) {
                $failed++;
                
                Log::error('微信消息加入队列失败', [
                    'message_id' => $message->id,
                    'error' => $e->getMessage(),
                ]);
                
                $this->error("消息 {$message->id} 处理失败: {$e->getMessage()}");
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        
        $this->info("处理完成！成功: {$processed}, 失败: {$failed}");
        
        return self::SUCCESS;
    }
}