<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Activity extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'status',
        'publish_time',
        'views_count',
        'qrcode_url',
        'wechat_url',
    ];

    protected $casts = [
        'publish_time' => 'datetime',
    ];

    public function details(): HasMany
    {
        return $this->hasMany(ActivityDetail::class);
    }

    public function registrations(): HasMany
    {
        return $this->hasMany(Registration::class);
    }
} 