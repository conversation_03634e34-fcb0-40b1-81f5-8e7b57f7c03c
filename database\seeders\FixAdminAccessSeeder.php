<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class FixAdminAccessSeeder extends Seeder
{
    /**
     * 修复管理员访问权限问题
     */
    public function run(): void
    {
        $this->command->info('🔧 修复管理员访问权限...');

        // 确保有super_admin角色
        $superAdminRole = Role::firstOrCreate([
            'name' => 'super_admin',
            'guard_name' => 'web'
        ]);

        // 确保有admin角色
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'web'
        ]);

        // 获取所有权限
        $allPermissions = Permission::all();

        // 给super_admin角色分配所有权限
        $superAdminRole->syncPermissions($allPermissions);
        $this->command->info("✅ super_admin角色已获得 {$allPermissions->count()} 个权限");

        // 给admin角色分配所有权限
        $adminRole->syncPermissions($allPermissions);
        $this->command->info("✅ admin角色已获得 {$allPermissions->count()} 个权限");

        // 确保管理员用户存在并有正确的角色
        $admin = User::where('email', '<EMAIL>')->first();
        
        if ($admin) {
            // 确保用户是激活状态
            $admin->update([
                'is_active' => true,
                'is_verified' => true,
            ]);

            // 分配角色
            $admin->syncRoles(['super_admin', 'admin']);
            
            $this->command->info("✅ 管理员用户已更新：");
            $this->command->info("   邮箱: {$admin->email}");
            $this->command->info("   状态: " . ($admin->is_active ? '激活' : '未激活'));
            $this->command->info("   角色: " . $admin->roles->pluck('name')->implode(', '));
        } else {
            // 创建管理员用户
            $admin = User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'is_active' => true,
                'is_verified' => true,
                'real_name' => '超级管理员',
                'region' => '系统',
                'school' => '管理中心',
            ]);

            $admin->assignRole(['super_admin', 'admin']);
            
            $this->command->info("✅ 创建了新的管理员用户：");
            $this->command->info("   邮箱: <EMAIL>");
            $this->command->info("   密码: password");
        }

        // 验证权限
        $this->validateAdminAccess($admin);
    }

    /**
     * 验证管理员访问权限
     */
    private function validateAdminAccess($admin)
    {
        $this->command->info('');
        $this->command->info('🔍 验证管理员访问权限...');

        // 检查基本条件
        $isActive = $admin->is_active;
        $hasAdminRole = $admin->hasRole('admin');
        $hasSuperAdminRole = $admin->hasRole('super_admin');

        $this->command->info("用户激活状态: " . ($isActive ? '✅ 是' : '❌ 否'));
        $this->command->info("拥有admin角色: " . ($hasAdminRole ? '✅ 是' : '❌ 否'));
        $this->command->info("拥有super_admin角色: " . ($hasSuperAdminRole ? '✅ 是' : '❌ 否'));

        // 检查canAccessPanel方法
        try {
            $panel = \Filament\Facades\Filament::getDefaultPanel();
            $canAccess = $admin->canAccessPanel($panel);
            $this->command->info("可以访问后台: " . ($canAccess ? '✅ 是' : '❌ 否'));
        } catch (\Exception $e) {
            $this->command->error("检查访问权限时出错: " . $e->getMessage());
        }

        // 检查一些关键权限
        $keyPermissions = [
            'view_any_user',
            'view_any_role',
            'view_any_registration',
            'view_any_activity',
        ];

        $this->command->info('');
        $this->command->info('🔑 关键权限检查:');
        foreach ($keyPermissions as $permission) {
            $hasPermission = $admin->can($permission);
            $this->command->info("  {$permission}: " . ($hasPermission ? '✅' : '❌'));
        }

        $this->command->info('');
        if ($isActive && ($hasAdminRole || $hasSuperAdminRole)) {
            $this->command->info('🎉 管理员权限配置完成！现在可以登录后台了。');
            $this->command->info('');
            $this->command->info('登录信息:');
            $this->command->info('URL: http://127.0.0.1:8000/admin');
            $this->command->info('邮箱: <EMAIL>');
            $this->command->info('密码: password');
        } else {
            $this->command->error('❌ 管理员权限配置有问题，请检查！');
        }
    }
}
