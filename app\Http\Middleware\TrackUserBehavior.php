<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\PointService;
use Illuminate\Support\Facades\Auth;

class TrackUserBehavior
{
    protected $pointService;

    public function __construct(PointService $pointService)
    {
        $this->pointService = $pointService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // 只处理已登录的用户
        if (Auth::check()) {
            $user = Auth::user();
            
            // 记录登录积分（如果是登录后的第一个请求）
            if ($request->session()->get('just_logged_in')) {
                $this->pointService->recordLoginPoints($user);
                $request->session()->forget('just_logged_in');
            }

            // 记录页面浏览行为（通过JavaScript发送的AJAX请求）
            if ($request->ajax() && $request->has('track_behavior')) {
                $this->handleBehaviorTracking($request, $user);
            }
        }

        return $response;
    }

    /**
     * 处理行为跟踪
     */
    protected function handleBehaviorTracking(Request $request, $user)
    {
        $action = $request->input('action');
        $targetType = $request->input('target_type');
        $targetId = $request->input('target_id');
        $duration = $request->input('duration', 0);
        $url = $request->input('url');

        switch ($action) {
            case 'browse':
                $this->pointService->recordBrowsePoints(
                    $user, 
                    $targetType, 
                    $targetId, 
                    $duration, 
                    $url
                );
                break;
            
            case 'video_view':
                $this->pointService->recordVideoPoints(
                    $user, 
                    $targetId, 
                    $duration, 
                    $url
                );
                break;
        }
    }
}
