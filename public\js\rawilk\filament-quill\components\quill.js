var Bt=Object.create;var wt=Object.defineProperty;var Ft=Object.getOwnPropertyDescriptor;var Ct=Object.getOwnPropertyNames;var Ut=Object.getPrototypeOf,Ht=Object.prototype.hasOwnProperty;var zt=(I,d)=>()=>(d||I((d={exports:{}}).exports,d),d.exports);var Kt=(I,d,h,T)=>{if(d&&typeof d=="object"||typeof d=="function")for(let k of Ct(d))!Ht.call(I,k)&&k!==h&&wt(I,k,{get:()=>d[k],enumerable:!(T=Ft(d,k))||T.enumerable});return I};var mt=(I,d,h)=>(h=I!=null?Bt(Ut(I)):{},Kt(d||!I||!I.__esModule?wt(h,"default",{value:I,enumerable:!0}):h,I));var vt=zt((dt,bt)=>{(function(d,h){typeof dt=="object"&&typeof bt=="object"?bt.exports=h():typeof define=="function"&&define.amd?define([],h):typeof dt=="object"?dt.Quill=h():d.Quill=h()})(typeof self<"u"?self:dt,function(){return function(I){var d={};function h(T){if(d[T])return d[T].exports;var k=d[T]={i:T,l:!1,exports:{}};return I[T].call(k.exports,k,k.exports,h),k.l=!0,k.exports}return h.m=I,h.c=d,h.d=function(T,k,E){h.o(T,k)||Object.defineProperty(T,k,{configurable:!1,enumerable:!0,get:E})},h.n=function(T){var k=T&&T.__esModule?function(){return T.default}:function(){return T};return h.d(k,"a",k),k},h.o=function(T,k){return Object.prototype.hasOwnProperty.call(T,k)},h.p="",h(h.s=109)}([function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(17),k=h(18),E=h(19),p=h(45),m=h(46),c=h(47),o=h(48),e=h(49),t=h(12),a=h(32),l=h(33),u=h(31),i=h(1),r={Scope:i.Scope,create:i.create,find:i.find,query:i.query,register:i.register,Container:T.default,Format:k.default,Leaf:E.default,Embed:o.default,Scroll:p.default,Block:c.default,Inline:m.default,Text:e.default,Attributor:{Attribute:t.default,Class:a.default,Style:l.default,Store:u.default}};d.default=r},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,r){i.__proto__=r}||function(i,r){for(var s in r)r.hasOwnProperty(s)&&(i[s]=r[s])};return function(i,r){u(i,r);function s(){this.constructor=i}i.prototype=r===null?Object.create(r):(s.prototype=r.prototype,new s)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=function(u){T(i,u);function i(r){var s=this;return r="[Parchment] "+r,s=u.call(this,r)||this,s.message=r,s.name=s.constructor.name,s}return i}(Error);d.ParchmentError=k;var E={},p={},m={},c={};d.DATA_KEY="__blot";var o;(function(u){u[u.TYPE=3]="TYPE",u[u.LEVEL=12]="LEVEL",u[u.ATTRIBUTE=13]="ATTRIBUTE",u[u.BLOT=14]="BLOT",u[u.INLINE=7]="INLINE",u[u.BLOCK=11]="BLOCK",u[u.BLOCK_BLOT=10]="BLOCK_BLOT",u[u.INLINE_BLOT=6]="INLINE_BLOT",u[u.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",u[u.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",u[u.ANY=15]="ANY"})(o=d.Scope||(d.Scope={}));function e(u,i){var r=a(u);if(r==null)throw new k("Unable to create "+u+" blot");var s=r,n=u instanceof Node||u.nodeType===Node.TEXT_NODE?u:s.create(i);return new s(n,i)}d.create=e;function t(u,i){return i===void 0&&(i=!1),u==null?null:u[d.DATA_KEY]!=null?u[d.DATA_KEY].blot:i?t(u.parentNode,i):null}d.find=t;function a(u,i){i===void 0&&(i=o.ANY);var r;if(typeof u=="string")r=c[u]||E[u];else if(u instanceof Text||u.nodeType===Node.TEXT_NODE)r=c.text;else if(typeof u=="number")u&o.LEVEL&o.BLOCK?r=c.block:u&o.LEVEL&o.INLINE&&(r=c.inline);else if(u instanceof HTMLElement){var s=(u.getAttribute("class")||"").split(/\s+/);for(var n in s)if(r=p[s[n]],r)break;r=r||m[u.tagName]}return r==null?null:i&o.LEVEL&r.scope&&i&o.TYPE&r.scope?r:null}d.query=a;function l(){for(var u=[],i=0;i<arguments.length;i++)u[i]=arguments[i];if(u.length>1)return u.map(function(n){return l(n)});var r=u[0];if(typeof r.blotName!="string"&&typeof r.attrName!="string")throw new k("Invalid definition");if(r.blotName==="abstract")throw new k("Cannot register abstract class");if(c[r.blotName||r.attrName]=r,typeof r.keyName=="string")E[r.keyName]=r;else if(r.className!=null&&(p[r.className]=r),r.tagName!=null){Array.isArray(r.tagName)?r.tagName=r.tagName.map(function(n){return n.toUpperCase()}):r.tagName=r.tagName.toUpperCase();var s=Array.isArray(r.tagName)?r.tagName:[r.tagName];s.forEach(function(n){(m[n]==null||r.className==null)&&(m[n]=r)})}return r}d.register=l},function(I,d,h){var T=h(51),k=h(11),E=h(3),p=h(20),m="\0",c=function(o){Array.isArray(o)?this.ops=o:o!=null&&Array.isArray(o.ops)?this.ops=o.ops:this.ops=[]};c.prototype.insert=function(o,e){var t={};return o.length===0?this:(t.insert=o,e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t))},c.prototype.delete=function(o){return o<=0?this:this.push({delete:o})},c.prototype.retain=function(o,e){if(o<=0)return this;var t={retain:o};return e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t)},c.prototype.push=function(o){var e=this.ops.length,t=this.ops[e-1];if(o=E(!0,{},o),typeof t=="object"){if(typeof o.delete=="number"&&typeof t.delete=="number")return this.ops[e-1]={delete:t.delete+o.delete},this;if(typeof t.delete=="number"&&o.insert!=null&&(e-=1,t=this.ops[e-1],typeof t!="object"))return this.ops.unshift(o),this;if(k(o.attributes,t.attributes)){if(typeof o.insert=="string"&&typeof t.insert=="string")return this.ops[e-1]={insert:t.insert+o.insert},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this;if(typeof o.retain=="number"&&typeof t.retain=="number")return this.ops[e-1]={retain:t.retain+o.retain},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this}}return e===this.ops.length?this.ops.push(o):this.ops.splice(e,0,o),this},c.prototype.chop=function(){var o=this.ops[this.ops.length-1];return o&&o.retain&&!o.attributes&&this.ops.pop(),this},c.prototype.filter=function(o){return this.ops.filter(o)},c.prototype.forEach=function(o){this.ops.forEach(o)},c.prototype.map=function(o){return this.ops.map(o)},c.prototype.partition=function(o){var e=[],t=[];return this.forEach(function(a){var l=o(a)?e:t;l.push(a)}),[e,t]},c.prototype.reduce=function(o,e){return this.ops.reduce(o,e)},c.prototype.changeLength=function(){return this.reduce(function(o,e){return e.insert?o+p.length(e):e.delete?o-e.delete:o},0)},c.prototype.length=function(){return this.reduce(function(o,e){return o+p.length(e)},0)},c.prototype.slice=function(o,e){o=o||0,typeof e!="number"&&(e=1/0);for(var t=[],a=p.iterator(this.ops),l=0;l<e&&a.hasNext();){var u;l<o?u=a.next(o-l):(u=a.next(e-l),t.push(u)),l+=p.length(u)}return new c(t)},c.prototype.compose=function(o){var e=p.iterator(this.ops),t=p.iterator(o.ops),a=[],l=t.peek();if(l!=null&&typeof l.retain=="number"&&l.attributes==null){for(var u=l.retain;e.peekType()==="insert"&&e.peekLength()<=u;)u-=e.peekLength(),a.push(e.next());l.retain-u>0&&t.next(l.retain-u)}for(var i=new c(a);e.hasNext()||t.hasNext();)if(t.peekType()==="insert")i.push(t.next());else if(e.peekType()==="delete")i.push(e.next());else{var r=Math.min(e.peekLength(),t.peekLength()),s=e.next(r),n=t.next(r);if(typeof n.retain=="number"){var f={};typeof s.retain=="number"?f.retain=r:f.insert=s.insert;var w=p.attributes.compose(s.attributes,n.attributes,typeof s.retain=="number");if(w&&(f.attributes=w),i.push(f),!t.hasNext()&&k(i.ops[i.ops.length-1],f)){var b=new c(e.rest());return i.concat(b).chop()}}else typeof n.delete=="number"&&typeof s.retain=="number"&&i.push(n)}return i.chop()},c.prototype.concat=function(o){var e=new c(this.ops.slice());return o.ops.length>0&&(e.push(o.ops[0]),e.ops=e.ops.concat(o.ops.slice(1))),e},c.prototype.diff=function(o,e){if(this.ops===o.ops)return new c;var t=[this,o].map(function(r){return r.map(function(s){if(s.insert!=null)return typeof s.insert=="string"?s.insert:m;var n=r===o?"on":"with";throw new Error("diff() called "+n+" non-document")}).join("")}),a=new c,l=T(t[0],t[1],e),u=p.iterator(this.ops),i=p.iterator(o.ops);return l.forEach(function(r){for(var s=r[1].length;s>0;){var n=0;switch(r[0]){case T.INSERT:n=Math.min(i.peekLength(),s),a.push(i.next(n));break;case T.DELETE:n=Math.min(s,u.peekLength()),u.next(n),a.delete(n);break;case T.EQUAL:n=Math.min(u.peekLength(),i.peekLength(),s);var f=u.next(n),w=i.next(n);k(f.insert,w.insert)?a.retain(n,p.attributes.diff(f.attributes,w.attributes)):a.push(w).delete(n);break}s-=n}}),a.chop()},c.prototype.eachLine=function(o,e){e=e||`
`;for(var t=p.iterator(this.ops),a=new c,l=0;t.hasNext();){if(t.peekType()!=="insert")return;var u=t.peek(),i=p.length(u)-t.peekLength(),r=typeof u.insert=="string"?u.insert.indexOf(e,i)-i:-1;if(r<0)a.push(t.next());else if(r>0)a.push(t.next(r));else{if(o(a,t.next(1).attributes||{},l)===!1)return;l+=1,a=new c}}a.length()>0&&o(a,{},l)},c.prototype.transform=function(o,e){if(e=!!e,typeof o=="number")return this.transformPosition(o,e);for(var t=p.iterator(this.ops),a=p.iterator(o.ops),l=new c;t.hasNext()||a.hasNext();)if(t.peekType()==="insert"&&(e||a.peekType()!=="insert"))l.retain(p.length(t.next()));else if(a.peekType()==="insert")l.push(a.next());else{var u=Math.min(t.peekLength(),a.peekLength()),i=t.next(u),r=a.next(u);if(i.delete)continue;r.delete?l.push(r):l.retain(u,p.attributes.transform(i.attributes,r.attributes,e))}return l.chop()},c.prototype.transformPosition=function(o,e){e=!!e;for(var t=p.iterator(this.ops),a=0;t.hasNext()&&a<=o;){var l=t.peekLength(),u=t.peekType();if(t.next(),u==="delete"){o-=Math.min(l,o-a);continue}else u==="insert"&&(a<o||!e)&&(o+=l);a+=l}return o},I.exports=c},function(I,d){"use strict";var h=Object.prototype.hasOwnProperty,T=Object.prototype.toString,k=Object.defineProperty,E=Object.getOwnPropertyDescriptor,p=function(t){return typeof Array.isArray=="function"?Array.isArray(t):T.call(t)==="[object Array]"},m=function(t){if(!t||T.call(t)!=="[object Object]")return!1;var a=h.call(t,"constructor"),l=t.constructor&&t.constructor.prototype&&h.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!a&&!l)return!1;var u;for(u in t);return typeof u>"u"||h.call(t,u)},c=function(t,a){k&&a.name==="__proto__"?k(t,a.name,{enumerable:!0,configurable:!0,value:a.newValue,writable:!0}):t[a.name]=a.newValue},o=function(t,a){if(a==="__proto__")if(h.call(t,a)){if(E)return E(t,a).value}else return;return t[a]};I.exports=function e(){var t,a,l,u,i,r,s=arguments[0],n=1,f=arguments.length,w=!1;for(typeof s=="boolean"&&(w=s,s=arguments[1]||{},n=2),(s==null||typeof s!="object"&&typeof s!="function")&&(s={});n<f;++n)if(t=arguments[n],t!=null)for(a in t)l=o(s,a),u=o(t,a),s!==u&&(w&&u&&(m(u)||(i=p(u)))?(i?(i=!1,r=l&&p(l)?l:[]):r=l&&m(l)?l:{},c(s,{name:a,newValue:e(w,r,u)})):typeof u<"u"&&c(s,{name:a,newValue:u}));return s}},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.BlockEmbed=d.bubbleFormats=void 0;var T=function(){function v(y,A){for(var x=0;x<A.length;x++){var L=A[x];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(y,L.key,L)}}return function(y,A,x){return A&&v(y.prototype,A),x&&v(y,x),y}}(),k=function v(y,A,x){y===null&&(y=Function.prototype);var L=Object.getOwnPropertyDescriptor(y,A);if(L===void 0){var B=Object.getPrototypeOf(y);return B===null?void 0:v(B,A,x)}else{if("value"in L)return L.value;var F=L.get;return F===void 0?void 0:F.call(x)}},E=h(3),p=s(E),m=h(2),c=s(m),o=h(0),e=s(o),t=h(16),a=s(t),l=h(6),u=s(l),i=h(7),r=s(i);function s(v){return v&&v.__esModule?v:{default:v}}function n(v,y){if(!(v instanceof y))throw new TypeError("Cannot call a class as a function")}function f(v,y){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return y&&(typeof y=="object"||typeof y=="function")?y:v}function w(v,y){if(typeof y!="function"&&y!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof y);v.prototype=Object.create(y&&y.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),y&&(Object.setPrototypeOf?Object.setPrototypeOf(v,y):v.__proto__=y)}var b=1,_=function(v){w(y,v);function y(){return n(this,y),f(this,(y.__proto__||Object.getPrototypeOf(y)).apply(this,arguments))}return T(y,[{key:"attach",value:function(){k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"attach",this).call(this),this.attributes=new e.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new c.default().insert(this.value(),(0,p.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(x,L){var B=e.default.query(x,e.default.Scope.BLOCK_ATTRIBUTE);B!=null&&this.attributes.attribute(B,L)}},{key:"formatAt",value:function(x,L,B,F){this.format(B,F)}},{key:"insertAt",value:function(x,L,B){if(typeof L=="string"&&L.endsWith(`
`)){var F=e.default.create(N.blotName);this.parent.insertBefore(F,x===0?this:this.next),F.insertAt(0,L.slice(0,-1))}else k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"insertAt",this).call(this,x,L,B)}}]),y}(e.default.Embed);_.scope=e.default.Scope.BLOCK_BLOT;var N=function(v){w(y,v);function y(A){n(this,y);var x=f(this,(y.__proto__||Object.getPrototypeOf(y)).call(this,A));return x.cache={},x}return T(y,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(e.default.Leaf).reduce(function(x,L){return L.length()===0?x:x.insert(L.value(),g(L))},new c.default).insert(`
`,g(this))),this.cache.delta}},{key:"deleteAt",value:function(x,L){k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"deleteAt",this).call(this,x,L),this.cache={}}},{key:"formatAt",value:function(x,L,B,F){L<=0||(e.default.query(B,e.default.Scope.BLOCK)?x+L===this.length()&&this.format(B,F):k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"formatAt",this).call(this,x,Math.min(L,this.length()-x-1),B,F),this.cache={})}},{key:"insertAt",value:function(x,L,B){if(B!=null)return k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"insertAt",this).call(this,x,L,B);if(L.length!==0){var F=L.split(`
`),V=F.shift();V.length>0&&(x<this.length()-1||this.children.tail==null?k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"insertAt",this).call(this,Math.min(x,this.length()-1),V):this.children.tail.insertAt(this.children.tail.length(),V),this.cache={});var M=this;F.reduce(function(R,O){return M=M.split(R,!0),M.insertAt(0,O),O.length},x+V.length)}}},{key:"insertBefore",value:function(x,L){var B=this.children.head;k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"insertBefore",this).call(this,x,L),B instanceof a.default&&B.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"length",this).call(this)+b),this.cache.length}},{key:"moveChildren",value:function(x,L){k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"moveChildren",this).call(this,x,L),this.cache={}}},{key:"optimize",value:function(x){k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"optimize",this).call(this,x),this.cache={}}},{key:"path",value:function(x){return k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"path",this).call(this,x,!0)}},{key:"removeChild",value:function(x){k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"removeChild",this).call(this,x),this.cache={}}},{key:"split",value:function(x){var L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(L&&(x===0||x>=this.length()-b)){var B=this.clone();return x===0?(this.parent.insertBefore(B,this),this):(this.parent.insertBefore(B,this.next),B)}else{var F=k(y.prototype.__proto__||Object.getPrototypeOf(y.prototype),"split",this).call(this,x,L);return this.cache={},F}}}]),y}(e.default.Block);N.blotName="block",N.tagName="P",N.defaultChild="break",N.allowedChildren=[u.default,e.default.Embed,r.default];function g(v){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return v==null||(typeof v.formats=="function"&&(y=(0,p.default)(y,v.formats())),v.parent==null||v.parent.blotName=="scroll"||v.parent.statics.scope!==v.statics.scope)?y:g(v.parent,y)}d.bubbleFormats=g,d.BlockEmbed=_,d.default=N},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.overload=d.expandConfig=void 0;var T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(M){return typeof M}:function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},k=function(){function M(R,O){var P=[],q=!0,C=!1,D=void 0;try{for(var S=R[Symbol.iterator](),j;!(q=(j=S.next()).done)&&(P.push(j.value),!(O&&P.length===O));q=!0);}catch(U){C=!0,D=U}finally{try{!q&&S.return&&S.return()}finally{if(C)throw D}}return P}return function(R,O){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return M(R,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),E=function(){function M(R,O){for(var P=0;P<O.length;P++){var q=O[P];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(R,q.key,q)}}return function(R,O,P){return O&&M(R.prototype,O),P&&M(R,P),R}}();h(50);var p=h(2),m=g(p),c=h(14),o=g(c),e=h(8),t=g(e),a=h(9),l=g(a),u=h(0),i=g(u),r=h(15),s=g(r),n=h(3),f=g(n),w=h(10),b=g(w),_=h(34),N=g(_);function g(M){return M&&M.__esModule?M:{default:M}}function v(M,R,O){return R in M?Object.defineProperty(M,R,{value:O,enumerable:!0,configurable:!0,writable:!0}):M[R]=O,M}function y(M,R){if(!(M instanceof R))throw new TypeError("Cannot call a class as a function")}var A=(0,b.default)("quill"),x=function(){E(M,null,[{key:"debug",value:function(O){O===!0&&(O="log"),b.default.level(O)}},{key:"find",value:function(O){return O.__quill||i.default.find(O)}},{key:"import",value:function(O){return this.imports[O]==null&&A.error("Cannot import "+O+". Are you sure it was registered?"),this.imports[O]}},{key:"register",value:function(O,P){var q=this,C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof O!="string"){var D=O.attrName||O.blotName;typeof D=="string"?this.register("formats/"+D,O,P):Object.keys(O).forEach(function(S){q.register(S,O[S],P)})}else this.imports[O]!=null&&!C&&A.warn("Overwriting "+O+" with",P),this.imports[O]=P,(O.startsWith("blots/")||O.startsWith("formats/"))&&P.blotName!=="abstract"?i.default.register(P):O.startsWith("modules")&&typeof P.register=="function"&&P.register()}}]);function M(R){var O=this,P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(y(this,M),this.options=L(R,P),this.container=this.options.container,this.container==null)return A.error("Invalid Quill container",R);this.options.debug&&M.debug(this.options.debug);var q=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new t.default,this.scroll=i.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new o.default(this.scroll),this.selection=new s.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(t.default.events.EDITOR_CHANGE,function(D){D===t.default.events.TEXT_CHANGE&&O.root.classList.toggle("ql-blank",O.editor.isBlank())}),this.emitter.on(t.default.events.SCROLL_UPDATE,function(D,S){var j=O.selection.lastRange,U=j&&j.length===0?j.index:void 0;B.call(O,function(){return O.editor.update(null,S,U)},D)});var C=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+q+"<p><br></p></div>");this.setContents(C),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return E(M,[{key:"addContainer",value:function(O){var P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof O=="string"){var q=O;O=document.createElement("div"),O.classList.add(q)}return this.container.insertBefore(O,P),O}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(O,P,q){var C=this,D=F(O,P,q),S=k(D,4);return O=S[0],P=S[1],q=S[3],B.call(this,function(){return C.editor.deleteText(O,P)},q,O,-1*P)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(O),this.container.classList.toggle("ql-disabled",!O)}},{key:"focus",value:function(){var O=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=O,this.scrollIntoView()}},{key:"format",value:function(O,P){var q=this,C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t.default.sources.API;return B.call(this,function(){var D=q.getSelection(!0),S=new m.default;if(D==null)return S;if(i.default.query(O,i.default.Scope.BLOCK))S=q.editor.formatLine(D.index,D.length,v({},O,P));else{if(D.length===0)return q.selection.format(O,P),S;S=q.editor.formatText(D.index,D.length,v({},O,P))}return q.setSelection(D,t.default.sources.SILENT),S},C)}},{key:"formatLine",value:function(O,P,q,C,D){var S=this,j=void 0,U=F(O,P,q,C,D),H=k(U,4);return O=H[0],P=H[1],j=H[2],D=H[3],B.call(this,function(){return S.editor.formatLine(O,P,j)},D,O,0)}},{key:"formatText",value:function(O,P,q,C,D){var S=this,j=void 0,U=F(O,P,q,C,D),H=k(U,4);return O=H[0],P=H[1],j=H[2],D=H[3],B.call(this,function(){return S.editor.formatText(O,P,j)},D,O,0)}},{key:"getBounds",value:function(O){var P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,q=void 0;typeof O=="number"?q=this.selection.getBounds(O,P):q=this.selection.getBounds(O.index,O.length);var C=this.container.getBoundingClientRect();return{bottom:q.bottom-C.top,height:q.height,left:q.left-C.left,right:q.right-C.left,top:q.top-C.top,width:q.width}}},{key:"getContents",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-O,q=F(O,P),C=k(q,2);return O=C[0],P=C[1],this.editor.getContents(O,P)}},{key:"getFormat",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof O=="number"?this.editor.getFormat(O,P):this.editor.getFormat(O.index,O.length)}},{key:"getIndex",value:function(O){return O.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(O){return this.scroll.leaf(O)}},{key:"getLine",value:function(O){return this.scroll.line(O)}},{key:"getLines",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof O!="number"?this.scroll.lines(O.index,O.length):this.scroll.lines(O,P)}},{key:"getModule",value:function(O){return this.theme.modules[O]}},{key:"getSelection",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return O&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-O,q=F(O,P),C=k(q,2);return O=C[0],P=C[1],this.editor.getText(O,P)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(O,P,q){var C=this,D=arguments.length>3&&arguments[3]!==void 0?arguments[3]:M.sources.API;return B.call(this,function(){return C.editor.insertEmbed(O,P,q)},D,O)}},{key:"insertText",value:function(O,P,q,C,D){var S=this,j=void 0,U=F(O,0,q,C,D),H=k(U,4);return O=H[0],j=H[2],D=H[3],B.call(this,function(){return S.editor.insertText(O,P,j)},D,O,P.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(O,P,q){this.clipboard.dangerouslyPasteHTML(O,P,q)}},{key:"removeFormat",value:function(O,P,q){var C=this,D=F(O,P,q),S=k(D,4);return O=S[0],P=S[1],q=S[3],B.call(this,function(){return C.editor.removeFormat(O,P)},q,O)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(O){var P=this,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return B.call(this,function(){O=new m.default(O);var C=P.getLength(),D=P.editor.deleteText(0,C),S=P.editor.applyDelta(O),j=S.ops[S.ops.length-1];j!=null&&typeof j.insert=="string"&&j.insert[j.insert.length-1]===`
`&&(P.editor.deleteText(P.getLength()-1,1),S.delete(1));var U=D.compose(S);return U},q)}},{key:"setSelection",value:function(O,P,q){if(O==null)this.selection.setRange(null,P||M.sources.API);else{var C=F(O,P,q),D=k(C,4);O=D[0],P=D[1],q=D[3],this.selection.setRange(new r.Range(O,P),q),q!==t.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(O){var P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API,q=new m.default().insert(O);return this.setContents(q,P)}},{key:"update",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:t.default.sources.USER,P=this.scroll.update(O);return this.selection.update(O),P}},{key:"updateContents",value:function(O){var P=this,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return B.call(this,function(){return O=new m.default(O),P.editor.applyDelta(O,q)},q,!0)}}]),M}();x.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},x.events=t.default.events,x.sources=t.default.sources,x.version="1.3.7",x.imports={delta:m.default,parchment:i.default,"core/module":l.default,"core/theme":N.default};function L(M,R){if(R=(0,f.default)(!0,{container:M,modules:{clipboard:!0,keyboard:!0,history:!0}},R),!R.theme||R.theme===x.DEFAULTS.theme)R.theme=N.default;else if(R.theme=x.import("themes/"+R.theme),R.theme==null)throw new Error("Invalid theme "+R.theme+". Did you register it?");var O=(0,f.default)(!0,{},R.theme.DEFAULTS);[O,R].forEach(function(C){C.modules=C.modules||{},Object.keys(C.modules).forEach(function(D){C.modules[D]===!0&&(C.modules[D]={})})});var P=Object.keys(O.modules).concat(Object.keys(R.modules)),q=P.reduce(function(C,D){var S=x.import("modules/"+D);return S==null?A.error("Cannot load "+D+" module. Are you sure you registered it?"):C[D]=S.DEFAULTS||{},C},{});return R.modules!=null&&R.modules.toolbar&&R.modules.toolbar.constructor!==Object&&(R.modules.toolbar={container:R.modules.toolbar}),R=(0,f.default)(!0,{},x.DEFAULTS,{modules:q},O,R),["bounds","container","scrollingContainer"].forEach(function(C){typeof R[C]=="string"&&(R[C]=document.querySelector(R[C]))}),R.modules=Object.keys(R.modules).reduce(function(C,D){return R.modules[D]&&(C[D]=R.modules[D]),C},{}),R}function B(M,R,O,P){if(this.options.strict&&!this.isEnabled()&&R===t.default.sources.USER)return new m.default;var q=O==null?null:this.getSelection(),C=this.editor.delta,D=M();if(q!=null&&(O===!0&&(O=q.index),P==null?q=V(q,D,R):P!==0&&(q=V(q,O,P,R)),this.setSelection(q,t.default.sources.SILENT)),D.length()>0){var S,j=[t.default.events.TEXT_CHANGE,D,C,R];if((S=this.emitter).emit.apply(S,[t.default.events.EDITOR_CHANGE].concat(j)),R!==t.default.sources.SILENT){var U;(U=this.emitter).emit.apply(U,j)}}return D}function F(M,R,O,P,q){var C={};return typeof M.index=="number"&&typeof M.length=="number"?typeof R!="number"?(q=P,P=O,O=R,R=M.length,M=M.index):(R=M.length,M=M.index):typeof R!="number"&&(q=P,P=O,O=R,R=0),(typeof O>"u"?"undefined":T(O))==="object"?(C=O,q=P):typeof O=="string"&&(P!=null?C[O]=P:q=O),q=q||t.default.sources.API,[M,R,C,q]}function V(M,R,O,P){if(M==null)return null;var q=void 0,C=void 0;if(R instanceof m.default){var D=[M.index,M.index+M.length].map(function(H){return R.transformPosition(H,P!==t.default.sources.USER)}),S=k(D,2);q=S[0],C=S[1]}else{var j=[M.index,M.index+M.length].map(function(H){return H<R||H===R&&P===t.default.sources.USER?H:O>=0?H+O:Math.max(R,H+O)}),U=k(j,2);q=U[0],C=U[1]}return new r.Range(q,C-q)}d.expandConfig=L,d.overload=F,d.default=x},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function u(i,r){for(var s=0;s<r.length;s++){var n=r[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,r,s){return r&&u(i.prototype,r),s&&u(i,s),i}}(),k=function u(i,r,s){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,r);if(n===void 0){var f=Object.getPrototypeOf(i);return f===null?void 0:u(f,r,s)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(s)}},E=h(7),p=o(E),m=h(0),c=o(m);function o(u){return u&&u.__esModule?u:{default:u}}function e(u,i){if(!(u instanceof i))throw new TypeError("Cannot call a class as a function")}function t(u,i){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:u}function a(u,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);u.prototype=Object.create(i&&i.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(u,i):u.__proto__=i)}var l=function(u){a(i,u);function i(){return e(this,i),t(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return T(i,[{key:"formatAt",value:function(s,n,f,w){if(i.compare(this.statics.blotName,f)<0&&c.default.query(f,c.default.Scope.BLOT)){var b=this.isolate(s,n);w&&b.wrap(f,w)}else k(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"formatAt",this).call(this,s,n,f,w)}},{key:"optimize",value:function(s){if(k(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"optimize",this).call(this,s),this.parent instanceof i&&i.compare(this.statics.blotName,this.parent.statics.blotName)>0){var n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}],[{key:"compare",value:function(s,n){var f=i.order.indexOf(s),w=i.order.indexOf(n);return f>=0||w>=0?f-w:s===n?0:s<n?-1:1}}]),i}(c.default.Inline);l.allowedChildren=[l,c.default.Embed,p.default],l.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],d.default=l},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(0),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function c(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){c(t,e);function t(){return p(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(k.default.Text);d.default=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function r(s,n){for(var f=0;f<n.length;f++){var w=n[f];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(s,w.key,w)}}return function(s,n,f){return n&&r(s.prototype,n),f&&r(s,f),s}}(),k=function r(s,n,f){s===null&&(s=Function.prototype);var w=Object.getOwnPropertyDescriptor(s,n);if(w===void 0){var b=Object.getPrototypeOf(s);return b===null?void 0:r(b,n,f)}else{if("value"in w)return w.value;var _=w.get;return _===void 0?void 0:_.call(f)}},E=h(54),p=o(E),m=h(10),c=o(m);function o(r){return r&&r.__esModule?r:{default:r}}function e(r,s){if(!(r instanceof s))throw new TypeError("Cannot call a class as a function")}function t(r,s){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:r}function a(r,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);r.prototype=Object.create(s&&s.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(r,s):r.__proto__=s)}var l=(0,c.default)("quill:events"),u=["selectionchange","mousedown","mouseup","click"];u.forEach(function(r){document.addEventListener(r,function(){for(var s=arguments.length,n=Array(s),f=0;f<s;f++)n[f]=arguments[f];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(w){if(w.__quill&&w.__quill.emitter){var b;(b=w.__quill.emitter).handleDOM.apply(b,n)}})})});var i=function(r){a(s,r);function s(){e(this,s);var n=t(this,(s.__proto__||Object.getPrototypeOf(s)).call(this));return n.listeners={},n.on("error",l.error),n}return T(s,[{key:"emit",value:function(){l.log.apply(l,arguments),k(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(f){for(var w=arguments.length,b=Array(w>1?w-1:0),_=1;_<w;_++)b[_-1]=arguments[_];(this.listeners[f.type]||[]).forEach(function(N){var g=N.node,v=N.handler;(f.target===g||g.contains(f.target))&&v.apply(void 0,[f].concat(b))})}},{key:"listenDOM",value:function(f,w,b){this.listeners[f]||(this.listeners[f]=[]),this.listeners[f].push({node:w,handler:b})}}]),s}(p.default);i.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},i.sources={API:"api",SILENT:"silent",USER:"user"},d.default=i},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});function T(E,p){if(!(E instanceof p))throw new TypeError("Cannot call a class as a function")}var k=function E(p){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};T(this,E),this.quill=p,this.options=m};k.DEFAULTS={},d.default=k},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=["error","warn","log","info"],k="warn";function E(m){if(T.indexOf(m)<=T.indexOf(k)){for(var c,o=arguments.length,e=Array(o>1?o-1:0),t=1;t<o;t++)e[t-1]=arguments[t];(c=console)[m].apply(c,e)}}function p(m){return T.reduce(function(c,o){return c[o]=E.bind(console,o,m),c},{})}E.level=p.level=function(m){k=m},d.default=p},function(I,d,h){var T=Array.prototype.slice,k=h(52),E=h(53),p=I.exports=function(e,t,a){return a||(a={}),e===t?!0:e instanceof Date&&t instanceof Date?e.getTime()===t.getTime():!e||!t||typeof e!="object"&&typeof t!="object"?a.strict?e===t:e==t:o(e,t,a)};function m(e){return e==null}function c(e){return!(!e||typeof e!="object"||typeof e.length!="number"||typeof e.copy!="function"||typeof e.slice!="function"||e.length>0&&typeof e[0]!="number")}function o(e,t,a){var l,u;if(m(e)||m(t)||e.prototype!==t.prototype)return!1;if(E(e))return E(t)?(e=T.call(e),t=T.call(t),p(e,t,a)):!1;if(c(e)){if(!c(t)||e.length!==t.length)return!1;for(l=0;l<e.length;l++)if(e[l]!==t[l])return!1;return!0}try{var i=k(e),r=k(t)}catch{return!1}if(i.length!=r.length)return!1;for(i.sort(),r.sort(),l=i.length-1;l>=0;l--)if(i[l]!=r[l])return!1;for(l=i.length-1;l>=0;l--)if(u=i[l],!p(e[u],t[u],a))return!1;return typeof e==typeof t}},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(1),k=function(){function E(p,m,c){c===void 0&&(c={}),this.attrName=p,this.keyName=m;var o=T.Scope.TYPE&T.Scope.ATTRIBUTE;c.scope!=null?this.scope=c.scope&T.Scope.LEVEL|o:this.scope=T.Scope.ATTRIBUTE,c.whitelist!=null&&(this.whitelist=c.whitelist)}return E.keys=function(p){return[].map.call(p.attributes,function(m){return m.name})},E.prototype.add=function(p,m){return this.canAdd(p,m)?(p.setAttribute(this.keyName,m),!0):!1},E.prototype.canAdd=function(p,m){var c=T.query(p,T.Scope.BLOT&(this.scope|T.Scope.TYPE));return c==null?!1:this.whitelist==null?!0:typeof m=="string"?this.whitelist.indexOf(m.replace(/["']/g,""))>-1:this.whitelist.indexOf(m)>-1},E.prototype.remove=function(p){p.removeAttribute(this.keyName)},E.prototype.value=function(p){var m=p.getAttribute(this.keyName);return this.canAdd(p,m)&&m?m:""},E}();d.default=k},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.Code=void 0;var T=function(){function _(N,g){var v=[],y=!0,A=!1,x=void 0;try{for(var L=N[Symbol.iterator](),B;!(y=(B=L.next()).done)&&(v.push(B.value),!(g&&v.length===g));y=!0);}catch(F){A=!0,x=F}finally{try{!y&&L.return&&L.return()}finally{if(A)throw x}}return v}return function(N,g){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return _(N,g);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function _(N,g){for(var v=0;v<g.length;v++){var y=g[v];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(N,y.key,y)}}return function(N,g,v){return g&&_(N.prototype,g),v&&_(N,v),N}}(),E=function _(N,g,v){N===null&&(N=Function.prototype);var y=Object.getOwnPropertyDescriptor(N,g);if(y===void 0){var A=Object.getPrototypeOf(N);return A===null?void 0:_(A,g,v)}else{if("value"in y)return y.value;var x=y.get;return x===void 0?void 0:x.call(v)}},p=h(2),m=r(p),c=h(0),o=r(c),e=h(4),t=r(e),a=h(6),l=r(a),u=h(7),i=r(u);function r(_){return _&&_.__esModule?_:{default:_}}function s(_,N){if(!(_ instanceof N))throw new TypeError("Cannot call a class as a function")}function n(_,N){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return N&&(typeof N=="object"||typeof N=="function")?N:_}function f(_,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof N);_.prototype=Object.create(N&&N.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),N&&(Object.setPrototypeOf?Object.setPrototypeOf(_,N):_.__proto__=N)}var w=function(_){f(N,_);function N(){return s(this,N),n(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return N}(l.default);w.blotName="code",w.tagName="CODE";var b=function(_){f(N,_);function N(){return s(this,N),n(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return k(N,[{key:"delta",value:function(){var v=this,y=this.domNode.textContent;return y.endsWith(`
`)&&(y=y.slice(0,-1)),y.split(`
`).reduce(function(A,x){return A.insert(x).insert(`
`,v.formats())},new m.default)}},{key:"format",value:function(v,y){if(!(v===this.statics.blotName&&y)){var A=this.descendant(i.default,this.length()-1),x=T(A,1),L=x[0];L?.deleteAt(L.length()-1,1),E(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"format",this).call(this,v,y)}}},{key:"formatAt",value:function(v,y,A,x){if(y!==0&&!(o.default.query(A,o.default.Scope.BLOCK)==null||A===this.statics.blotName&&x===this.statics.formats(this.domNode))){var L=this.newlineIndex(v);if(!(L<0||L>=v+y)){var B=this.newlineIndex(v,!0)+1,F=L-B+1,V=this.isolate(B,F),M=V.next;V.format(A,x),M instanceof N&&M.formatAt(0,v-B+y-F,A,x)}}}},{key:"insertAt",value:function(v,y,A){if(A==null){var x=this.descendant(i.default,v),L=T(x,2),B=L[0],F=L[1];B.insertAt(F,y)}}},{key:"length",value:function(){var v=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?v:v+1}},{key:"newlineIndex",value:function(v){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(y)return this.domNode.textContent.slice(0,v).lastIndexOf(`
`);var A=this.domNode.textContent.slice(v).indexOf(`
`);return A>-1?v+A:-1}},{key:"optimize",value:function(v){this.domNode.textContent.endsWith(`
`)||this.appendChild(o.default.create("text",`
`)),E(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"optimize",this).call(this,v);var y=this.next;y!=null&&y.prev===this&&y.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===y.statics.formats(y.domNode)&&(y.optimize(v),y.moveChildren(this),y.remove())}},{key:"replace",value:function(v){E(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"replace",this).call(this,v),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(y){var A=o.default.find(y);A==null?y.parentNode.removeChild(y):A instanceof o.default.Embed?A.remove():A.unwrap()})}}],[{key:"create",value:function(v){var y=E(N.__proto__||Object.getPrototypeOf(N),"create",this).call(this,v);return y.setAttribute("spellcheck",!1),y}},{key:"formats",value:function(){return!0}}]),N}(t.default);b.blotName="code-block",b.tagName="PRE",b.TAB="  ",d.Code=w,d.default=b},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(M){return typeof M}:function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},k=function(){function M(R,O){var P=[],q=!0,C=!1,D=void 0;try{for(var S=R[Symbol.iterator](),j;!(q=(j=S.next()).done)&&(P.push(j.value),!(O&&P.length===O));q=!0);}catch(U){C=!0,D=U}finally{try{!q&&S.return&&S.return()}finally{if(C)throw D}}return P}return function(R,O){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return M(R,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),E=function(){function M(R,O){for(var P=0;P<O.length;P++){var q=O[P];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(R,q.key,q)}}return function(R,O,P){return O&&M(R.prototype,O),P&&M(R,P),R}}(),p=h(2),m=y(p),c=h(20),o=y(c),e=h(0),t=y(e),a=h(13),l=y(a),u=h(24),i=y(u),r=h(4),s=y(r),n=h(16),f=y(n),w=h(21),b=y(w),_=h(11),N=y(_),g=h(3),v=y(g);function y(M){return M&&M.__esModule?M:{default:M}}function A(M,R,O){return R in M?Object.defineProperty(M,R,{value:O,enumerable:!0,configurable:!0,writable:!0}):M[R]=O,M}function x(M,R){if(!(M instanceof R))throw new TypeError("Cannot call a class as a function")}var L=/^[ -~]*$/,B=function(){function M(R){x(this,M),this.scroll=R,this.delta=this.getDelta()}return E(M,[{key:"applyDelta",value:function(O){var P=this,q=!1;this.scroll.update();var C=this.scroll.length();return this.scroll.batchStart(),O=V(O),O.reduce(function(D,S){var j=S.retain||S.delete||S.insert.length||1,U=S.attributes||{};if(S.insert!=null){if(typeof S.insert=="string"){var H=S.insert;H.endsWith(`
`)&&q&&(q=!1,H=H.slice(0,-1)),D>=C&&!H.endsWith(`
`)&&(q=!0),P.scroll.insertAt(D,H);var $=P.scroll.line(D),Y=k($,2),X=Y[0],Q=Y[1],nt=(0,v.default)({},(0,r.bubbleFormats)(X));if(X instanceof s.default){var rt=X.descendant(t.default.Leaf,Q),at=k(rt,1),lt=at[0];nt=(0,v.default)(nt,(0,r.bubbleFormats)(lt))}U=o.default.attributes.diff(nt,U)||{}}else if(T(S.insert)==="object"){var z=Object.keys(S.insert)[0];if(z==null)return D;P.scroll.insertAt(D,z,S.insert[z])}C+=j}return Object.keys(U).forEach(function(K){P.scroll.formatAt(D,j,K,U[K])}),D+j},0),O.reduce(function(D,S){return typeof S.delete=="number"?(P.scroll.deleteAt(D,S.delete),D):D+(S.retain||S.insert.length||1)},0),this.scroll.batchEnd(),this.update(O)}},{key:"deleteText",value:function(O,P){return this.scroll.deleteAt(O,P),this.update(new m.default().retain(O).delete(P))}},{key:"formatLine",value:function(O,P){var q=this,C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(C).forEach(function(D){if(!(q.scroll.whitelist!=null&&!q.scroll.whitelist[D])){var S=q.scroll.lines(O,Math.max(P,1)),j=P;S.forEach(function(U){var H=U.length();if(!(U instanceof l.default))U.format(D,C[D]);else{var $=O-U.offset(q.scroll),Y=U.newlineIndex($+j)-$+1;U.formatAt($,Y,D,C[D])}j-=H})}}),this.scroll.optimize(),this.update(new m.default().retain(O).retain(P,(0,b.default)(C)))}},{key:"formatText",value:function(O,P){var q=this,C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(C).forEach(function(D){q.scroll.formatAt(O,P,D,C[D])}),this.update(new m.default().retain(O).retain(P,(0,b.default)(C)))}},{key:"getContents",value:function(O,P){return this.delta.slice(O,O+P)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(O,P){return O.concat(P.delta())},new m.default)}},{key:"getFormat",value:function(O){var P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,q=[],C=[];P===0?this.scroll.path(O).forEach(function(S){var j=k(S,1),U=j[0];U instanceof s.default?q.push(U):U instanceof t.default.Leaf&&C.push(U)}):(q=this.scroll.lines(O,P),C=this.scroll.descendants(t.default.Leaf,O,P));var D=[q,C].map(function(S){if(S.length===0)return{};for(var j=(0,r.bubbleFormats)(S.shift());Object.keys(j).length>0;){var U=S.shift();if(U==null)return j;j=F((0,r.bubbleFormats)(U),j)}return j});return v.default.apply(v.default,D)}},{key:"getText",value:function(O,P){return this.getContents(O,P).filter(function(q){return typeof q.insert=="string"}).map(function(q){return q.insert}).join("")}},{key:"insertEmbed",value:function(O,P,q){return this.scroll.insertAt(O,P,q),this.update(new m.default().retain(O).insert(A({},P,q)))}},{key:"insertText",value:function(O,P){var q=this,C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return P=P.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(O,P),Object.keys(C).forEach(function(D){q.scroll.formatAt(O,P.length,D,C[D])}),this.update(new m.default().retain(O).insert(P,(0,b.default)(C)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var O=this.scroll.children.head;return O.statics.blotName!==s.default.blotName||O.children.length>1?!1:O.children.head instanceof f.default}},{key:"removeFormat",value:function(O,P){var q=this.getText(O,P),C=this.scroll.line(O+P),D=k(C,2),S=D[0],j=D[1],U=0,H=new m.default;S!=null&&(S instanceof l.default?U=S.newlineIndex(j)-j+1:U=S.length()-j,H=S.delta().slice(j,j+U-1).insert(`
`));var $=this.getContents(O,P+U),Y=$.diff(new m.default().insert(q).concat(H)),X=new m.default().retain(O).concat(Y);return this.applyDelta(X)}},{key:"update",value:function(O){var P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,C=this.delta;if(P.length===1&&P[0].type==="characterData"&&P[0].target.data.match(L)&&t.default.find(P[0].target)){var D=t.default.find(P[0].target),S=(0,r.bubbleFormats)(D),j=D.offset(this.scroll),U=P[0].oldValue.replace(i.default.CONTENTS,""),H=new m.default().insert(U),$=new m.default().insert(D.value()),Y=new m.default().retain(j).concat(H.diff($,q));O=Y.reduce(function(X,Q){return Q.insert?X.insert(Q.insert,S):X.push(Q)},new m.default),this.delta=C.compose(O)}else this.delta=this.getDelta(),(!O||!(0,N.default)(C.compose(O),this.delta))&&(O=C.diff(this.delta,q));return O}}]),M}();function F(M,R){return Object.keys(R).reduce(function(O,P){return M[P]==null||(R[P]===M[P]?O[P]=R[P]:Array.isArray(R[P])?R[P].indexOf(M[P])<0&&(O[P]=R[P].concat([M[P]])):O[P]=[R[P],M[P]]),O},{})}function V(M){return M.reduce(function(R,O){if(O.insert===1){var P=(0,b.default)(O.attributes);return delete P.image,R.insert({image:O.attributes.image},P)}if(O.attributes!=null&&(O.attributes.list===!0||O.attributes.bullet===!0)&&(O=(0,b.default)(O),O.attributes.list?O.attributes.list="ordered":(O.attributes.list="bullet",delete O.attributes.bullet)),typeof O.insert=="string"){var q=O.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return R.insert(q,O.attributes)}return R.push(O)},new m.default)}d.default=B},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.Range=void 0;var T=function(){function _(N,g){var v=[],y=!0,A=!1,x=void 0;try{for(var L=N[Symbol.iterator](),B;!(y=(B=L.next()).done)&&(v.push(B.value),!(g&&v.length===g));y=!0);}catch(F){A=!0,x=F}finally{try{!y&&L.return&&L.return()}finally{if(A)throw x}}return v}return function(N,g){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return _(N,g);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function _(N,g){for(var v=0;v<g.length;v++){var y=g[v];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(N,y.key,y)}}return function(N,g,v){return g&&_(N.prototype,g),v&&_(N,v),N}}(),E=h(0),p=i(E),m=h(21),c=i(m),o=h(11),e=i(o),t=h(8),a=i(t),l=h(10),u=i(l);function i(_){return _&&_.__esModule?_:{default:_}}function r(_){if(Array.isArray(_)){for(var N=0,g=Array(_.length);N<_.length;N++)g[N]=_[N];return g}else return Array.from(_)}function s(_,N){if(!(_ instanceof N))throw new TypeError("Cannot call a class as a function")}var n=(0,u.default)("quill:selection"),f=function _(N){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;s(this,_),this.index=N,this.length=g},w=function(){function _(N,g){var v=this;s(this,_),this.emitter=g,this.scroll=N,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=p.default.create("cursor",this),this.lastRange=this.savedRange=new f(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){v.mouseDown||setTimeout(v.update.bind(v,a.default.sources.USER),1)}),this.emitter.on(a.default.events.EDITOR_CHANGE,function(y,A){y===a.default.events.TEXT_CHANGE&&A.length()>0&&v.update(a.default.sources.SILENT)}),this.emitter.on(a.default.events.SCROLL_BEFORE_UPDATE,function(){if(v.hasFocus()){var y=v.getNativeRange();y!=null&&y.start.node!==v.cursor.textNode&&v.emitter.once(a.default.events.SCROLL_UPDATE,function(){try{v.setNativeRange(y.start.node,y.start.offset,y.end.node,y.end.offset)}catch{}})}}),this.emitter.on(a.default.events.SCROLL_OPTIMIZE,function(y,A){if(A.range){var x=A.range,L=x.startNode,B=x.startOffset,F=x.endNode,V=x.endOffset;v.setNativeRange(L,B,F,V)}}),this.update(a.default.sources.SILENT)}return k(_,[{key:"handleComposition",value:function(){var g=this;this.root.addEventListener("compositionstart",function(){g.composing=!0}),this.root.addEventListener("compositionend",function(){if(g.composing=!1,g.cursor.parent){var v=g.cursor.restore();if(!v)return;setTimeout(function(){g.setNativeRange(v.startNode,v.startOffset,v.endNode,v.endOffset)},1)}})}},{key:"handleDragging",value:function(){var g=this;this.emitter.listenDOM("mousedown",document.body,function(){g.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){g.mouseDown=!1,g.update(a.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(g,v){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[g])){this.scroll.update();var y=this.getNativeRange();if(!(y==null||!y.native.collapsed||p.default.query(g,p.default.Scope.BLOCK))){if(y.start.node!==this.cursor.textNode){var A=p.default.find(y.start.node,!1);if(A==null)return;if(A instanceof p.default.Leaf){var x=A.split(y.start.offset);A.parent.insertBefore(this.cursor,x)}else A.insertBefore(this.cursor,y.start.node);this.cursor.attach()}this.cursor.format(g,v),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(g){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,y=this.scroll.length();g=Math.min(g,y-1),v=Math.min(g+v,y-1)-g;var A=void 0,x=this.scroll.leaf(g),L=T(x,2),B=L[0],F=L[1];if(B==null)return null;var V=B.position(F,!0),M=T(V,2);A=M[0],F=M[1];var R=document.createRange();if(v>0){R.setStart(A,F);var O=this.scroll.leaf(g+v),P=T(O,2);if(B=P[0],F=P[1],B==null)return null;var q=B.position(F,!0),C=T(q,2);return A=C[0],F=C[1],R.setEnd(A,F),R.getBoundingClientRect()}else{var D="left",S=void 0;return A instanceof Text?(F<A.data.length?(R.setStart(A,F),R.setEnd(A,F+1)):(R.setStart(A,F-1),R.setEnd(A,F),D="right"),S=R.getBoundingClientRect()):(S=B.domNode.getBoundingClientRect(),F>0&&(D="right")),{bottom:S.top+S.height,height:S.height,left:S[D],right:S[D],top:S.top,width:0}}}},{key:"getNativeRange",value:function(){var g=document.getSelection();if(g==null||g.rangeCount<=0)return null;var v=g.getRangeAt(0);if(v==null)return null;var y=this.normalizeNative(v);return n.info("getNativeRange",y),y}},{key:"getRange",value:function(){var g=this.getNativeRange();if(g==null)return[null,null];var v=this.normalizedToRange(g);return[v,g]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(g){var v=this,y=[[g.start.node,g.start.offset]];g.native.collapsed||y.push([g.end.node,g.end.offset]);var A=y.map(function(B){var F=T(B,2),V=F[0],M=F[1],R=p.default.find(V,!0),O=R.offset(v.scroll);return M===0?O:R instanceof p.default.Container?O+R.length():O+R.index(V,M)}),x=Math.min(Math.max.apply(Math,r(A)),this.scroll.length()-1),L=Math.min.apply(Math,[x].concat(r(A)));return new f(L,x-L)}},{key:"normalizeNative",value:function(g){if(!b(this.root,g.startContainer)||!g.collapsed&&!b(this.root,g.endContainer))return null;var v={start:{node:g.startContainer,offset:g.startOffset},end:{node:g.endContainer,offset:g.endOffset},native:g};return[v.start,v.end].forEach(function(y){for(var A=y.node,x=y.offset;!(A instanceof Text)&&A.childNodes.length>0;)if(A.childNodes.length>x)A=A.childNodes[x],x=0;else if(A.childNodes.length===x)A=A.lastChild,x=A instanceof Text?A.data.length:A.childNodes.length+1;else break;y.node=A,y.offset=x}),v}},{key:"rangeToNative",value:function(g){var v=this,y=g.collapsed?[g.index]:[g.index,g.index+g.length],A=[],x=this.scroll.length();return y.forEach(function(L,B){L=Math.min(x-1,L);var F=void 0,V=v.scroll.leaf(L),M=T(V,2),R=M[0],O=M[1],P=R.position(O,B!==0),q=T(P,2);F=q[0],O=q[1],A.push(F,O)}),A.length<2&&(A=A.concat(A)),A}},{key:"scrollIntoView",value:function(g){var v=this.lastRange;if(v!=null){var y=this.getBounds(v.index,v.length);if(y!=null){var A=this.scroll.length()-1,x=this.scroll.line(Math.min(v.index,A)),L=T(x,1),B=L[0],F=B;if(v.length>0){var V=this.scroll.line(Math.min(v.index+v.length,A)),M=T(V,1);F=M[0]}if(!(B==null||F==null)){var R=g.getBoundingClientRect();y.top<R.top?g.scrollTop-=R.top-y.top:y.bottom>R.bottom&&(g.scrollTop+=y.bottom-R.bottom)}}}}},{key:"setNativeRange",value:function(g,v){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:g,A=arguments.length>3&&arguments[3]!==void 0?arguments[3]:v,x=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(n.info("setNativeRange",g,v,y,A),!(g!=null&&(this.root.parentNode==null||g.parentNode==null||y.parentNode==null))){var L=document.getSelection();if(L!=null)if(g!=null){this.hasFocus()||this.root.focus();var B=(this.getNativeRange()||{}).native;if(B==null||x||g!==B.startContainer||v!==B.startOffset||y!==B.endContainer||A!==B.endOffset){g.tagName=="BR"&&(v=[].indexOf.call(g.parentNode.childNodes,g),g=g.parentNode),y.tagName=="BR"&&(A=[].indexOf.call(y.parentNode.childNodes,y),y=y.parentNode);var F=document.createRange();F.setStart(g,v),F.setEnd(y,A),L.removeAllRanges(),L.addRange(F)}}else L.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(g){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:a.default.sources.API;if(typeof v=="string"&&(y=v,v=!1),n.info("setRange",g),g!=null){var A=this.rangeToNative(g);this.setNativeRange.apply(this,r(A).concat([v]))}else this.setNativeRange(null);this.update(y)}},{key:"update",value:function(){var g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:a.default.sources.USER,v=this.lastRange,y=this.getRange(),A=T(y,2),x=A[0],L=A[1];if(this.lastRange=x,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,e.default)(v,this.lastRange)){var B;!this.composing&&L!=null&&L.native.collapsed&&L.start.node!==this.cursor.textNode&&this.cursor.restore();var F=[a.default.events.SELECTION_CHANGE,(0,c.default)(this.lastRange),(0,c.default)(v),g];if((B=this.emitter).emit.apply(B,[a.default.events.EDITOR_CHANGE].concat(F)),g!==a.default.sources.SILENT){var V;(V=this.emitter).emit.apply(V,F)}}}}]),_}();function b(_,N){try{N.parentNode}catch{return!1}return N instanceof Text&&(N=N.parentNode),_.contains(N)}d.Range=f,d.default=w},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function a(l,u){for(var i=0;i<u.length;i++){var r=u[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(l,r.key,r)}}return function(l,u,i){return u&&a(l.prototype,u),i&&a(l,i),l}}(),k=function a(l,u,i){l===null&&(l=Function.prototype);var r=Object.getOwnPropertyDescriptor(l,u);if(r===void 0){var s=Object.getPrototypeOf(l);return s===null?void 0:a(s,u,i)}else{if("value"in r)return r.value;var n=r.get;return n===void 0?void 0:n.call(i)}},E=h(0),p=m(E);function m(a){return a&&a.__esModule?a:{default:a}}function c(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function o(a,l){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:a}function e(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(a,l):a.__proto__=l)}var t=function(a){e(l,a);function l(){return c(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return T(l,[{key:"insertInto",value:function(i,r){i.children.length===0?k(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"insertInto",this).call(this,i,r):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),l}(p.default.Embed);t.blotName="break",t.tagName="BR",d.default=t},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])};return function(e,t){o(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(44),E=h(30),p=h(1),m=function(o){T(e,o);function e(t){var a=o.call(this,t)||this;return a.build(),a}return e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){o.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},e.prototype.build=function(){var t=this;this.children=new k.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(a){try{var l=c(a);t.insertBefore(l,t.children.head||void 0)}catch(u){if(u instanceof p.ParchmentError)return;throw u}})},e.prototype.deleteAt=function(t,a){if(t===0&&a===this.length())return this.remove();this.children.forEachAt(t,a,function(l,u,i){l.deleteAt(u,i)})},e.prototype.descendant=function(t,a){var l=this.children.find(a),u=l[0],i=l[1];return t.blotName==null&&t(u)||t.blotName!=null&&u instanceof t?[u,i]:u instanceof e?u.descendant(t,i):[null,-1]},e.prototype.descendants=function(t,a,l){a===void 0&&(a=0),l===void 0&&(l=Number.MAX_VALUE);var u=[],i=l;return this.children.forEachAt(a,l,function(r,s,n){(t.blotName==null&&t(r)||t.blotName!=null&&r instanceof t)&&u.push(r),r instanceof e&&(u=u.concat(r.descendants(t,s,i))),i-=n}),u},e.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),o.prototype.detach.call(this)},e.prototype.formatAt=function(t,a,l,u){this.children.forEachAt(t,a,function(i,r,s){i.formatAt(r,s,l,u)})},e.prototype.insertAt=function(t,a,l){var u=this.children.find(t),i=u[0],r=u[1];if(i)i.insertAt(r,a,l);else{var s=l==null?p.create("text",a):p.create(a,l);this.appendChild(s)}},e.prototype.insertBefore=function(t,a){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(l){return t instanceof l}))throw new p.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,a)},e.prototype.length=function(){return this.children.reduce(function(t,a){return t+a.length()},0)},e.prototype.moveChildren=function(t,a){this.children.forEach(function(l){t.insertBefore(l,a)})},e.prototype.optimize=function(t){if(o.prototype.optimize.call(this,t),this.children.length===0)if(this.statics.defaultChild!=null){var a=p.create(this.statics.defaultChild);this.appendChild(a),a.optimize(t)}else this.remove()},e.prototype.path=function(t,a){a===void 0&&(a=!1);var l=this.children.find(t,a),u=l[0],i=l[1],r=[[this,t]];return u instanceof e?r.concat(u.path(i,a)):(u!=null&&r.push([u,i]),r)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replace=function(t){t instanceof e&&t.moveChildren(this),o.prototype.replace.call(this,t)},e.prototype.split=function(t,a){if(a===void 0&&(a=!1),!a){if(t===0)return this;if(t===this.length())return this.next}var l=this.clone();return this.parent.insertBefore(l,this.next),this.children.forEachAt(t,this.length(),function(u,i,r){u=u.split(i,a),l.appendChild(u)}),l},e.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},e.prototype.update=function(t,a){var l=this,u=[],i=[];t.forEach(function(r){r.target===l.domNode&&r.type==="childList"&&(u.push.apply(u,r.addedNodes),i.push.apply(i,r.removedNodes))}),i.forEach(function(r){if(!(r.parentNode!=null&&r.tagName!=="IFRAME"&&document.body.compareDocumentPosition(r)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var s=p.find(r);s!=null&&(s.domNode.parentNode==null||s.domNode.parentNode===l.domNode)&&s.detach()}}),u.filter(function(r){return r.parentNode==l.domNode}).sort(function(r,s){return r===s?0:r.compareDocumentPosition(s)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(r){var s=null;r.nextSibling!=null&&(s=p.find(r.nextSibling));var n=c(r);(n.next!=s||n.next==null)&&(n.parent!=null&&n.parent.removeChild(l),l.insertBefore(n,s||void 0))})},e}(E.default);function c(o){var e=p.find(o);if(e==null)try{e=p.create(o)}catch{e=p.create(p.Scope.INLINE),[].slice.call(o.childNodes).forEach(function(a){e.domNode.appendChild(a)}),o.parentNode&&o.parentNode.replaceChild(e.domNode,o),e.attach()}return e}d.default=m},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])};return function(e,t){o(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(12),E=h(31),p=h(17),m=h(1),c=function(o){T(e,o);function e(t){var a=o.call(this,t)||this;return a.attributes=new E.default(a.domNode),a}return e.formats=function(t){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()},e.prototype.format=function(t,a){var l=m.query(t);l instanceof k.default?this.attributes.attribute(l,a):a&&l!=null&&(t!==this.statics.blotName||this.formats()[t]!==a)&&this.replaceWith(t,a)},e.prototype.formats=function(){var t=this.attributes.values(),a=this.statics.formats(this.domNode);return a!=null&&(t[this.statics.blotName]=a),t},e.prototype.replaceWith=function(t,a){var l=o.prototype.replaceWith.call(this,t,a);return this.attributes.copy(l),l},e.prototype.update=function(t,a){var l=this;o.prototype.update.call(this,t,a),t.some(function(u){return u.target===l.domNode&&u.type==="attributes"})&&this.attributes.build()},e.prototype.wrap=function(t,a){var l=o.prototype.wrap.call(this,t,a);return l instanceof e&&l.statics.scope===this.statics.scope&&this.attributes.move(l),l},e}(p.default);d.default=c},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var e in o)o.hasOwnProperty(e)&&(c[e]=o[e])};return function(c,o){m(c,o);function e(){this.constructor=c}c.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(30),E=h(1),p=function(m){T(c,m);function c(){return m!==null&&m.apply(this,arguments)||this}return c.value=function(o){return!0},c.prototype.index=function(o,e){return this.domNode===o||this.domNode.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},c.prototype.position=function(o,e){var t=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return o>0&&(t+=1),[this.parent.domNode,t]},c.prototype.value=function(){var o;return o={},o[this.statics.blotName]=this.statics.value(this.domNode)||!0,o},c.scope=E.Scope.INLINE_BLOT,c}(k.default);d.default=p},function(I,d,h){var T=h(11),k=h(3),E={attributes:{compose:function(m,c,o){typeof m!="object"&&(m={}),typeof c!="object"&&(c={});var e=k(!0,{},c);o||(e=Object.keys(e).reduce(function(a,l){return e[l]!=null&&(a[l]=e[l]),a},{}));for(var t in m)m[t]!==void 0&&c[t]===void 0&&(e[t]=m[t]);return Object.keys(e).length>0?e:void 0},diff:function(m,c){typeof m!="object"&&(m={}),typeof c!="object"&&(c={});var o=Object.keys(m).concat(Object.keys(c)).reduce(function(e,t){return T(m[t],c[t])||(e[t]=c[t]===void 0?null:c[t]),e},{});return Object.keys(o).length>0?o:void 0},transform:function(m,c,o){if(typeof m!="object")return c;if(typeof c=="object"){if(!o)return c;var e=Object.keys(c).reduce(function(t,a){return m[a]===void 0&&(t[a]=c[a]),t},{});return Object.keys(e).length>0?e:void 0}}},iterator:function(m){return new p(m)},length:function(m){return typeof m.delete=="number"?m.delete:typeof m.retain=="number"?m.retain:typeof m.insert=="string"?m.insert.length:1}};function p(m){this.ops=m,this.index=0,this.offset=0}p.prototype.hasNext=function(){return this.peekLength()<1/0},p.prototype.next=function(m){m||(m=1/0);var c=this.ops[this.index];if(c){var o=this.offset,e=E.length(c);if(m>=e-o?(m=e-o,this.index+=1,this.offset=0):this.offset+=m,typeof c.delete=="number")return{delete:m};var t={};return c.attributes&&(t.attributes=c.attributes),typeof c.retain=="number"?t.retain=m:typeof c.insert=="string"?t.insert=c.insert.substr(o,m):t.insert=c.insert,t}else return{retain:1/0}},p.prototype.peek=function(){return this.ops[this.index]},p.prototype.peekLength=function(){return this.ops[this.index]?E.length(this.ops[this.index])-this.offset:1/0},p.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},p.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var m=this.offset,c=this.index,o=this.next(),e=this.ops.slice(this.index);return this.offset=m,this.index=c,[o].concat(e)}else return[]},I.exports=E},function(I,d){var h=function(){"use strict";function T(l,u){return u!=null&&l instanceof u}var k;try{k=Map}catch{k=function(){}}var E;try{E=Set}catch{E=function(){}}var p;try{p=Promise}catch{p=function(){}}function m(l,u,i,r,s){typeof u=="object"&&(i=u.depth,r=u.prototype,s=u.includeNonEnumerable,u=u.circular);var n=[],f=[],w=typeof Buffer<"u";typeof u>"u"&&(u=!0),typeof i>"u"&&(i=1/0);function b(_,N){if(_===null)return null;if(N===0)return _;var g,v;if(typeof _!="object")return _;if(T(_,k))g=new k;else if(T(_,E))g=new E;else if(T(_,p))g=new p(function(R,O){_.then(function(P){R(b(P,N-1))},function(P){O(b(P,N-1))})});else if(m.__isArray(_))g=[];else if(m.__isRegExp(_))g=new RegExp(_.source,a(_)),_.lastIndex&&(g.lastIndex=_.lastIndex);else if(m.__isDate(_))g=new Date(_.getTime());else{if(w&&Buffer.isBuffer(_))return Buffer.allocUnsafe?g=Buffer.allocUnsafe(_.length):g=new Buffer(_.length),_.copy(g),g;T(_,Error)?g=Object.create(_):typeof r>"u"?(v=Object.getPrototypeOf(_),g=Object.create(v)):(g=Object.create(r),v=r)}if(u){var y=n.indexOf(_);if(y!=-1)return f[y];n.push(_),f.push(g)}T(_,k)&&_.forEach(function(R,O){var P=b(O,N-1),q=b(R,N-1);g.set(P,q)}),T(_,E)&&_.forEach(function(R){var O=b(R,N-1);g.add(O)});for(var A in _){var x;v&&(x=Object.getOwnPropertyDescriptor(v,A)),!(x&&x.set==null)&&(g[A]=b(_[A],N-1))}if(Object.getOwnPropertySymbols)for(var L=Object.getOwnPropertySymbols(_),A=0;A<L.length;A++){var B=L[A],F=Object.getOwnPropertyDescriptor(_,B);F&&!F.enumerable&&!s||(g[B]=b(_[B],N-1),F.enumerable||Object.defineProperty(g,B,{enumerable:!1}))}if(s)for(var V=Object.getOwnPropertyNames(_),A=0;A<V.length;A++){var M=V[A],F=Object.getOwnPropertyDescriptor(_,M);F&&F.enumerable||(g[M]=b(_[M],N-1),Object.defineProperty(g,M,{enumerable:!1}))}return g}return b(l,i)}m.clonePrototype=function(u){if(u===null)return null;var i=function(){};return i.prototype=u,new i};function c(l){return Object.prototype.toString.call(l)}m.__objToStr=c;function o(l){return typeof l=="object"&&c(l)==="[object Date]"}m.__isDate=o;function e(l){return typeof l=="object"&&c(l)==="[object Array]"}m.__isArray=e;function t(l){return typeof l=="object"&&c(l)==="[object RegExp]"}m.__isRegExp=t;function a(l){var u="";return l.global&&(u+="g"),l.ignoreCase&&(u+="i"),l.multiline&&(u+="m"),u}return m.__getRegExpFlags=a,m}();typeof I=="object"&&I.exports&&(I.exports=h)},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function g(v,y){var A=[],x=!0,L=!1,B=void 0;try{for(var F=v[Symbol.iterator](),V;!(x=(V=F.next()).done)&&(A.push(V.value),!(y&&A.length===y));x=!0);}catch(M){L=!0,B=M}finally{try{!x&&F.return&&F.return()}finally{if(L)throw B}}return A}return function(v,y){if(Array.isArray(v))return v;if(Symbol.iterator in Object(v))return g(v,y);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function g(v,y){for(var A=0;A<y.length;A++){var x=y[A];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(v,x.key,x)}}return function(v,y,A){return y&&g(v.prototype,y),A&&g(v,A),v}}(),E=function g(v,y,A){v===null&&(v=Function.prototype);var x=Object.getOwnPropertyDescriptor(v,y);if(x===void 0){var L=Object.getPrototypeOf(v);return L===null?void 0:g(L,y,A)}else{if("value"in x)return x.value;var B=x.get;return B===void 0?void 0:B.call(A)}},p=h(0),m=n(p),c=h(8),o=n(c),e=h(4),t=n(e),a=h(16),l=n(a),u=h(13),i=n(u),r=h(25),s=n(r);function n(g){return g&&g.__esModule?g:{default:g}}function f(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}function w(g,v){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:g}function b(g,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);g.prototype=Object.create(v&&v.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(g,v):g.__proto__=v)}function _(g){return g instanceof t.default||g instanceof e.BlockEmbed}var N=function(g){b(v,g);function v(y,A){f(this,v);var x=w(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,y));return x.emitter=A.emitter,Array.isArray(A.whitelist)&&(x.whitelist=A.whitelist.reduce(function(L,B){return L[B]=!0,L},{})),x.domNode.addEventListener("DOMNodeInserted",function(){}),x.optimize(),x.enable(),x}return k(v,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(A,x){var L=this.line(A),B=T(L,2),F=B[0],V=B[1],M=this.line(A+x),R=T(M,1),O=R[0];if(E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"deleteAt",this).call(this,A,x),O!=null&&F!==O&&V>0){if(F instanceof e.BlockEmbed||O instanceof e.BlockEmbed){this.optimize();return}if(F instanceof i.default){var P=F.newlineIndex(F.length(),!0);if(P>-1&&(F=F.split(P+1),F===O)){this.optimize();return}}else if(O instanceof i.default){var q=O.newlineIndex(0);q>-1&&O.split(q+1)}var C=O.children.head instanceof l.default?null:O.children.head;F.moveChildren(O,C),F.remove()}this.optimize()}},{key:"enable",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",A)}},{key:"formatAt",value:function(A,x,L,B){this.whitelist!=null&&!this.whitelist[L]||(E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"formatAt",this).call(this,A,x,L,B),this.optimize())}},{key:"insertAt",value:function(A,x,L){if(!(L!=null&&this.whitelist!=null&&!this.whitelist[x])){if(A>=this.length())if(L==null||m.default.query(x,m.default.Scope.BLOCK)==null){var B=m.default.create(this.statics.defaultChild);this.appendChild(B),L==null&&x.endsWith(`
`)&&(x=x.slice(0,-1)),B.insertAt(0,x,L)}else{var F=m.default.create(x,L);this.appendChild(F)}else E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertAt",this).call(this,A,x,L);this.optimize()}}},{key:"insertBefore",value:function(A,x){if(A.statics.scope===m.default.Scope.INLINE_BLOT){var L=m.default.create(this.statics.defaultChild);L.appendChild(A),A=L}E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertBefore",this).call(this,A,x)}},{key:"leaf",value:function(A){return this.path(A).pop()||[null,-1]}},{key:"line",value:function(A){return A===this.length()?this.line(A-1):this.descendant(_,A)}},{key:"lines",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,L=function B(F,V,M){var R=[],O=M;return F.children.forEachAt(V,M,function(P,q,C){_(P)?R.push(P):P instanceof m.default.Container&&(R=R.concat(B(P,q,O))),O-=C}),R};return L(this,A,x)}},{key:"optimize",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"optimize",this).call(this,A,x),A.length>0&&this.emitter.emit(o.default.events.SCROLL_OPTIMIZE,A,x))}},{key:"path",value:function(A){return E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"path",this).call(this,A).slice(1)}},{key:"update",value:function(A){if(this.batch!==!0){var x=o.default.sources.USER;typeof A=="string"&&(x=A),Array.isArray(A)||(A=this.observer.takeRecords()),A.length>0&&this.emitter.emit(o.default.events.SCROLL_BEFORE_UPDATE,x,A),E(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"update",this).call(this,A.concat([])),A.length>0&&this.emitter.emit(o.default.events.SCROLL_UPDATE,x,A)}}}]),v}(m.default.Scroll);N.blotName="scroll",N.className="ql-editor",N.tagName="DIV",N.defaultChild="block",N.allowedChildren=[t.default,e.BlockEmbed,s.default],d.default=N},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.SHORTKEY=d.default=void 0;var T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(S){return typeof S}:function(S){return S&&typeof Symbol=="function"&&S.constructor===Symbol&&S!==Symbol.prototype?"symbol":typeof S},k=function(){function S(j,U){var H=[],$=!0,Y=!1,X=void 0;try{for(var Q=j[Symbol.iterator](),nt;!($=(nt=Q.next()).done)&&(H.push(nt.value),!(U&&H.length===U));$=!0);}catch(rt){Y=!0,X=rt}finally{try{!$&&Q.return&&Q.return()}finally{if(Y)throw X}}return H}return function(j,U){if(Array.isArray(j))return j;if(Symbol.iterator in Object(j))return S(j,U);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),E=function(){function S(j,U){for(var H=0;H<U.length;H++){var $=U[H];$.enumerable=$.enumerable||!1,$.configurable=!0,"value"in $&&($.writable=!0),Object.defineProperty(j,$.key,$)}}return function(j,U,H){return U&&S(j.prototype,U),H&&S(j,H),j}}(),p=h(21),m=g(p),c=h(11),o=g(c),e=h(3),t=g(e),a=h(2),l=g(a),u=h(20),i=g(u),r=h(0),s=g(r),n=h(5),f=g(n),w=h(10),b=g(w),_=h(9),N=g(_);function g(S){return S&&S.__esModule?S:{default:S}}function v(S,j,U){return j in S?Object.defineProperty(S,j,{value:U,enumerable:!0,configurable:!0,writable:!0}):S[j]=U,S}function y(S,j){if(!(S instanceof j))throw new TypeError("Cannot call a class as a function")}function A(S,j){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return j&&(typeof j=="object"||typeof j=="function")?j:S}function x(S,j){if(typeof j!="function"&&j!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof j);S.prototype=Object.create(j&&j.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),j&&(Object.setPrototypeOf?Object.setPrototypeOf(S,j):S.__proto__=j)}var L=(0,b.default)("quill:keyboard"),B=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",F=function(S){x(j,S),E(j,null,[{key:"match",value:function(H,$){return $=D($),["altKey","ctrlKey","metaKey","shiftKey"].some(function(Y){return!!$[Y]!==H[Y]&&$[Y]!==null})?!1:$.key===(H.which||H.keyCode)}}]);function j(U,H){y(this,j);var $=A(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,U,H));return $.bindings={},Object.keys($.options.bindings).forEach(function(Y){Y==="list autofill"&&U.scroll.whitelist!=null&&!U.scroll.whitelist.list||$.options.bindings[Y]&&$.addBinding($.options.bindings[Y])}),$.addBinding({key:j.keys.ENTER,shiftKey:null},P),$.addBinding({key:j.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?($.addBinding({key:j.keys.BACKSPACE},{collapsed:!0},M),$.addBinding({key:j.keys.DELETE},{collapsed:!0},R)):($.addBinding({key:j.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},M),$.addBinding({key:j.keys.DELETE},{collapsed:!0,suffix:/^.?$/},R)),$.addBinding({key:j.keys.BACKSPACE},{collapsed:!1},O),$.addBinding({key:j.keys.DELETE},{collapsed:!1},O),$.addBinding({key:j.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},M),$.listen(),$}return E(j,[{key:"addBinding",value:function(H){var $=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},X=D(H);if(X==null||X.key==null)return L.warn("Attempted to add invalid keyboard binding",X);typeof $=="function"&&($={handler:$}),typeof Y=="function"&&(Y={handler:Y}),X=(0,t.default)(X,$,Y),this.bindings[X.key]=this.bindings[X.key]||[],this.bindings[X.key].push(X)}},{key:"listen",value:function(){var H=this;this.quill.root.addEventListener("keydown",function($){if(!$.defaultPrevented){var Y=$.which||$.keyCode,X=(H.bindings[Y]||[]).filter(function(ot){return j.match($,ot)});if(X.length!==0){var Q=H.quill.getSelection();if(!(Q==null||!H.quill.hasFocus())){var nt=H.quill.getLine(Q.index),rt=k(nt,2),at=rt[0],lt=rt[1],z=H.quill.getLeaf(Q.index),K=k(z,2),W=K[0],G=K[1],Z=Q.length===0?[W,G]:H.quill.getLeaf(Q.index+Q.length),J=k(Z,2),tt=J[0],et=J[1],ut=W instanceof s.default.Text?W.value().slice(0,G):"",st=tt instanceof s.default.Text?tt.value().slice(et):"",it={collapsed:Q.length===0,empty:Q.length===0&&at.length()<=1,format:H.quill.getFormat(Q),offset:lt,prefix:ut,suffix:st},Dt=X.some(function(ot){if(ot.collapsed!=null&&ot.collapsed!==it.collapsed||ot.empty!=null&&ot.empty!==it.empty||ot.offset!=null&&ot.offset!==it.offset)return!1;if(Array.isArray(ot.format)){if(ot.format.every(function(ct){return it.format[ct]==null}))return!1}else if(T(ot.format)==="object"&&!Object.keys(ot.format).every(function(ct){return ot.format[ct]===!0?it.format[ct]!=null:ot.format[ct]===!1?it.format[ct]==null:(0,o.default)(ot.format[ct],it.format[ct])}))return!1;return ot.prefix!=null&&!ot.prefix.test(it.prefix)||ot.suffix!=null&&!ot.suffix.test(it.suffix)?!1:ot.handler.call(H,Q,it)!==!0});Dt&&$.preventDefault()}}}})}}]),j}(N.default);F.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},F.DEFAULTS={bindings:{bold:C("bold"),italic:C("italic"),underline:C("underline"),indent:{key:F.keys.TAB,format:["blockquote","indent","list"],handler:function(j,U){if(U.collapsed&&U.offset!==0)return!0;this.quill.format("indent","+1",f.default.sources.USER)}},outdent:{key:F.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(j,U){if(U.collapsed&&U.offset!==0)return!0;this.quill.format("indent","-1",f.default.sources.USER)}},"outdent backspace":{key:F.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(j,U){U.format.indent!=null?this.quill.format("indent","-1",f.default.sources.USER):U.format.list!=null&&this.quill.format("list",!1,f.default.sources.USER)}},"indent code-block":q(!0),"outdent code-block":q(!1),"remove tab":{key:F.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(j){this.quill.deleteText(j.index-1,1,f.default.sources.USER)}},tab:{key:F.keys.TAB,handler:function(j){this.quill.history.cutoff();var U=new l.default().retain(j.index).delete(j.length).insert("	");this.quill.updateContents(U,f.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index+1,f.default.sources.SILENT)}},"list empty enter":{key:F.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(j,U){this.quill.format("list",!1,f.default.sources.USER),U.format.indent&&this.quill.format("indent",!1,f.default.sources.USER)}},"checklist enter":{key:F.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(j){var U=this.quill.getLine(j.index),H=k(U,2),$=H[0],Y=H[1],X=(0,t.default)({},$.formats(),{list:"checked"}),Q=new l.default().retain(j.index).insert(`
`,X).retain($.length()-Y-1).retain(1,{list:"unchecked"});this.quill.updateContents(Q,f.default.sources.USER),this.quill.setSelection(j.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:F.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(j,U){var H=this.quill.getLine(j.index),$=k(H,2),Y=$[0],X=$[1],Q=new l.default().retain(j.index).insert(`
`,U.format).retain(Y.length()-X-1).retain(1,{header:null});this.quill.updateContents(Q,f.default.sources.USER),this.quill.setSelection(j.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(j,U){var H=U.prefix.length,$=this.quill.getLine(j.index),Y=k($,2),X=Y[0],Q=Y[1];if(Q>H)return!0;var nt=void 0;switch(U.prefix.trim()){case"[]":case"[ ]":nt="unchecked";break;case"[x]":nt="checked";break;case"-":case"*":nt="bullet";break;default:nt="ordered"}this.quill.insertText(j.index," ",f.default.sources.USER),this.quill.history.cutoff();var rt=new l.default().retain(j.index-Q).delete(H+1).retain(X.length()-2-Q).retain(1,{list:nt});this.quill.updateContents(rt,f.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index-H,f.default.sources.SILENT)}},"code exit":{key:F.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(j){var U=this.quill.getLine(j.index),H=k(U,2),$=H[0],Y=H[1],X=new l.default().retain(j.index+$.length()-Y-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(X,f.default.sources.USER)}},"embed left":V(F.keys.LEFT,!1),"embed left shift":V(F.keys.LEFT,!0),"embed right":V(F.keys.RIGHT,!1),"embed right shift":V(F.keys.RIGHT,!0)}};function V(S,j){var U,H=S===F.keys.LEFT?"prefix":"suffix";return U={key:S,shiftKey:j,altKey:null},v(U,H,/^$/),v(U,"handler",function(Y){var X=Y.index;S===F.keys.RIGHT&&(X+=Y.length+1);var Q=this.quill.getLeaf(X),nt=k(Q,1),rt=nt[0];return rt instanceof s.default.Embed?(S===F.keys.LEFT?j?this.quill.setSelection(Y.index-1,Y.length+1,f.default.sources.USER):this.quill.setSelection(Y.index-1,f.default.sources.USER):j?this.quill.setSelection(Y.index,Y.length+1,f.default.sources.USER):this.quill.setSelection(Y.index+Y.length+1,f.default.sources.USER),!1):!0}),U}function M(S,j){if(!(S.index===0||this.quill.getLength()<=1)){var U=this.quill.getLine(S.index),H=k(U,1),$=H[0],Y={};if(j.offset===0){var X=this.quill.getLine(S.index-1),Q=k(X,1),nt=Q[0];if(nt!=null&&nt.length()>1){var rt=$.formats(),at=this.quill.getFormat(S.index-1,1);Y=i.default.attributes.diff(rt,at)||{}}}var lt=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(j.prefix)?2:1;this.quill.deleteText(S.index-lt,lt,f.default.sources.USER),Object.keys(Y).length>0&&this.quill.formatLine(S.index-lt,lt,Y,f.default.sources.USER),this.quill.focus()}}function R(S,j){var U=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(j.suffix)?2:1;if(!(S.index>=this.quill.getLength()-U)){var H={},$=0,Y=this.quill.getLine(S.index),X=k(Y,1),Q=X[0];if(j.offset>=Q.length()-1){var nt=this.quill.getLine(S.index+1),rt=k(nt,1),at=rt[0];if(at){var lt=Q.formats(),z=this.quill.getFormat(S.index,1);H=i.default.attributes.diff(lt,z)||{},$=at.length()}}this.quill.deleteText(S.index,U,f.default.sources.USER),Object.keys(H).length>0&&this.quill.formatLine(S.index+$-1,U,H,f.default.sources.USER)}}function O(S){var j=this.quill.getLines(S),U={};if(j.length>1){var H=j[0].formats(),$=j[j.length-1].formats();U=i.default.attributes.diff($,H)||{}}this.quill.deleteText(S,f.default.sources.USER),Object.keys(U).length>0&&this.quill.formatLine(S.index,1,U,f.default.sources.USER),this.quill.setSelection(S.index,f.default.sources.SILENT),this.quill.focus()}function P(S,j){var U=this;S.length>0&&this.quill.scroll.deleteAt(S.index,S.length);var H=Object.keys(j.format).reduce(function($,Y){return s.default.query(Y,s.default.Scope.BLOCK)&&!Array.isArray(j.format[Y])&&($[Y]=j.format[Y]),$},{});this.quill.insertText(S.index,`
`,H,f.default.sources.USER),this.quill.setSelection(S.index+1,f.default.sources.SILENT),this.quill.focus(),Object.keys(j.format).forEach(function($){H[$]==null&&(Array.isArray(j.format[$])||$!=="link"&&U.quill.format($,j.format[$],f.default.sources.USER))})}function q(S){return{key:F.keys.TAB,shiftKey:!S,format:{"code-block":!0},handler:function(U){var H=s.default.query("code-block"),$=U.index,Y=U.length,X=this.quill.scroll.descendant(H,$),Q=k(X,2),nt=Q[0],rt=Q[1];if(nt!=null){var at=this.quill.getIndex(nt),lt=nt.newlineIndex(rt,!0)+1,z=nt.newlineIndex(at+rt+Y),K=nt.domNode.textContent.slice(lt,z).split(`
`);rt=0,K.forEach(function(W,G){S?(nt.insertAt(lt+rt,H.TAB),rt+=H.TAB.length,G===0?$+=H.TAB.length:Y+=H.TAB.length):W.startsWith(H.TAB)&&(nt.deleteAt(lt+rt,H.TAB.length),rt-=H.TAB.length,G===0?$-=H.TAB.length:Y-=H.TAB.length),rt+=W.length+1}),this.quill.update(f.default.sources.USER),this.quill.setSelection($,Y,f.default.sources.SILENT)}}}}function C(S){return{key:S[0].toUpperCase(),shortKey:!0,handler:function(U,H){this.quill.format(S,!H.format[S],f.default.sources.USER)}}}function D(S){if(typeof S=="string"||typeof S=="number")return D({key:S});if((typeof S>"u"?"undefined":T(S))==="object"&&(S=(0,m.default)(S,!1)),typeof S.key=="string")if(F.keys[S.key.toUpperCase()]!=null)S.key=F.keys[S.key.toUpperCase()];else if(S.key.length===1)S.key=S.key.toUpperCase().charCodeAt(0);else return null;return S.shortKey&&(S[B]=S.shortKey,delete S.shortKey),S}d.default=F,d.SHORTKEY=B},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function i(r,s){var n=[],f=!0,w=!1,b=void 0;try{for(var _=r[Symbol.iterator](),N;!(f=(N=_.next()).done)&&(n.push(N.value),!(s&&n.length===s));f=!0);}catch(g){w=!0,b=g}finally{try{!f&&_.return&&_.return()}finally{if(w)throw b}}return n}return function(r,s){if(Array.isArray(r))return r;if(Symbol.iterator in Object(r))return i(r,s);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function i(r,s,n){r===null&&(r=Function.prototype);var f=Object.getOwnPropertyDescriptor(r,s);if(f===void 0){var w=Object.getPrototypeOf(r);return w===null?void 0:i(w,s,n)}else{if("value"in f)return f.value;var b=f.get;return b===void 0?void 0:b.call(n)}},E=function(){function i(r,s){for(var n=0;n<s.length;n++){var f=s[n];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(r,f.key,f)}}return function(r,s,n){return s&&i(r.prototype,s),n&&i(r,n),r}}(),p=h(0),m=e(p),c=h(7),o=e(c);function e(i){return i&&i.__esModule?i:{default:i}}function t(i,r){if(!(i instanceof r))throw new TypeError("Cannot call a class as a function")}function a(i,r){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:i}function l(i,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);i.prototype=Object.create(r&&r.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(i,r):i.__proto__=r)}var u=function(i){l(r,i),E(r,null,[{key:"value",value:function(){}}]);function r(s,n){t(this,r);var f=a(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,s));return f.selection=n,f.textNode=document.createTextNode(r.CONTENTS),f.domNode.appendChild(f.textNode),f._length=0,f}return E(r,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(n,f){if(this._length!==0)return k(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,n,f);for(var w=this,b=0;w!=null&&w.statics.scope!==m.default.Scope.BLOCK_BLOT;)b+=w.offset(w.parent),w=w.parent;w!=null&&(this._length=r.CONTENTS.length,w.optimize(),w.formatAt(b,r.CONTENTS.length,n,f),this._length=0)}},{key:"index",value:function(n,f){return n===this.textNode?0:k(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"index",this).call(this,n,f)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){k(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var n=this.textNode,f=this.selection.getNativeRange(),w=void 0,b=void 0,_=void 0;if(f!=null&&f.start.node===n&&f.end.node===n){var N=[n,f.start.offset,f.end.offset];w=N[0],b=N[1],_=N[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==r.CONTENTS){var g=this.textNode.data.split(r.CONTENTS).join("");this.next instanceof o.default?(w=this.next.domNode,this.next.insertAt(0,g),this.textNode.data=r.CONTENTS):(this.textNode.data=g,this.parent.insertBefore(m.default.create(this.textNode),this),this.textNode=document.createTextNode(r.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),b!=null){var v=[b,_].map(function(A){return Math.max(0,Math.min(w.data.length,A-1))}),y=T(v,2);return b=y[0],_=y[1],{startNode:w,startOffset:b,endNode:w,endOffset:_}}}}},{key:"update",value:function(n,f){var w=this;if(n.some(function(_){return _.type==="characterData"&&_.target===w.textNode})){var b=this.restore();b&&(f.range=b)}}},{key:"value",value:function(){return""}}]),r}(m.default.Embed);u.blotName="cursor",u.className="ql-cursor",u.tagName="span",u.CONTENTS="\uFEFF",d.default=u},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(0),k=m(T),E=h(4),p=m(E);function m(a){return a&&a.__esModule?a:{default:a}}function c(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function o(a,l){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:a}function e(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(a,l):a.__proto__=l)}var t=function(a){e(l,a);function l(){return c(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(k.default.Container);t.allowedChildren=[p.default,E.BlockEmbed,t],d.default=t},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.ColorStyle=d.ColorClass=d.ColorAttributor=void 0;var T=function(){function u(i,r){for(var s=0;s<r.length;s++){var n=r[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,r,s){return r&&u(i.prototype,r),s&&u(i,s),i}}(),k=function u(i,r,s){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,r);if(n===void 0){var f=Object.getPrototypeOf(i);return f===null?void 0:u(f,r,s)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(s)}},E=h(0),p=m(E);function m(u){return u&&u.__esModule?u:{default:u}}function c(u,i){if(!(u instanceof i))throw new TypeError("Cannot call a class as a function")}function o(u,i){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:u}function e(u,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);u.prototype=Object.create(i&&i.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(u,i):u.__proto__=i)}var t=function(u){e(i,u);function i(){return c(this,i),o(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return T(i,[{key:"value",value:function(s){var n=k(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"value",this).call(this,s);return n.startsWith("rgb(")?(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+n.split(",").map(function(f){return("00"+parseInt(f).toString(16)).slice(-2)}).join("")):n}}]),i}(p.default.Attributor.Style),a=new p.default.Attributor.Class("color","ql-color",{scope:p.default.Scope.INLINE}),l=new t("color","color",{scope:p.default.Scope.INLINE});d.ColorAttributor=t,d.ColorClass=a,d.ColorStyle=l},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.sanitize=d.default=void 0;var T=function(){function l(u,i){for(var r=0;r<i.length;r++){var s=i[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(u,s.key,s)}}return function(u,i,r){return i&&l(u.prototype,i),r&&l(u,r),u}}(),k=function l(u,i,r){u===null&&(u=Function.prototype);var s=Object.getOwnPropertyDescriptor(u,i);if(s===void 0){var n=Object.getPrototypeOf(u);return n===null?void 0:l(n,i,r)}else{if("value"in s)return s.value;var f=s.get;return f===void 0?void 0:f.call(r)}},E=h(6),p=m(E);function m(l){return l&&l.__esModule?l:{default:l}}function c(l,u){if(!(l instanceof u))throw new TypeError("Cannot call a class as a function")}function o(l,u){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:l}function e(l,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);l.prototype=Object.create(u&&u.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(l,u):l.__proto__=u)}var t=function(l){e(u,l);function u(){return c(this,u),o(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return T(u,[{key:"format",value:function(r,s){if(r!==this.statics.blotName||!s)return k(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"format",this).call(this,r,s);s=this.constructor.sanitize(s),this.domNode.setAttribute("href",s)}}],[{key:"create",value:function(r){var s=k(u.__proto__||Object.getPrototypeOf(u),"create",this).call(this,r);return r=this.sanitize(r),s.setAttribute("href",r),s.setAttribute("rel","noopener noreferrer"),s.setAttribute("target","_blank"),s}},{key:"formats",value:function(r){return r.getAttribute("href")}},{key:"sanitize",value:function(r){return a(r,this.PROTOCOL_WHITELIST)?r:this.SANITIZED_URL}}]),u}(p.default);t.blotName="link",t.tagName="A",t.SANITIZED_URL="about:blank",t.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function a(l,u){var i=document.createElement("a");i.href=l;var r=i.href.slice(0,i.href.indexOf(":"));return u.indexOf(r)>-1}d.default=t,d.sanitize=a},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},k=function(){function u(i,r){for(var s=0;s<r.length;s++){var n=r[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,r,s){return r&&u(i.prototype,r),s&&u(i,s),i}}(),E=h(23),p=o(E),m=h(107),c=o(m);function o(u){return u&&u.__esModule?u:{default:u}}function e(u,i){if(!(u instanceof i))throw new TypeError("Cannot call a class as a function")}var t=0;function a(u,i){u.setAttribute(i,u.getAttribute(i)!=="true")}var l=function(){function u(i){var r=this;e(this,u),this.select=i,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){r.togglePicker()}),this.label.addEventListener("keydown",function(s){switch(s.keyCode){case p.default.keys.ENTER:r.togglePicker();break;case p.default.keys.ESCAPE:r.escape(),s.preventDefault();break;default:}}),this.select.addEventListener("change",this.update.bind(this))}return k(u,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),a(this.label,"aria-expanded"),a(this.options,"aria-hidden")}},{key:"buildItem",value:function(r){var s=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),r.hasAttribute("value")&&n.setAttribute("data-value",r.getAttribute("value")),r.textContent&&n.setAttribute("data-label",r.textContent),n.addEventListener("click",function(){s.selectItem(n,!0)}),n.addEventListener("keydown",function(f){switch(f.keyCode){case p.default.keys.ENTER:s.selectItem(n,!0),f.preventDefault();break;case p.default.keys.ESCAPE:s.escape(),f.preventDefault();break;default:}}),n}},{key:"buildLabel",value:function(){var r=document.createElement("span");return r.classList.add("ql-picker-label"),r.innerHTML=c.default,r.tabIndex="0",r.setAttribute("role","button"),r.setAttribute("aria-expanded","false"),this.container.appendChild(r),r}},{key:"buildOptions",value:function(){var r=this,s=document.createElement("span");s.classList.add("ql-picker-options"),s.setAttribute("aria-hidden","true"),s.tabIndex="-1",s.id="ql-picker-options-"+t,t+=1,this.label.setAttribute("aria-controls",s.id),this.options=s,[].slice.call(this.select.options).forEach(function(n){var f=r.buildItem(n);s.appendChild(f),n.selected===!0&&r.selectItem(f)}),this.container.appendChild(s)}},{key:"buildPicker",value:function(){var r=this;[].slice.call(this.select.attributes).forEach(function(s){r.container.setAttribute(s.name,s.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var r=this;this.close(),setTimeout(function(){return r.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(r){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=this.container.querySelector(".ql-selected");if(r!==n&&(n?.classList.remove("ql-selected"),r!=null&&(r.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(r.parentNode.children,r),r.hasAttribute("data-value")?this.label.setAttribute("data-value",r.getAttribute("data-value")):this.label.removeAttribute("data-value"),r.hasAttribute("data-label")?this.label.setAttribute("data-label",r.getAttribute("data-label")):this.label.removeAttribute("data-label"),s))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event>"u"?"undefined":T(Event))==="object"){var f=document.createEvent("Event");f.initEvent("change",!0,!0),this.select.dispatchEvent(f)}this.close()}}},{key:"update",value:function(){var r=void 0;if(this.select.selectedIndex>-1){var s=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];r=this.select.options[this.select.selectedIndex],this.selectItem(s)}else this.selectItem(null);var n=r!=null&&r!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),u}();d.default=l},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(0),k=L(T),E=h(5),p=L(E),m=h(4),c=L(m),o=h(16),e=L(o),t=h(25),a=L(t),l=h(24),u=L(l),i=h(35),r=L(i),s=h(6),n=L(s),f=h(22),w=L(f),b=h(7),_=L(b),N=h(55),g=L(N),v=h(42),y=L(v),A=h(23),x=L(A);function L(B){return B&&B.__esModule?B:{default:B}}p.default.register({"blots/block":c.default,"blots/block/embed":m.BlockEmbed,"blots/break":e.default,"blots/container":a.default,"blots/cursor":u.default,"blots/embed":r.default,"blots/inline":n.default,"blots/scroll":w.default,"blots/text":_.default,"modules/clipboard":g.default,"modules/history":y.default,"modules/keyboard":x.default}),k.default.register(c.default,e.default,u.default,n.default,w.default,_.default),d.default=p.default},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(1),k=function(){function E(p){this.domNode=p,this.domNode[T.DATA_KEY]={blot:this}}return Object.defineProperty(E.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),E.create=function(p){if(this.tagName==null)throw new T.ParchmentError("Blot definition missing tagName");var m;return Array.isArray(this.tagName)?(typeof p=="string"&&(p=p.toUpperCase(),parseInt(p).toString()===p&&(p=parseInt(p))),typeof p=="number"?m=document.createElement(this.tagName[p-1]):this.tagName.indexOf(p)>-1?m=document.createElement(p):m=document.createElement(this.tagName[0])):m=document.createElement(this.tagName),this.className&&m.classList.add(this.className),m},E.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},E.prototype.clone=function(){var p=this.domNode.cloneNode(!1);return T.create(p)},E.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[T.DATA_KEY]},E.prototype.deleteAt=function(p,m){var c=this.isolate(p,m);c.remove()},E.prototype.formatAt=function(p,m,c,o){var e=this.isolate(p,m);if(T.query(c,T.Scope.BLOT)!=null&&o)e.wrap(c,o);else if(T.query(c,T.Scope.ATTRIBUTE)!=null){var t=T.create(this.statics.scope);e.wrap(t),t.format(c,o)}},E.prototype.insertAt=function(p,m,c){var o=c==null?T.create("text",m):T.create(m,c),e=this.split(p);this.parent.insertBefore(o,e)},E.prototype.insertInto=function(p,m){m===void 0&&(m=null),this.parent!=null&&this.parent.children.remove(this);var c=null;p.children.insertBefore(this,m),m!=null&&(c=m.domNode),(this.domNode.parentNode!=p.domNode||this.domNode.nextSibling!=c)&&p.domNode.insertBefore(this.domNode,c),this.parent=p,this.attach()},E.prototype.isolate=function(p,m){var c=this.split(p);return c.split(m),c},E.prototype.length=function(){return 1},E.prototype.offset=function(p){return p===void 0&&(p=this.parent),this.parent==null||this==p?0:this.parent.children.offset(this)+this.parent.offset(p)},E.prototype.optimize=function(p){this.domNode[T.DATA_KEY]!=null&&delete this.domNode[T.DATA_KEY].mutations},E.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},E.prototype.replace=function(p){p.parent!=null&&(p.parent.insertBefore(this,p.next),p.remove())},E.prototype.replaceWith=function(p,m){var c=typeof p=="string"?T.create(p,m):p;return c.replace(this),c},E.prototype.split=function(p,m){return p===0?this:this.next},E.prototype.update=function(p,m){},E.prototype.wrap=function(p,m){var c=typeof p=="string"?T.create(p,m):p;return this.parent!=null&&this.parent.insertBefore(c,this.next),c.appendChild(this),c},E.blotName="abstract",E}();d.default=k},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(12),k=h(32),E=h(33),p=h(1),m=function(){function c(o){this.attributes={},this.domNode=o,this.build()}return c.prototype.attribute=function(o,e){e?o.add(this.domNode,e)&&(o.value(this.domNode)!=null?this.attributes[o.attrName]=o:delete this.attributes[o.attrName]):(o.remove(this.domNode),delete this.attributes[o.attrName])},c.prototype.build=function(){var o=this;this.attributes={};var e=T.default.keys(this.domNode),t=k.default.keys(this.domNode),a=E.default.keys(this.domNode);e.concat(t).concat(a).forEach(function(l){var u=p.query(l,p.Scope.ATTRIBUTE);u instanceof T.default&&(o.attributes[u.attrName]=u)})},c.prototype.copy=function(o){var e=this;Object.keys(this.attributes).forEach(function(t){var a=e.attributes[t].value(e.domNode);o.format(t,a)})},c.prototype.move=function(o){var e=this;this.copy(o),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},c.prototype.values=function(){var o=this;return Object.keys(this.attributes).reduce(function(e,t){return e[t]=o.attributes[t].value(o.domNode),e},{})},c}();d.default=m},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var e in o)o.hasOwnProperty(e)&&(c[e]=o[e])};return function(c,o){m(c,o);function e(){this.constructor=c}c.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(12);function E(m,c){var o=m.getAttribute("class")||"";return o.split(/\s+/).filter(function(e){return e.indexOf(c+"-")===0})}var p=function(m){T(c,m);function c(){return m!==null&&m.apply(this,arguments)||this}return c.keys=function(o){return(o.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})},c.prototype.add=function(o,e){return this.canAdd(o,e)?(this.remove(o),o.classList.add(this.keyName+"-"+e),!0):!1},c.prototype.remove=function(o){var e=E(o,this.keyName);e.forEach(function(t){o.classList.remove(t)}),o.classList.length===0&&o.removeAttribute("class")},c.prototype.value=function(o){var e=E(o,this.keyName)[0]||"",t=e.slice(this.keyName.length+1);return this.canAdd(o,t)?t:""},c}(k.default);d.default=p},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var e in o)o.hasOwnProperty(e)&&(c[e]=o[e])};return function(c,o){m(c,o);function e(){this.constructor=c}c.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(12);function E(m){var c=m.split("-"),o=c.slice(1).map(function(e){return e[0].toUpperCase()+e.slice(1)}).join("");return c[0]+o}var p=function(m){T(c,m);function c(){return m!==null&&m.apply(this,arguments)||this}return c.keys=function(o){return(o.getAttribute("style")||"").split(";").map(function(e){var t=e.split(":");return t[0].trim()})},c.prototype.add=function(o,e){return this.canAdd(o,e)?(o.style[E(this.keyName)]=e,!0):!1},c.prototype.remove=function(o){o.style[E(this.keyName)]="",o.getAttribute("style")||o.removeAttribute("style")},c.prototype.value=function(o){var e=o.style[E(this.keyName)];return this.canAdd(o,e)?e:""},c}(k.default);d.default=p},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function p(m,c){for(var o=0;o<c.length;o++){var e=c[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(m,e.key,e)}}return function(m,c,o){return c&&p(m.prototype,c),o&&p(m,o),m}}();function k(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}var E=function(){function p(m,c){k(this,p),this.quill=m,this.options=c,this.modules={}}return T(p,[{key:"init",value:function(){var c=this;Object.keys(this.options.modules).forEach(function(o){c.modules[o]==null&&c.addModule(o)})}},{key:"addModule",value:function(c){var o=this.quill.constructor.import("modules/"+c);return this.modules[c]=new o(this.quill,this.options.modules[c]||{}),this.modules[c]}}]),p}();E.DEFAULTS={modules:{}},E.themes={default:E},d.default=E},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function i(r,s){for(var n=0;n<s.length;n++){var f=s[n];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(r,f.key,f)}}return function(r,s,n){return s&&i(r.prototype,s),n&&i(r,n),r}}(),k=function i(r,s,n){r===null&&(r=Function.prototype);var f=Object.getOwnPropertyDescriptor(r,s);if(f===void 0){var w=Object.getPrototypeOf(r);return w===null?void 0:i(w,s,n)}else{if("value"in f)return f.value;var b=f.get;return b===void 0?void 0:b.call(n)}},E=h(0),p=o(E),m=h(7),c=o(m);function o(i){return i&&i.__esModule?i:{default:i}}function e(i,r){if(!(i instanceof r))throw new TypeError("Cannot call a class as a function")}function t(i,r){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:i}function a(i,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);i.prototype=Object.create(r&&r.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(i,r):i.__proto__=r)}var l="\uFEFF",u=function(i){a(r,i);function r(s){e(this,r);var n=t(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,s));return n.contentNode=document.createElement("span"),n.contentNode.setAttribute("contenteditable",!1),[].slice.call(n.domNode.childNodes).forEach(function(f){n.contentNode.appendChild(f)}),n.leftGuard=document.createTextNode(l),n.rightGuard=document.createTextNode(l),n.domNode.appendChild(n.leftGuard),n.domNode.appendChild(n.contentNode),n.domNode.appendChild(n.rightGuard),n}return T(r,[{key:"index",value:function(n,f){return n===this.leftGuard?0:n===this.rightGuard?1:k(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"index",this).call(this,n,f)}},{key:"restore",value:function(n){var f=void 0,w=void 0,b=n.data.split(l).join("");if(n===this.leftGuard)if(this.prev instanceof c.default){var _=this.prev.length();this.prev.insertAt(_,b),f={startNode:this.prev.domNode,startOffset:_+b.length}}else w=document.createTextNode(b),this.parent.insertBefore(p.default.create(w),this),f={startNode:w,startOffset:b.length};else n===this.rightGuard&&(this.next instanceof c.default?(this.next.insertAt(0,b),f={startNode:this.next.domNode,startOffset:b.length}):(w=document.createTextNode(b),this.parent.insertBefore(p.default.create(w),this.next),f={startNode:w,startOffset:b.length}));return n.data=l,f}},{key:"update",value:function(n,f){var w=this;n.forEach(function(b){if(b.type==="characterData"&&(b.target===w.leftGuard||b.target===w.rightGuard)){var _=w.restore(b.target);_&&(f.range=_)}})}}]),r}(p.default.Embed);d.default=u},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.AlignStyle=d.AlignClass=d.AlignAttribute=void 0;var T=h(0),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}var p={scope:k.default.Scope.BLOCK,whitelist:["right","center","justify"]},m=new k.default.Attributor.Attribute("align","align",p),c=new k.default.Attributor.Class("align","ql-align",p),o=new k.default.Attributor.Style("align","text-align",p);d.AlignAttribute=m,d.AlignClass=c,d.AlignStyle=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.BackgroundStyle=d.BackgroundClass=void 0;var T=h(0),k=p(T),E=h(26);function p(o){return o&&o.__esModule?o:{default:o}}var m=new k.default.Attributor.Class("background","ql-bg",{scope:k.default.Scope.INLINE}),c=new E.ColorAttributor("background","background-color",{scope:k.default.Scope.INLINE});d.BackgroundClass=m,d.BackgroundStyle=c},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.DirectionStyle=d.DirectionClass=d.DirectionAttribute=void 0;var T=h(0),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}var p={scope:k.default.Scope.BLOCK,whitelist:["rtl"]},m=new k.default.Attributor.Attribute("direction","dir",p),c=new k.default.Attributor.Class("direction","ql-direction",p),o=new k.default.Attributor.Style("direction","direction",p);d.DirectionAttribute=m,d.DirectionClass=c,d.DirectionStyle=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.FontClass=d.FontStyle=void 0;var T=function(){function i(r,s){for(var n=0;n<s.length;n++){var f=s[n];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(r,f.key,f)}}return function(r,s,n){return s&&i(r.prototype,s),n&&i(r,n),r}}(),k=function i(r,s,n){r===null&&(r=Function.prototype);var f=Object.getOwnPropertyDescriptor(r,s);if(f===void 0){var w=Object.getPrototypeOf(r);return w===null?void 0:i(w,s,n)}else{if("value"in f)return f.value;var b=f.get;return b===void 0?void 0:b.call(n)}},E=h(0),p=m(E);function m(i){return i&&i.__esModule?i:{default:i}}function c(i,r){if(!(i instanceof r))throw new TypeError("Cannot call a class as a function")}function o(i,r){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:i}function e(i,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);i.prototype=Object.create(r&&r.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(i,r):i.__proto__=r)}var t={scope:p.default.Scope.INLINE,whitelist:["serif","monospace"]},a=new p.default.Attributor.Class("font","ql-font",t),l=function(i){e(r,i);function r(){return c(this,r),o(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return T(r,[{key:"value",value:function(n){return k(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"value",this).call(this,n).replace(/["']/g,"")}}]),r}(p.default.Attributor.Style),u=new l("font","font-family",t);d.FontStyle=u,d.FontClass=a},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.SizeStyle=d.SizeClass=void 0;var T=h(0),k=E(T);function E(c){return c&&c.__esModule?c:{default:c}}var p=new k.default.Attributor.Class("size","ql-size",{scope:k.default.Scope.INLINE,whitelist:["small","large","huge"]}),m=new k.default.Attributor.Style("size","font-size",{scope:k.default.Scope.INLINE,whitelist:["10px","18px","32px"]});d.SizeClass=p,d.SizeStyle=m},function(I,d,h){"use strict";I.exports={align:{"":h(76),center:h(77),right:h(78),justify:h(79)},background:h(80),blockquote:h(81),bold:h(82),clean:h(83),code:h(58),"code-block":h(58),color:h(84),direction:{"":h(85),rtl:h(86)},float:{center:h(87),full:h(88),left:h(89),right:h(90)},formula:h(91),header:{1:h(92),2:h(93)},italic:h(94),image:h(95),indent:{"+1":h(96),"-1":h(97)},link:h(98),list:{ordered:h(99),bullet:h(100),check:h(101)},script:{sub:h(102),super:h(103)},strike:h(104),underline:h(105),video:h(106)}},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.getLastChangeIndex=d.default=void 0;var T=function(){function s(n,f){for(var w=0;w<f.length;w++){var b=f[w];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(n,b.key,b)}}return function(n,f,w){return f&&s(n.prototype,f),w&&s(n,w),n}}(),k=h(0),E=e(k),p=h(5),m=e(p),c=h(9),o=e(c);function e(s){return s&&s.__esModule?s:{default:s}}function t(s,n){if(!(s instanceof n))throw new TypeError("Cannot call a class as a function")}function a(s,n){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:s}function l(s,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);s.prototype=Object.create(n&&n.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(s,n):s.__proto__=n)}var u=function(s){l(n,s);function n(f,w){t(this,n);var b=a(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,f,w));return b.lastRecorded=0,b.ignoreChange=!1,b.clear(),b.quill.on(m.default.events.EDITOR_CHANGE,function(_,N,g,v){_!==m.default.events.TEXT_CHANGE||b.ignoreChange||(!b.options.userOnly||v===m.default.sources.USER?b.record(N,g):b.transform(N))}),b.quill.keyboard.addBinding({key:"Z",shortKey:!0},b.undo.bind(b)),b.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},b.redo.bind(b)),/Win/i.test(navigator.platform)&&b.quill.keyboard.addBinding({key:"Y",shortKey:!0},b.redo.bind(b)),b}return T(n,[{key:"change",value:function(w,b){if(this.stack[w].length!==0){var _=this.stack[w].pop();this.stack[b].push(_),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(_[w],m.default.sources.USER),this.ignoreChange=!1;var N=r(_[w]);this.quill.setSelection(N)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(w,b){if(w.ops.length!==0){this.stack.redo=[];var _=this.quill.getContents().diff(b),N=Date.now();if(this.lastRecorded+this.options.delay>N&&this.stack.undo.length>0){var g=this.stack.undo.pop();_=_.compose(g.undo),w=g.redo.compose(w)}else this.lastRecorded=N;this.stack.undo.push({redo:w,undo:_}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(w){this.stack.undo.forEach(function(b){b.undo=w.transform(b.undo,!0),b.redo=w.transform(b.redo,!0)}),this.stack.redo.forEach(function(b){b.undo=w.transform(b.undo,!0),b.redo=w.transform(b.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),n}(o.default);u.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function i(s){var n=s.ops[s.ops.length-1];return n==null?!1:n.insert!=null?typeof n.insert=="string"&&n.insert.endsWith(`
`):n.attributes!=null?Object.keys(n.attributes).some(function(f){return E.default.query(f,E.default.Scope.BLOCK)!=null}):!1}function r(s){var n=s.reduce(function(w,b){return w+=b.delete||0,w},0),f=s.length()-n;return i(s)&&(f-=1),f}d.default=u,d.getLastChangeIndex=r},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.BaseTooltip=void 0;var T=function(){function P(q,C){for(var D=0;D<C.length;D++){var S=C[D];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(q,S.key,S)}}return function(q,C,D){return C&&P(q.prototype,C),D&&P(q,D),q}}(),k=function P(q,C,D){q===null&&(q=Function.prototype);var S=Object.getOwnPropertyDescriptor(q,C);if(S===void 0){var j=Object.getPrototypeOf(q);return j===null?void 0:P(j,C,D)}else{if("value"in S)return S.value;var U=S.get;return U===void 0?void 0:U.call(D)}},E=h(3),p=N(E),m=h(2),c=N(m),o=h(8),e=N(o),t=h(23),a=N(t),l=h(34),u=N(l),i=h(59),r=N(i),s=h(60),n=N(s),f=h(28),w=N(f),b=h(61),_=N(b);function N(P){return P&&P.__esModule?P:{default:P}}function g(P,q){if(!(P instanceof q))throw new TypeError("Cannot call a class as a function")}function v(P,q){if(!P)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return q&&(typeof q=="object"||typeof q=="function")?q:P}function y(P,q){if(typeof q!="function"&&q!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof q);P.prototype=Object.create(q&&q.prototype,{constructor:{value:P,enumerable:!1,writable:!0,configurable:!0}}),q&&(Object.setPrototypeOf?Object.setPrototypeOf(P,q):P.__proto__=q)}var A=[!1,"center","right","justify"],x=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],L=[!1,"serif","monospace"],B=["1","2","3",!1],F=["small",!1,"large","huge"],V=function(P){y(q,P);function q(C,D){g(this,q);var S=v(this,(q.__proto__||Object.getPrototypeOf(q)).call(this,C,D)),j=function U(H){if(!document.body.contains(C.root))return document.body.removeEventListener("click",U);S.tooltip!=null&&!S.tooltip.root.contains(H.target)&&document.activeElement!==S.tooltip.textbox&&!S.quill.hasFocus()&&S.tooltip.hide(),S.pickers!=null&&S.pickers.forEach(function($){$.container.contains(H.target)||$.close()})};return C.emitter.listenDOM("click",document.body,j),S}return T(q,[{key:"addModule",value:function(D){var S=k(q.prototype.__proto__||Object.getPrototypeOf(q.prototype),"addModule",this).call(this,D);return D==="toolbar"&&this.extendToolbar(S),S}},{key:"buildButtons",value:function(D,S){D.forEach(function(j){var U=j.getAttribute("class")||"";U.split(/\s+/).forEach(function(H){if(H.startsWith("ql-")&&(H=H.slice(3),S[H]!=null))if(H==="direction")j.innerHTML=S[H][""]+S[H].rtl;else if(typeof S[H]=="string")j.innerHTML=S[H];else{var $=j.value||"";$!=null&&S[H][$]&&(j.innerHTML=S[H][$])}})})}},{key:"buildPickers",value:function(D,S){var j=this;this.pickers=D.map(function(H){if(H.classList.contains("ql-align"))return H.querySelector("option")==null&&O(H,A),new n.default(H,S.align);if(H.classList.contains("ql-background")||H.classList.contains("ql-color")){var $=H.classList.contains("ql-background")?"background":"color";return H.querySelector("option")==null&&O(H,x,$==="background"?"#ffffff":"#000000"),new r.default(H,S[$])}else return H.querySelector("option")==null&&(H.classList.contains("ql-font")?O(H,L):H.classList.contains("ql-header")?O(H,B):H.classList.contains("ql-size")&&O(H,F)),new w.default(H)});var U=function(){j.pickers.forEach(function($){$.update()})};this.quill.on(e.default.events.EDITOR_CHANGE,U)}}]),q}(u.default);V.DEFAULTS=(0,p.default)(!0,{},u.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var q=this,C=this.container.querySelector("input.ql-image[type=file]");C==null&&(C=document.createElement("input"),C.setAttribute("type","file"),C.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),C.classList.add("ql-image"),C.addEventListener("change",function(){if(C.files!=null&&C.files[0]!=null){var D=new FileReader;D.onload=function(S){var j=q.quill.getSelection(!0);q.quill.updateContents(new c.default().retain(j.index).delete(j.length).insert({image:S.target.result}),e.default.sources.USER),q.quill.setSelection(j.index+1,e.default.sources.SILENT),C.value=""},D.readAsDataURL(C.files[0])}}),this.container.appendChild(C)),C.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var M=function(P){y(q,P);function q(C,D){g(this,q);var S=v(this,(q.__proto__||Object.getPrototypeOf(q)).call(this,C,D));return S.textbox=S.root.querySelector('input[type="text"]'),S.listen(),S}return T(q,[{key:"listen",value:function(){var D=this;this.textbox.addEventListener("keydown",function(S){a.default.match(S,"enter")?(D.save(),S.preventDefault()):a.default.match(S,"escape")&&(D.cancel(),S.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var D=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),S!=null?this.textbox.value=S:D!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+D)||""),this.root.setAttribute("data-mode",D)}},{key:"restoreFocus",value:function(){var D=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=D}},{key:"save",value:function(){var D=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var S=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",D,e.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",D,e.default.sources.USER)),this.quill.root.scrollTop=S;break}case"video":D=R(D);case"formula":{if(!D)break;var j=this.quill.getSelection(!0);if(j!=null){var U=j.index+j.length;this.quill.insertEmbed(U,this.root.getAttribute("data-mode"),D,e.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(U+1," ",e.default.sources.USER),this.quill.setSelection(U+2,e.default.sources.USER)}break}default:}this.textbox.value="",this.hide()}}]),q}(_.default);function R(P){var q=P.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||P.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return q?(q[1]||"https")+"://www.youtube.com/embed/"+q[2]+"?showinfo=0":(q=P.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(q[1]||"https")+"://player.vimeo.com/video/"+q[2]+"/":P}function O(P,q){var C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;q.forEach(function(D){var S=document.createElement("option");D===C?S.setAttribute("selected","selected"):S.setAttribute("value",D),P.appendChild(S)})}d.BaseTooltip=M,d.default=V},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function k(){this.head=this.tail=null,this.length=0}return k.prototype.append=function(){for(var E=[],p=0;p<arguments.length;p++)E[p]=arguments[p];this.insertBefore(E[0],null),E.length>1&&this.append.apply(this,E.slice(1))},k.prototype.contains=function(E){for(var p,m=this.iterator();p=m();)if(p===E)return!0;return!1},k.prototype.insertBefore=function(E,p){E&&(E.next=p,p!=null?(E.prev=p.prev,p.prev!=null&&(p.prev.next=E),p.prev=E,p===this.head&&(this.head=E)):this.tail!=null?(this.tail.next=E,E.prev=this.tail,this.tail=E):(E.prev=null,this.head=this.tail=E),this.length+=1)},k.prototype.offset=function(E){for(var p=0,m=this.head;m!=null;){if(m===E)return p;p+=m.length(),m=m.next}return-1},k.prototype.remove=function(E){this.contains(E)&&(E.prev!=null&&(E.prev.next=E.next),E.next!=null&&(E.next.prev=E.prev),E===this.head&&(this.head=E.next),E===this.tail&&(this.tail=E.prev),this.length-=1)},k.prototype.iterator=function(E){return E===void 0&&(E=this.head),function(){var p=E;return E!=null&&(E=E.next),p}},k.prototype.find=function(E,p){p===void 0&&(p=!1);for(var m,c=this.iterator();m=c();){var o=m.length();if(E<o||p&&E===o&&(m.next==null||m.next.length()!==0))return[m,E];E-=o}return[null,0]},k.prototype.forEach=function(E){for(var p,m=this.iterator();p=m();)E(p)},k.prototype.forEachAt=function(E,p,m){if(!(p<=0))for(var c=this.find(E),o=c[0],e=c[1],t,a=E-e,l=this.iterator(o);(t=l())&&a<E+p;){var u=t.length();E>a?m(t,E-a,Math.min(p,a+u-E)):m(t,0,Math.min(u,E+p-a)),a+=u}},k.prototype.map=function(E){return this.reduce(function(p,m){return p.push(E(m)),p},[])},k.prototype.reduce=function(E,p){for(var m,c=this.iterator();m=c();)p=E(p,m);return p},k}();d.default=T},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])};return function(e,t){o(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(17),E=h(1),p={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},m=100,c=function(o){T(e,o);function e(t){var a=o.call(this,t)||this;return a.scroll=a,a.observer=new MutationObserver(function(l){a.update(l)}),a.observer.observe(a.domNode,p),a.attach(),a}return e.prototype.detach=function(){o.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(t,a){this.update(),t===0&&a===this.length()?this.children.forEach(function(l){l.remove()}):o.prototype.deleteAt.call(this,t,a)},e.prototype.formatAt=function(t,a,l,u){this.update(),o.prototype.formatAt.call(this,t,a,l,u)},e.prototype.insertAt=function(t,a,l){this.update(),o.prototype.insertAt.call(this,t,a,l)},e.prototype.optimize=function(t,a){var l=this;t===void 0&&(t=[]),a===void 0&&(a={}),o.prototype.optimize.call(this,a);for(var u=[].slice.call(this.observer.takeRecords());u.length>0;)t.push(u.pop());for(var i=function(f,w){w===void 0&&(w=!0),!(f==null||f===l)&&f.domNode.parentNode!=null&&(f.domNode[E.DATA_KEY].mutations==null&&(f.domNode[E.DATA_KEY].mutations=[]),w&&i(f.parent))},r=function(f){f.domNode[E.DATA_KEY]==null||f.domNode[E.DATA_KEY].mutations==null||(f instanceof k.default&&f.children.forEach(r),f.optimize(a))},s=t,n=0;s.length>0;n+=1){if(n>=m)throw new Error("[Parchment] Maximum optimize iterations reached");for(s.forEach(function(f){var w=E.find(f.target,!0);w!=null&&(w.domNode===f.target&&(f.type==="childList"?(i(E.find(f.previousSibling,!1)),[].forEach.call(f.addedNodes,function(b){var _=E.find(b,!1);i(_,!1),_ instanceof k.default&&_.children.forEach(function(N){i(N,!1)})})):f.type==="attributes"&&i(w.prev)),i(w))}),this.children.forEach(r),s=[].slice.call(this.observer.takeRecords()),u=s.slice();u.length>0;)t.push(u.pop())}},e.prototype.update=function(t,a){var l=this;a===void 0&&(a={}),t=t||this.observer.takeRecords(),t.map(function(u){var i=E.find(u.target,!0);return i==null?null:i.domNode[E.DATA_KEY].mutations==null?(i.domNode[E.DATA_KEY].mutations=[u],i):(i.domNode[E.DATA_KEY].mutations.push(u),null)}).forEach(function(u){u==null||u===l||u.domNode[E.DATA_KEY]==null||u.update(u.domNode[E.DATA_KEY].mutations||[],a)}),this.domNode[E.DATA_KEY].mutations!=null&&o.prototype.update.call(this,this.domNode[E.DATA_KEY].mutations,a),this.optimize(t,a)},e.blotName="scroll",e.defaultChild="block",e.scope=E.Scope.BLOCK_BLOT,e.tagName="DIV",e}(k.default);d.default=c},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,e){o.__proto__=e}||function(o,e){for(var t in e)e.hasOwnProperty(t)&&(o[t]=e[t])};return function(o,e){c(o,e);function t(){this.constructor=o}o.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(18),E=h(1);function p(c,o){if(Object.keys(c).length!==Object.keys(o).length)return!1;for(var e in c)if(c[e]!==o[e])return!1;return!0}var m=function(c){T(o,c);function o(){return c!==null&&c.apply(this,arguments)||this}return o.formats=function(e){if(e.tagName!==o.tagName)return c.formats.call(this,e)},o.prototype.format=function(e,t){var a=this;e===this.statics.blotName&&!t?(this.children.forEach(function(l){l instanceof k.default||(l=l.wrap(o.blotName,!0)),a.attributes.copy(l)}),this.unwrap()):c.prototype.format.call(this,e,t)},o.prototype.formatAt=function(e,t,a,l){if(this.formats()[a]!=null||E.query(a,E.Scope.ATTRIBUTE)){var u=this.isolate(e,t);u.format(a,l)}else c.prototype.formatAt.call(this,e,t,a,l)},o.prototype.optimize=function(e){c.prototype.optimize.call(this,e);var t=this.formats();if(Object.keys(t).length===0)return this.unwrap();var a=this.next;a instanceof o&&a.prev===this&&p(t,a.formats())&&(a.moveChildren(this),a.remove())},o.blotName="inline",o.scope=E.Scope.INLINE_BLOT,o.tagName="SPAN",o}(k.default);d.default=m},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var e in o)o.hasOwnProperty(e)&&(c[e]=o[e])};return function(c,o){m(c,o);function e(){this.constructor=c}c.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(18),E=h(1),p=function(m){T(c,m);function c(){return m!==null&&m.apply(this,arguments)||this}return c.formats=function(o){var e=E.query(c.blotName).tagName;if(o.tagName!==e)return m.formats.call(this,o)},c.prototype.format=function(o,e){E.query(o,E.Scope.BLOCK)!=null&&(o===this.statics.blotName&&!e?this.replaceWith(c.blotName):m.prototype.format.call(this,o,e))},c.prototype.formatAt=function(o,e,t,a){E.query(t,E.Scope.BLOCK)!=null?this.format(t,a):m.prototype.formatAt.call(this,o,e,t,a)},c.prototype.insertAt=function(o,e,t){if(t==null||E.query(e,E.Scope.INLINE)!=null)m.prototype.insertAt.call(this,o,e,t);else{var a=this.split(o),l=E.create(e,t);a.parent.insertBefore(l,a)}},c.prototype.update=function(o,e){navigator.userAgent.match(/Trident/)?this.build():m.prototype.update.call(this,o,e)},c.blotName="block",c.scope=E.Scope.BLOCK_BLOT,c.tagName="P",c}(k.default);d.default=p},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(m,c){m.__proto__=c}||function(m,c){for(var o in c)c.hasOwnProperty(o)&&(m[o]=c[o])};return function(m,c){p(m,c);function o(){this.constructor=m}m.prototype=c===null?Object.create(c):(o.prototype=c.prototype,new o)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(19),E=function(p){T(m,p);function m(){return p!==null&&p.apply(this,arguments)||this}return m.formats=function(c){},m.prototype.format=function(c,o){p.prototype.formatAt.call(this,0,this.length(),c,o)},m.prototype.formatAt=function(c,o,e,t){c===0&&o===this.length()?this.format(e,t):p.prototype.formatAt.call(this,c,o,e,t)},m.prototype.formats=function(){return this.statics.formats(this.domNode)},m}(k.default);d.default=E},function(I,d,h){"use strict";var T=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var e in o)o.hasOwnProperty(e)&&(c[e]=o[e])};return function(c,o){m(c,o);function e(){this.constructor=c}c.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var k=h(19),E=h(1),p=function(m){T(c,m);function c(o){var e=m.call(this,o)||this;return e.text=e.statics.value(e.domNode),e}return c.create=function(o){return document.createTextNode(o)},c.value=function(o){var e=o.data;return e.normalize&&(e=e.normalize()),e},c.prototype.deleteAt=function(o,e){this.domNode.data=this.text=this.text.slice(0,o)+this.text.slice(o+e)},c.prototype.index=function(o,e){return this.domNode===o?e:-1},c.prototype.insertAt=function(o,e,t){t==null?(this.text=this.text.slice(0,o)+e+this.text.slice(o),this.domNode.data=this.text):m.prototype.insertAt.call(this,o,e,t)},c.prototype.length=function(){return this.text.length},c.prototype.optimize=function(o){m.prototype.optimize.call(this,o),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof c&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},c.prototype.position=function(o,e){return e===void 0&&(e=!1),[this.domNode,o]},c.prototype.split=function(o,e){if(e===void 0&&(e=!1),!e){if(o===0)return this;if(o===this.length())return this.next}var t=E.create(this.domNode.splitText(o));return this.parent.insertBefore(t,this.next),this.text=this.statics.value(this.domNode),t},c.prototype.update=function(o,e){var t=this;o.some(function(a){return a.type==="characterData"&&a.target===t.domNode})&&(this.text=this.statics.value(this.domNode))},c.prototype.value=function(){return this.text},c.blotName="text",c.scope=E.Scope.INLINE_BLOT,c}(k.default);d.default=p},function(I,d,h){"use strict";var T=document.createElement("div");if(T.classList.toggle("test-class",!1),T.classList.contains("test-class")){var k=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(E,p){return arguments.length>1&&!this.contains(E)==!p?p:k.call(this,E)}}String.prototype.startsWith||(String.prototype.startsWith=function(E,p){return p=p||0,this.substr(p,E.length)===E}),String.prototype.endsWith||(String.prototype.endsWith=function(E,p){var m=this.toString();(typeof p!="number"||!isFinite(p)||Math.floor(p)!==p||p>m.length)&&(p=m.length),p-=E.length;var c=m.indexOf(E,p);return c!==-1&&c===p}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(p){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof p!="function")throw new TypeError("predicate must be a function");for(var m=Object(this),c=m.length>>>0,o=arguments[1],e,t=0;t<c;t++)if(e=m[t],p.call(o,e,t,m))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(I,d){var h=-1,T=1,k=0;function E(n,f,w){if(n==f)return n?[[k,n]]:[];(w<0||n.length<w)&&(w=null);var b=o(n,f),_=n.substring(0,b);n=n.substring(b),f=f.substring(b),b=e(n,f);var N=n.substring(n.length-b);n=n.substring(0,n.length-b),f=f.substring(0,f.length-b);var g=p(n,f);return _&&g.unshift([k,_]),N&&g.push([k,N]),a(g),w!=null&&(g=i(g,w)),g=r(g),g}function p(n,f){var w;if(!n)return[[T,f]];if(!f)return[[h,n]];var b=n.length>f.length?n:f,_=n.length>f.length?f:n,N=b.indexOf(_);if(N!=-1)return w=[[T,b.substring(0,N)],[k,_],[T,b.substring(N+_.length)]],n.length>f.length&&(w[0][0]=w[2][0]=h),w;if(_.length==1)return[[h,n],[T,f]];var g=t(n,f);if(g){var v=g[0],y=g[1],A=g[2],x=g[3],L=g[4],B=E(v,A),F=E(y,x);return B.concat([[k,L]],F)}return m(n,f)}function m(n,f){for(var w=n.length,b=f.length,_=Math.ceil((w+b)/2),N=_,g=2*_,v=new Array(g),y=new Array(g),A=0;A<g;A++)v[A]=-1,y[A]=-1;v[N+1]=0,y[N+1]=0;for(var x=w-b,L=x%2!=0,B=0,F=0,V=0,M=0,R=0;R<_;R++){for(var O=-R+B;O<=R-F;O+=2){var P=N+O,q;O==-R||O!=R&&v[P-1]<v[P+1]?q=v[P+1]:q=v[P-1]+1;for(var C=q-O;q<w&&C<b&&n.charAt(q)==f.charAt(C);)q++,C++;if(v[P]=q,q>w)F+=2;else if(C>b)B+=2;else if(L){var D=N+x-O;if(D>=0&&D<g&&y[D]!=-1){var S=w-y[D];if(q>=S)return c(n,f,q,C)}}}for(var j=-R+V;j<=R-M;j+=2){var D=N+j,S;j==-R||j!=R&&y[D-1]<y[D+1]?S=y[D+1]:S=y[D-1]+1;for(var U=S-j;S<w&&U<b&&n.charAt(w-S-1)==f.charAt(b-U-1);)S++,U++;if(y[D]=S,S>w)M+=2;else if(U>b)V+=2;else if(!L){var P=N+x-j;if(P>=0&&P<g&&v[P]!=-1){var q=v[P],C=N+q-P;if(S=w-S,q>=S)return c(n,f,q,C)}}}}return[[h,n],[T,f]]}function c(n,f,w,b){var _=n.substring(0,w),N=f.substring(0,b),g=n.substring(w),v=f.substring(b),y=E(_,N),A=E(g,v);return y.concat(A)}function o(n,f){if(!n||!f||n.charAt(0)!=f.charAt(0))return 0;for(var w=0,b=Math.min(n.length,f.length),_=b,N=0;w<_;)n.substring(N,_)==f.substring(N,_)?(w=_,N=w):b=_,_=Math.floor((b-w)/2+w);return _}function e(n,f){if(!n||!f||n.charAt(n.length-1)!=f.charAt(f.length-1))return 0;for(var w=0,b=Math.min(n.length,f.length),_=b,N=0;w<_;)n.substring(n.length-_,n.length-N)==f.substring(f.length-_,f.length-N)?(w=_,N=w):b=_,_=Math.floor((b-w)/2+w);return _}function t(n,f){var w=n.length>f.length?n:f,b=n.length>f.length?f:n;if(w.length<4||b.length*2<w.length)return null;function _(F,V,M){for(var R=F.substring(M,M+Math.floor(F.length/4)),O=-1,P="",q,C,D,S;(O=V.indexOf(R,O+1))!=-1;){var j=o(F.substring(M),V.substring(O)),U=e(F.substring(0,M),V.substring(0,O));P.length<U+j&&(P=V.substring(O-U,O)+V.substring(O,O+j),q=F.substring(0,M-U),C=F.substring(M+j),D=V.substring(0,O-U),S=V.substring(O+j))}return P.length*2>=F.length?[q,C,D,S,P]:null}var N=_(w,b,Math.ceil(w.length/4)),g=_(w,b,Math.ceil(w.length/2)),v;if(!N&&!g)return null;g?N?v=N[4].length>g[4].length?N:g:v=g:v=N;var y,A,x,L;n.length>f.length?(y=v[0],A=v[1],x=v[2],L=v[3]):(x=v[0],L=v[1],y=v[2],A=v[3]);var B=v[4];return[y,A,x,L,B]}function a(n){n.push([k,""]);for(var f=0,w=0,b=0,_="",N="",g;f<n.length;)switch(n[f][0]){case T:b++,N+=n[f][1],f++;break;case h:w++,_+=n[f][1],f++;break;case k:w+b>1?(w!==0&&b!==0&&(g=o(N,_),g!==0&&(f-w-b>0&&n[f-w-b-1][0]==k?n[f-w-b-1][1]+=N.substring(0,g):(n.splice(0,0,[k,N.substring(0,g)]),f++),N=N.substring(g),_=_.substring(g)),g=e(N,_),g!==0&&(n[f][1]=N.substring(N.length-g)+n[f][1],N=N.substring(0,N.length-g),_=_.substring(0,_.length-g))),w===0?n.splice(f-b,w+b,[T,N]):b===0?n.splice(f-w,w+b,[h,_]):n.splice(f-w-b,w+b,[h,_],[T,N]),f=f-w-b+(w?1:0)+(b?1:0)+1):f!==0&&n[f-1][0]==k?(n[f-1][1]+=n[f][1],n.splice(f,1)):f++,b=0,w=0,_="",N="";break}n[n.length-1][1]===""&&n.pop();var v=!1;for(f=1;f<n.length-1;)n[f-1][0]==k&&n[f+1][0]==k&&(n[f][1].substring(n[f][1].length-n[f-1][1].length)==n[f-1][1]?(n[f][1]=n[f-1][1]+n[f][1].substring(0,n[f][1].length-n[f-1][1].length),n[f+1][1]=n[f-1][1]+n[f+1][1],n.splice(f-1,1),v=!0):n[f][1].substring(0,n[f+1][1].length)==n[f+1][1]&&(n[f-1][1]+=n[f+1][1],n[f][1]=n[f][1].substring(n[f+1][1].length)+n[f+1][1],n.splice(f+1,1),v=!0)),f++;v&&a(n)}var l=E;l.INSERT=T,l.DELETE=h,l.EQUAL=k,I.exports=l;function u(n,f){if(f===0)return[k,n];for(var w=0,b=0;b<n.length;b++){var _=n[b];if(_[0]===h||_[0]===k){var N=w+_[1].length;if(f===N)return[b+1,n];if(f<N){n=n.slice();var g=f-w,v=[_[0],_[1].slice(0,g)],y=[_[0],_[1].slice(g)];return n.splice(b,1,v,y),[b+1,n]}else w=N}}throw new Error("cursor_pos is out of bounds!")}function i(n,f){var w=u(n,f),b=w[1],_=w[0],N=b[_],g=b[_+1];if(N==null)return n;if(N[0]!==k)return n;if(g!=null&&N[1]+g[1]===g[1]+N[1])return b.splice(_,2,g,N),s(b,_,2);if(g!=null&&g[1].indexOf(N[1])===0){b.splice(_,2,[g[0],N[1]],[0,N[1]]);var v=g[1].slice(N[1].length);return v.length>0&&b.splice(_+2,0,[g[0],v]),s(b,_,3)}else return n}function r(n){for(var f=!1,w=function(g){return g.charCodeAt(0)>=56320&&g.charCodeAt(0)<=57343},b=function(g){return g.charCodeAt(g.length-1)>=55296&&g.charCodeAt(g.length-1)<=56319},_=2;_<n.length;_+=1)n[_-2][0]===k&&b(n[_-2][1])&&n[_-1][0]===h&&w(n[_-1][1])&&n[_][0]===T&&w(n[_][1])&&(f=!0,n[_-1][1]=n[_-2][1].slice(-1)+n[_-1][1],n[_][1]=n[_-2][1].slice(-1)+n[_][1],n[_-2][1]=n[_-2][1].slice(0,-1));if(!f)return n;for(var N=[],_=0;_<n.length;_+=1)n[_][1].length>0&&N.push(n[_]);return N}function s(n,f,w){for(var b=f+w-1;b>=0&&b>=f-1;b--)if(b+1<n.length){var _=n[b],N=n[b+1];_[0]===N[1]&&n.splice(b,2,[_[0],_[1]+N[1]])}return n}},function(I,d){d=I.exports=typeof Object.keys=="function"?Object.keys:h,d.shim=h;function h(T){var k=[];for(var E in T)k.push(E);return k}},function(I,d){var h=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";d=I.exports=h?T:k,d.supported=T;function T(E){return Object.prototype.toString.call(E)=="[object Arguments]"}d.unsupported=k;function k(E){return E&&typeof E=="object"&&typeof E.length=="number"&&Object.prototype.hasOwnProperty.call(E,"callee")&&!Object.prototype.propertyIsEnumerable.call(E,"callee")||!1}},function(I,d){"use strict";var h=Object.prototype.hasOwnProperty,T="~";function k(){}Object.create&&(k.prototype=Object.create(null),new k().__proto__||(T=!1));function E(m,c,o){this.fn=m,this.context=c,this.once=o||!1}function p(){this._events=new k,this._eventsCount=0}p.prototype.eventNames=function(){var c=[],o,e;if(this._eventsCount===0)return c;for(e in o=this._events)h.call(o,e)&&c.push(T?e.slice(1):e);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(o)):c},p.prototype.listeners=function(c,o){var e=T?T+c:c,t=this._events[e];if(o)return!!t;if(!t)return[];if(t.fn)return[t.fn];for(var a=0,l=t.length,u=new Array(l);a<l;a++)u[a]=t[a].fn;return u},p.prototype.emit=function(c,o,e,t,a,l){var u=T?T+c:c;if(!this._events[u])return!1;var i=this._events[u],r=arguments.length,s,n;if(i.fn){switch(i.once&&this.removeListener(c,i.fn,void 0,!0),r){case 1:return i.fn.call(i.context),!0;case 2:return i.fn.call(i.context,o),!0;case 3:return i.fn.call(i.context,o,e),!0;case 4:return i.fn.call(i.context,o,e,t),!0;case 5:return i.fn.call(i.context,o,e,t,a),!0;case 6:return i.fn.call(i.context,o,e,t,a,l),!0}for(n=1,s=new Array(r-1);n<r;n++)s[n-1]=arguments[n];i.fn.apply(i.context,s)}else{var f=i.length,w;for(n=0;n<f;n++)switch(i[n].once&&this.removeListener(c,i[n].fn,void 0,!0),r){case 1:i[n].fn.call(i[n].context);break;case 2:i[n].fn.call(i[n].context,o);break;case 3:i[n].fn.call(i[n].context,o,e);break;case 4:i[n].fn.call(i[n].context,o,e,t);break;default:if(!s)for(w=1,s=new Array(r-1);w<r;w++)s[w-1]=arguments[w];i[n].fn.apply(i[n].context,s)}}return!0},p.prototype.on=function(c,o,e){var t=new E(o,e||this),a=T?T+c:c;return this._events[a]?this._events[a].fn?this._events[a]=[this._events[a],t]:this._events[a].push(t):(this._events[a]=t,this._eventsCount++),this},p.prototype.once=function(c,o,e){var t=new E(o,e||this,!0),a=T?T+c:c;return this._events[a]?this._events[a].fn?this._events[a]=[this._events[a],t]:this._events[a].push(t):(this._events[a]=t,this._eventsCount++),this},p.prototype.removeListener=function(c,o,e,t){var a=T?T+c:c;if(!this._events[a])return this;if(!o)return--this._eventsCount===0?this._events=new k:delete this._events[a],this;var l=this._events[a];if(l.fn)l.fn===o&&(!t||l.once)&&(!e||l.context===e)&&(--this._eventsCount===0?this._events=new k:delete this._events[a]);else{for(var u=0,i=[],r=l.length;u<r;u++)(l[u].fn!==o||t&&!l[u].once||e&&l[u].context!==e)&&i.push(l[u]);i.length?this._events[a]=i.length===1?i[0]:i:--this._eventsCount===0?this._events=new k:delete this._events[a]}return this},p.prototype.removeAllListeners=function(c){var o;return c?(o=T?T+c:c,this._events[o]&&(--this._eventsCount===0?this._events=new k:delete this._events[o])):(this._events=new k,this._eventsCount=0),this},p.prototype.off=p.prototype.removeListener,p.prototype.addListener=p.prototype.on,p.prototype.setMaxListeners=function(){return this},p.prefixed=T,p.EventEmitter=p,typeof I<"u"&&(I.exports=p)},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.matchText=d.matchSpacing=d.matchNewline=d.matchBlot=d.matchAttributor=d.default=void 0;var T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(z){return typeof z}:function(z){return z&&typeof Symbol=="function"&&z.constructor===Symbol&&z!==Symbol.prototype?"symbol":typeof z},k=function(){function z(K,W){var G=[],Z=!0,J=!1,tt=void 0;try{for(var et=K[Symbol.iterator](),ut;!(Z=(ut=et.next()).done)&&(G.push(ut.value),!(W&&G.length===W));Z=!0);}catch(st){J=!0,tt=st}finally{try{!Z&&et.return&&et.return()}finally{if(J)throw tt}}return G}return function(K,W){if(Array.isArray(K))return K;if(Symbol.iterator in Object(K))return z(K,W);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),E=function(){function z(K,W){for(var G=0;G<W.length;G++){var Z=W[G];Z.enumerable=Z.enumerable||!1,Z.configurable=!0,"value"in Z&&(Z.writable=!0),Object.defineProperty(K,Z.key,Z)}}return function(K,W,G){return W&&z(K.prototype,W),G&&z(K,G),K}}(),p=h(3),m=y(p),c=h(2),o=y(c),e=h(0),t=y(e),a=h(5),l=y(a),u=h(10),i=y(u),r=h(9),s=y(r),n=h(36),f=h(37),w=h(13),b=y(w),_=h(26),N=h(38),g=h(39),v=h(40);function y(z){return z&&z.__esModule?z:{default:z}}function A(z,K,W){return K in z?Object.defineProperty(z,K,{value:W,enumerable:!0,configurable:!0,writable:!0}):z[K]=W,z}function x(z,K){if(!(z instanceof K))throw new TypeError("Cannot call a class as a function")}function L(z,K){if(!z)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return K&&(typeof K=="object"||typeof K=="function")?K:z}function B(z,K){if(typeof K!="function"&&K!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof K);z.prototype=Object.create(K&&K.prototype,{constructor:{value:z,enumerable:!1,writable:!0,configurable:!0}}),K&&(Object.setPrototypeOf?Object.setPrototypeOf(z,K):z.__proto__=K)}var F=(0,i.default)("quill:clipboard"),V="__ql-matcher",M=[[Node.TEXT_NODE,lt],[Node.TEXT_NODE,nt],["br",Y],[Node.ELEMENT_NODE,nt],[Node.ELEMENT_NODE,$],[Node.ELEMENT_NODE,rt],[Node.ELEMENT_NODE,H],[Node.ELEMENT_NODE,at],["li",Q],["b",U.bind(U,"bold")],["i",U.bind(U,"italic")],["style",X]],R=[n.AlignAttribute,N.DirectionAttribute].reduce(function(z,K){return z[K.keyName]=K,z},{}),O=[n.AlignStyle,f.BackgroundStyle,_.ColorStyle,N.DirectionStyle,g.FontStyle,v.SizeStyle].reduce(function(z,K){return z[K.keyName]=K,z},{}),P=function(z){B(K,z);function K(W,G){x(this,K);var Z=L(this,(K.__proto__||Object.getPrototypeOf(K)).call(this,W,G));return Z.quill.root.addEventListener("paste",Z.onPaste.bind(Z)),Z.container=Z.quill.addContainer("ql-clipboard"),Z.container.setAttribute("contenteditable",!0),Z.container.setAttribute("tabindex",-1),Z.matchers=[],M.concat(Z.options.matchers).forEach(function(J){var tt=k(J,2),et=tt[0],ut=tt[1];!G.matchVisual&&ut===rt||Z.addMatcher(et,ut)}),Z}return E(K,[{key:"addMatcher",value:function(G,Z){this.matchers.push([G,Z])}},{key:"convert",value:function(G){if(typeof G=="string")return this.container.innerHTML=G.replace(/\>\r?\n +\</g,"><"),this.convert();var Z=this.quill.getFormat(this.quill.selection.savedRange.index);if(Z[b.default.blotName]){var J=this.container.innerText;return this.container.innerHTML="",new o.default().insert(J,A({},b.default.blotName,Z[b.default.blotName]))}var tt=this.prepareMatching(),et=k(tt,2),ut=et[0],st=et[1],it=j(this.container,ut,st);return D(it,`
`)&&it.ops[it.ops.length-1].attributes==null&&(it=it.compose(new o.default().retain(it.length()-1).delete(1))),F.log("convert",this.container.innerHTML,it),this.container.innerHTML="",it}},{key:"dangerouslyPasteHTML",value:function(G,Z){var J=arguments.length>2&&arguments[2]!==void 0?arguments[2]:l.default.sources.API;if(typeof G=="string")this.quill.setContents(this.convert(G),Z),this.quill.setSelection(0,l.default.sources.SILENT);else{var tt=this.convert(Z);this.quill.updateContents(new o.default().retain(G).concat(tt),J),this.quill.setSelection(G+tt.length(),l.default.sources.SILENT)}}},{key:"onPaste",value:function(G){var Z=this;if(!(G.defaultPrevented||!this.quill.isEnabled())){var J=this.quill.getSelection(),tt=new o.default().retain(J.index),et=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(l.default.sources.SILENT),setTimeout(function(){tt=tt.concat(Z.convert()).delete(J.length),Z.quill.updateContents(tt,l.default.sources.USER),Z.quill.setSelection(tt.length()-J.length,l.default.sources.SILENT),Z.quill.scrollingContainer.scrollTop=et,Z.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var G=this,Z=[],J=[];return this.matchers.forEach(function(tt){var et=k(tt,2),ut=et[0],st=et[1];switch(ut){case Node.TEXT_NODE:J.push(st);break;case Node.ELEMENT_NODE:Z.push(st);break;default:[].forEach.call(G.container.querySelectorAll(ut),function(it){it[V]=it[V]||[],it[V].push(st)});break}}),[Z,J]}}]),K}(s.default);P.DEFAULTS={matchers:[],matchVisual:!0};function q(z,K,W){return(typeof K>"u"?"undefined":T(K))==="object"?Object.keys(K).reduce(function(G,Z){return q(G,Z,K[Z])},z):z.reduce(function(G,Z){return Z.attributes&&Z.attributes[K]?G.push(Z):G.insert(Z.insert,(0,m.default)({},A({},K,W),Z.attributes))},new o.default)}function C(z){if(z.nodeType!==Node.ELEMENT_NODE)return{};var K="__ql-computed-style";return z[K]||(z[K]=window.getComputedStyle(z))}function D(z,K){for(var W="",G=z.ops.length-1;G>=0&&W.length<K.length;--G){var Z=z.ops[G];if(typeof Z.insert!="string")break;W=Z.insert+W}return W.slice(-1*K.length)===K}function S(z){if(z.childNodes.length===0)return!1;var K=C(z);return["block","list-item"].indexOf(K.display)>-1}function j(z,K,W){return z.nodeType===z.TEXT_NODE?W.reduce(function(G,Z){return Z(z,G)},new o.default):z.nodeType===z.ELEMENT_NODE?[].reduce.call(z.childNodes||[],function(G,Z){var J=j(Z,K,W);return Z.nodeType===z.ELEMENT_NODE&&(J=K.reduce(function(tt,et){return et(Z,tt)},J),J=(Z[V]||[]).reduce(function(tt,et){return et(Z,tt)},J)),G.concat(J)},new o.default):new o.default}function U(z,K,W){return q(W,z,!0)}function H(z,K){var W=t.default.Attributor.Attribute.keys(z),G=t.default.Attributor.Class.keys(z),Z=t.default.Attributor.Style.keys(z),J={};return W.concat(G).concat(Z).forEach(function(tt){var et=t.default.query(tt,t.default.Scope.ATTRIBUTE);et!=null&&(J[et.attrName]=et.value(z),J[et.attrName])||(et=R[tt],et!=null&&(et.attrName===tt||et.keyName===tt)&&(J[et.attrName]=et.value(z)||void 0),et=O[tt],et!=null&&(et.attrName===tt||et.keyName===tt)&&(et=O[tt],J[et.attrName]=et.value(z)||void 0))}),Object.keys(J).length>0&&(K=q(K,J)),K}function $(z,K){var W=t.default.query(z);if(W==null)return K;if(W.prototype instanceof t.default.Embed){var G={},Z=W.value(z);Z!=null&&(G[W.blotName]=Z,K=new o.default().insert(G,W.formats(z)))}else typeof W.formats=="function"&&(K=q(K,W.blotName,W.formats(z)));return K}function Y(z,K){return D(K,`
`)||K.insert(`
`),K}function X(){return new o.default}function Q(z,K){var W=t.default.query(z);if(W==null||W.blotName!=="list-item"||!D(K,`
`))return K;for(var G=-1,Z=z.parentNode;!Z.classList.contains("ql-clipboard");)(t.default.query(Z)||{}).blotName==="list"&&(G+=1),Z=Z.parentNode;return G<=0?K:K.compose(new o.default().retain(K.length()-1).retain(1,{indent:G}))}function nt(z,K){return D(K,`
`)||(S(z)||K.length()>0&&z.nextSibling&&S(z.nextSibling))&&K.insert(`
`),K}function rt(z,K){if(S(z)&&z.nextElementSibling!=null&&!D(K,`

`)){var W=z.offsetHeight+parseFloat(C(z).marginTop)+parseFloat(C(z).marginBottom);z.nextElementSibling.offsetTop>z.offsetTop+W*1.5&&K.insert(`
`)}return K}function at(z,K){var W={},G=z.style||{};return G.fontStyle&&C(z).fontStyle==="italic"&&(W.italic=!0),G.fontWeight&&(C(z).fontWeight.startsWith("bold")||parseInt(C(z).fontWeight)>=700)&&(W.bold=!0),Object.keys(W).length>0&&(K=q(K,W)),parseFloat(G.textIndent||0)>0&&(K=new o.default().insert("	").concat(K)),K}function lt(z,K){var W=z.data;if(z.parentNode.tagName==="O:P")return K.insert(W.trim());if(W.trim().length===0&&z.parentNode.classList.contains("ql-clipboard"))return K;if(!C(z.parentNode).whiteSpace.startsWith("pre")){var G=function(J,tt){return tt=tt.replace(/[^\u00a0]/g,""),tt.length<1&&J?" ":tt};W=W.replace(/\r\n/g," ").replace(/\n/g," "),W=W.replace(/\s\s+/g,G.bind(G,!0)),(z.previousSibling==null&&S(z.parentNode)||z.previousSibling!=null&&S(z.previousSibling))&&(W=W.replace(/^\s+/,G.bind(G,!1))),(z.nextSibling==null&&S(z.parentNode)||z.nextSibling!=null&&S(z.nextSibling))&&(W=W.replace(/\s+$/,G.bind(G,!1)))}return K.insert(W)}d.default=P,d.matchAttributor=H,d.matchBlot=$,d.matchNewline=nt,d.matchSpacing=rt,d.matchText=lt},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function a(l,u){for(var i=0;i<u.length;i++){var r=u[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(l,r.key,r)}}return function(l,u,i){return u&&a(l.prototype,u),i&&a(l,i),l}}(),k=function a(l,u,i){l===null&&(l=Function.prototype);var r=Object.getOwnPropertyDescriptor(l,u);if(r===void 0){var s=Object.getPrototypeOf(l);return s===null?void 0:a(s,u,i)}else{if("value"in r)return r.value;var n=r.get;return n===void 0?void 0:n.call(i)}},E=h(6),p=m(E);function m(a){return a&&a.__esModule?a:{default:a}}function c(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function o(a,l){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:a}function e(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(a,l):a.__proto__=l)}var t=function(a){e(l,a);function l(){return c(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return T(l,[{key:"optimize",value:function(i){k(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"optimize",this).call(this,i),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return k(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),l}(p.default);t.blotName="bold",t.tagName=["STRONG","B"],d.default=t},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.addControls=d.default=void 0;var T=function(){function v(y,A){var x=[],L=!0,B=!1,F=void 0;try{for(var V=y[Symbol.iterator](),M;!(L=(M=V.next()).done)&&(x.push(M.value),!(A&&x.length===A));L=!0);}catch(R){B=!0,F=R}finally{try{!L&&V.return&&V.return()}finally{if(B)throw F}}return x}return function(y,A){if(Array.isArray(y))return y;if(Symbol.iterator in Object(y))return v(y,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function v(y,A){for(var x=0;x<A.length;x++){var L=A[x];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(y,L.key,L)}}return function(y,A,x){return A&&v(y.prototype,A),x&&v(y,x),y}}(),E=h(2),p=i(E),m=h(0),c=i(m),o=h(5),e=i(o),t=h(10),a=i(t),l=h(9),u=i(l);function i(v){return v&&v.__esModule?v:{default:v}}function r(v,y,A){return y in v?Object.defineProperty(v,y,{value:A,enumerable:!0,configurable:!0,writable:!0}):v[y]=A,v}function s(v,y){if(!(v instanceof y))throw new TypeError("Cannot call a class as a function")}function n(v,y){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return y&&(typeof y=="object"||typeof y=="function")?y:v}function f(v,y){if(typeof y!="function"&&y!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof y);v.prototype=Object.create(y&&y.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),y&&(Object.setPrototypeOf?Object.setPrototypeOf(v,y):v.__proto__=y)}var w=(0,a.default)("quill:toolbar"),b=function(v){f(y,v);function y(A,x){s(this,y);var L=n(this,(y.__proto__||Object.getPrototypeOf(y)).call(this,A,x));if(Array.isArray(L.options.container)){var B=document.createElement("div");N(B,L.options.container),A.container.parentNode.insertBefore(B,A.container),L.container=B}else typeof L.options.container=="string"?L.container=document.querySelector(L.options.container):L.container=L.options.container;if(!(L.container instanceof HTMLElement)){var F;return F=w.error("Container required for toolbar",L.options),n(L,F)}return L.container.classList.add("ql-toolbar"),L.controls=[],L.handlers={},Object.keys(L.options.handlers).forEach(function(V){L.addHandler(V,L.options.handlers[V])}),[].forEach.call(L.container.querySelectorAll("button, select"),function(V){L.attach(V)}),L.quill.on(e.default.events.EDITOR_CHANGE,function(V,M){V===e.default.events.SELECTION_CHANGE&&L.update(M)}),L.quill.on(e.default.events.SCROLL_OPTIMIZE,function(){var V=L.quill.selection.getRange(),M=T(V,1),R=M[0];L.update(R)}),L}return k(y,[{key:"addHandler",value:function(x,L){this.handlers[x]=L}},{key:"attach",value:function(x){var L=this,B=[].find.call(x.classList,function(V){return V.indexOf("ql-")===0});if(B){if(B=B.slice(3),x.tagName==="BUTTON"&&x.setAttribute("type","button"),this.handlers[B]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[B]==null){w.warn("ignoring attaching to disabled format",B,x);return}if(c.default.query(B)==null){w.warn("ignoring attaching to nonexistent format",B,x);return}}var F=x.tagName==="SELECT"?"change":"click";x.addEventListener(F,function(V){var M=void 0;if(x.tagName==="SELECT"){if(x.selectedIndex<0)return;var R=x.options[x.selectedIndex];R.hasAttribute("selected")?M=!1:M=R.value||!1}else x.classList.contains("ql-active")?M=!1:M=x.value||!x.hasAttribute("value"),V.preventDefault();L.quill.focus();var O=L.quill.selection.getRange(),P=T(O,1),q=P[0];if(L.handlers[B]!=null)L.handlers[B].call(L,M);else if(c.default.query(B).prototype instanceof c.default.Embed){if(M=prompt("Enter "+B),!M)return;L.quill.updateContents(new p.default().retain(q.index).delete(q.length).insert(r({},B,M)),e.default.sources.USER)}else L.quill.format(B,M,e.default.sources.USER);L.update(q)}),this.controls.push([B,x])}}},{key:"update",value:function(x){var L=x==null?{}:this.quill.getFormat(x);this.controls.forEach(function(B){var F=T(B,2),V=F[0],M=F[1];if(M.tagName==="SELECT"){var R=void 0;if(x==null)R=null;else if(L[V]==null)R=M.querySelector("option[selected]");else if(!Array.isArray(L[V])){var O=L[V];typeof O=="string"&&(O=O.replace(/\"/g,'\\"')),R=M.querySelector('option[value="'+O+'"]')}R==null?(M.value="",M.selectedIndex=-1):R.selected=!0}else if(x==null)M.classList.remove("ql-active");else if(M.hasAttribute("value")){var P=L[V]===M.getAttribute("value")||L[V]!=null&&L[V].toString()===M.getAttribute("value")||L[V]==null&&!M.getAttribute("value");M.classList.toggle("ql-active",P)}else M.classList.toggle("ql-active",L[V]!=null)})}}]),y}(u.default);b.DEFAULTS={};function _(v,y,A){var x=document.createElement("button");x.setAttribute("type","button"),x.classList.add("ql-"+y),A!=null&&(x.value=A),v.appendChild(x)}function N(v,y){Array.isArray(y[0])||(y=[y]),y.forEach(function(A){var x=document.createElement("span");x.classList.add("ql-formats"),A.forEach(function(L){if(typeof L=="string")_(x,L);else{var B=Object.keys(L)[0],F=L[B];Array.isArray(F)?g(x,B,F):_(x,B,F)}}),v.appendChild(x)})}function g(v,y,A){var x=document.createElement("select");x.classList.add("ql-"+y),A.forEach(function(L){var B=document.createElement("option");L!==!1?B.setAttribute("value",L):B.setAttribute("selected","selected"),x.appendChild(B)}),v.appendChild(x)}b.DEFAULTS={container:null,handlers:{clean:function(){var y=this,A=this.quill.getSelection();if(A!=null)if(A.length==0){var x=this.quill.getFormat();Object.keys(x).forEach(function(L){c.default.query(L,c.default.Scope.INLINE)!=null&&y.quill.format(L,!1)})}else this.quill.removeFormat(A,e.default.sources.USER)},direction:function(y){var A=this.quill.getFormat().align;y==="rtl"&&A==null?this.quill.format("align","right",e.default.sources.USER):!y&&A==="right"&&this.quill.format("align",!1,e.default.sources.USER),this.quill.format("direction",y,e.default.sources.USER)},indent:function(y){var A=this.quill.getSelection(),x=this.quill.getFormat(A),L=parseInt(x.indent||0);if(y==="+1"||y==="-1"){var B=y==="+1"?1:-1;x.direction==="rtl"&&(B*=-1),this.quill.format("indent",L+B,e.default.sources.USER)}},link:function(y){y===!0&&(y=prompt("Enter link URL:")),this.quill.format("link",y,e.default.sources.USER)},list:function(y){var A=this.quill.getSelection(),x=this.quill.getFormat(A);y==="check"?x.list==="checked"||x.list==="unchecked"?this.quill.format("list",!1,e.default.sources.USER):this.quill.format("list","unchecked",e.default.sources.USER):this.quill.format("list",y,e.default.sources.USER)}}},d.default=b,d.addControls=N},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function a(l,u){for(var i=0;i<u.length;i++){var r=u[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(l,r.key,r)}}return function(l,u,i){return u&&a(l.prototype,u),i&&a(l,i),l}}(),k=function a(l,u,i){l===null&&(l=Function.prototype);var r=Object.getOwnPropertyDescriptor(l,u);if(r===void 0){var s=Object.getPrototypeOf(l);return s===null?void 0:a(s,u,i)}else{if("value"in r)return r.value;var n=r.get;return n===void 0?void 0:n.call(i)}},E=h(28),p=m(E);function m(a){return a&&a.__esModule?a:{default:a}}function c(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function o(a,l){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:a}function e(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(a,l):a.__proto__=l)}var t=function(a){e(l,a);function l(u,i){c(this,l);var r=o(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,u));return r.label.innerHTML=i,r.container.classList.add("ql-color-picker"),[].slice.call(r.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(s){s.classList.add("ql-primary")}),r}return T(l,[{key:"buildItem",value:function(i){var r=k(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"buildItem",this).call(this,i);return r.style.backgroundColor=i.getAttribute("value")||"",r}},{key:"selectItem",value:function(i,r){k(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,i,r);var s=this.label.querySelector(".ql-color-label"),n=i&&i.getAttribute("data-value")||"";s&&(s.tagName==="line"?s.style.stroke=n:s.style.fill=n)}}]),l}(p.default);d.default=t},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function a(l,u){for(var i=0;i<u.length;i++){var r=u[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(l,r.key,r)}}return function(l,u,i){return u&&a(l.prototype,u),i&&a(l,i),l}}(),k=function a(l,u,i){l===null&&(l=Function.prototype);var r=Object.getOwnPropertyDescriptor(l,u);if(r===void 0){var s=Object.getPrototypeOf(l);return s===null?void 0:a(s,u,i)}else{if("value"in r)return r.value;var n=r.get;return n===void 0?void 0:n.call(i)}},E=h(28),p=m(E);function m(a){return a&&a.__esModule?a:{default:a}}function c(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function o(a,l){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:a}function e(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(a,l):a.__proto__=l)}var t=function(a){e(l,a);function l(u,i){c(this,l);var r=o(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,u));return r.container.classList.add("ql-icon-picker"),[].forEach.call(r.container.querySelectorAll(".ql-picker-item"),function(s){s.innerHTML=i[s.getAttribute("data-value")||""]}),r.defaultItem=r.container.querySelector(".ql-selected"),r.selectItem(r.defaultItem),r}return T(l,[{key:"selectItem",value:function(i,r){k(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,i,r),i=i||this.defaultItem,this.label.innerHTML=i.innerHTML}}]),l}(p.default);d.default=t},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function p(m,c){for(var o=0;o<c.length;o++){var e=c[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(m,e.key,e)}}return function(m,c,o){return c&&p(m.prototype,c),o&&p(m,o),m}}();function k(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}var E=function(){function p(m,c){var o=this;k(this,p),this.quill=m,this.boundsContainer=c||document.body,this.root=m.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){o.root.style.marginTop=-1*o.quill.root.scrollTop+"px"}),this.hide()}return T(p,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(c){var o=c.left+c.width/2-this.root.offsetWidth/2,e=c.bottom+this.quill.root.scrollTop;this.root.style.left=o+"px",this.root.style.top=e+"px",this.root.classList.remove("ql-flip");var t=this.boundsContainer.getBoundingClientRect(),a=this.root.getBoundingClientRect(),l=0;if(a.right>t.right&&(l=t.right-a.right,this.root.style.left=o+l+"px"),a.left<t.left&&(l=t.left-a.left,this.root.style.left=o+l+"px"),a.bottom>t.bottom){var u=a.bottom-a.top,i=c.bottom-c.top+u;this.root.style.top=e-i+"px",this.root.classList.add("ql-flip")}return l}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),p}();d.default=E},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function g(v,y){var A=[],x=!0,L=!1,B=void 0;try{for(var F=v[Symbol.iterator](),V;!(x=(V=F.next()).done)&&(A.push(V.value),!(y&&A.length===y));x=!0);}catch(M){L=!0,B=M}finally{try{!x&&F.return&&F.return()}finally{if(L)throw B}}return A}return function(v,y){if(Array.isArray(v))return v;if(Symbol.iterator in Object(v))return g(v,y);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function g(v,y,A){v===null&&(v=Function.prototype);var x=Object.getOwnPropertyDescriptor(v,y);if(x===void 0){var L=Object.getPrototypeOf(v);return L===null?void 0:g(L,y,A)}else{if("value"in x)return x.value;var B=x.get;return B===void 0?void 0:B.call(A)}},E=function(){function g(v,y){for(var A=0;A<y.length;A++){var x=y[A];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(v,x.key,x)}}return function(v,y,A){return y&&g(v.prototype,y),A&&g(v,A),v}}(),p=h(3),m=s(p),c=h(8),o=s(c),e=h(43),t=s(e),a=h(27),l=s(a),u=h(15),i=h(41),r=s(i);function s(g){return g&&g.__esModule?g:{default:g}}function n(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}function f(g,v){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:g}function w(g,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);g.prototype=Object.create(v&&v.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(g,v):g.__proto__=v)}var b=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],_=function(g){w(v,g);function v(y,A){n(this,v),A.modules.toolbar!=null&&A.modules.toolbar.container==null&&(A.modules.toolbar.container=b);var x=f(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,y,A));return x.quill.container.classList.add("ql-snow"),x}return E(v,[{key:"extendToolbar",value:function(A){A.container.classList.add("ql-snow"),this.buildButtons([].slice.call(A.container.querySelectorAll("button")),r.default),this.buildPickers([].slice.call(A.container.querySelectorAll("select")),r.default),this.tooltip=new N(this.quill,this.options.bounds),A.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(x,L){A.handlers.link.call(A,!L.format.link)})}}]),v}(t.default);_.DEFAULTS=(0,m.default)(!0,{},t.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(v){if(v){var y=this.quill.getSelection();if(y==null||y.length==0)return;var A=this.quill.getText(y);/^\S+@\S+\.\S+$/.test(A)&&A.indexOf("mailto:")!==0&&(A="mailto:"+A);var x=this.quill.theme.tooltip;x.edit("link",A)}else this.quill.format("link",!1)}}}}});var N=function(g){w(v,g);function v(y,A){n(this,v);var x=f(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,y,A));return x.preview=x.root.querySelector("a.ql-preview"),x}return E(v,[{key:"listen",value:function(){var A=this;k(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(x){A.root.classList.contains("ql-editing")?A.save():A.edit("link",A.preview.textContent),x.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(x){if(A.linkRange!=null){var L=A.linkRange;A.restoreFocus(),A.quill.formatText(L,"link",!1,o.default.sources.USER),delete A.linkRange}x.preventDefault(),A.hide()}),this.quill.on(o.default.events.SELECTION_CHANGE,function(x,L,B){if(x!=null){if(x.length===0&&B===o.default.sources.USER){var F=A.quill.scroll.descendant(l.default,x.index),V=T(F,2),M=V[0],R=V[1];if(M!=null){A.linkRange=new u.Range(x.index-R,M.length());var O=l.default.formats(M.domNode);A.preview.textContent=O,A.preview.setAttribute("href",O),A.show(),A.position(A.quill.getBounds(A.linkRange));return}}else delete A.linkRange;A.hide()}})}},{key:"show",value:function(){k(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),v}(e.BaseTooltip);N.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),d.default=_},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(29),k=Z(T),E=h(36),p=h(38),m=h(64),c=h(65),o=Z(c),e=h(66),t=Z(e),a=h(67),l=Z(a),u=h(37),i=h(26),r=h(39),s=h(40),n=h(56),f=Z(n),w=h(68),b=Z(w),_=h(27),N=Z(_),g=h(69),v=Z(g),y=h(70),A=Z(y),x=h(71),L=Z(x),B=h(72),F=Z(B),V=h(73),M=Z(V),R=h(13),O=Z(R),P=h(74),q=Z(P),C=h(75),D=Z(C),S=h(57),j=Z(S),U=h(41),H=Z(U),$=h(28),Y=Z($),X=h(59),Q=Z(X),nt=h(60),rt=Z(nt),at=h(61),lt=Z(at),z=h(108),K=Z(z),W=h(62),G=Z(W);function Z(J){return J&&J.__esModule?J:{default:J}}k.default.register({"attributors/attribute/direction":p.DirectionAttribute,"attributors/class/align":E.AlignClass,"attributors/class/background":u.BackgroundClass,"attributors/class/color":i.ColorClass,"attributors/class/direction":p.DirectionClass,"attributors/class/font":r.FontClass,"attributors/class/size":s.SizeClass,"attributors/style/align":E.AlignStyle,"attributors/style/background":u.BackgroundStyle,"attributors/style/color":i.ColorStyle,"attributors/style/direction":p.DirectionStyle,"attributors/style/font":r.FontStyle,"attributors/style/size":s.SizeStyle},!0),k.default.register({"formats/align":E.AlignClass,"formats/direction":p.DirectionClass,"formats/indent":m.IndentClass,"formats/background":u.BackgroundStyle,"formats/color":i.ColorStyle,"formats/font":r.FontClass,"formats/size":s.SizeClass,"formats/blockquote":o.default,"formats/code-block":O.default,"formats/header":t.default,"formats/list":l.default,"formats/bold":f.default,"formats/code":R.Code,"formats/italic":b.default,"formats/link":N.default,"formats/script":v.default,"formats/strike":A.default,"formats/underline":L.default,"formats/image":F.default,"formats/video":M.default,"formats/list/item":a.ListItem,"modules/formula":q.default,"modules/syntax":D.default,"modules/toolbar":j.default,"themes/bubble":K.default,"themes/snow":G.default,"ui/icons":H.default,"ui/picker":Y.default,"ui/icon-picker":rt.default,"ui/color-picker":Q.default,"ui/tooltip":lt.default},!0),d.default=k.default},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.IndentClass=void 0;var T=function(){function l(u,i){for(var r=0;r<i.length;r++){var s=i[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(u,s.key,s)}}return function(u,i,r){return i&&l(u.prototype,i),r&&l(u,r),u}}(),k=function l(u,i,r){u===null&&(u=Function.prototype);var s=Object.getOwnPropertyDescriptor(u,i);if(s===void 0){var n=Object.getPrototypeOf(u);return n===null?void 0:l(n,i,r)}else{if("value"in s)return s.value;var f=s.get;return f===void 0?void 0:f.call(r)}},E=h(0),p=m(E);function m(l){return l&&l.__esModule?l:{default:l}}function c(l,u){if(!(l instanceof u))throw new TypeError("Cannot call a class as a function")}function o(l,u){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:l}function e(l,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);l.prototype=Object.create(u&&u.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(l,u):l.__proto__=u)}var t=function(l){e(u,l);function u(){return c(this,u),o(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return T(u,[{key:"add",value:function(r,s){if(s==="+1"||s==="-1"){var n=this.value(r)||0;s=s==="+1"?n+1:n-1}return s===0?(this.remove(r),!0):k(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"add",this).call(this,r,s)}},{key:"canAdd",value:function(r,s){return k(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"canAdd",this).call(this,r,s)||k(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"canAdd",this).call(this,r,parseInt(s))}},{key:"value",value:function(r){return parseInt(k(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"value",this).call(this,r))||void 0}}]),u}(p.default.Attributor.Class),a=new t("indent","ql-indent",{scope:p.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});d.IndentClass=a},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(4),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function c(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){c(t,e);function t(){return p(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(k.default);o.blotName="blockquote",o.tagName="blockquote",d.default=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function t(a,l){for(var u=0;u<l.length;u++){var i=l[u];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(a,i.key,i)}}return function(a,l,u){return l&&t(a.prototype,l),u&&t(a,u),a}}(),k=h(4),E=p(k);function p(t){return t&&t.__esModule?t:{default:t}}function m(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}function c(t,a){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:t}function o(t,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);t.prototype=Object.create(a&&a.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(t,a):t.__proto__=a)}var e=function(t){o(a,t);function a(){return m(this,a),c(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return T(a,null,[{key:"formats",value:function(u){return this.tagName.indexOf(u.tagName)+1}}]),a}(E.default);e.blotName="header",e.tagName=["H1","H2","H3","H4","H5","H6"],d.default=e},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.ListItem=void 0;var T=function(){function n(f,w){for(var b=0;b<w.length;b++){var _=w[b];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(f,_.key,_)}}return function(f,w,b){return w&&n(f.prototype,w),b&&n(f,b),f}}(),k=function n(f,w,b){f===null&&(f=Function.prototype);var _=Object.getOwnPropertyDescriptor(f,w);if(_===void 0){var N=Object.getPrototypeOf(f);return N===null?void 0:n(N,w,b)}else{if("value"in _)return _.value;var g=_.get;return g===void 0?void 0:g.call(b)}},E=h(0),p=t(E),m=h(4),c=t(m),o=h(25),e=t(o);function t(n){return n&&n.__esModule?n:{default:n}}function a(n,f,w){return f in n?Object.defineProperty(n,f,{value:w,enumerable:!0,configurable:!0,writable:!0}):n[f]=w,n}function l(n,f){if(!(n instanceof f))throw new TypeError("Cannot call a class as a function")}function u(n,f){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:n}function i(n,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);n.prototype=Object.create(f&&f.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(n,f):n.__proto__=f)}var r=function(n){i(f,n);function f(){return l(this,f),u(this,(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments))}return T(f,[{key:"format",value:function(b,_){b===s.blotName&&!_?this.replaceWith(p.default.create(this.statics.scope)):k(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"format",this).call(this,b,_)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():k(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(b,_){return this.parent.isolate(this.offset(this.parent),this.length()),b===this.parent.statics.blotName?(this.parent.replaceWith(b,_),this):(this.parent.unwrap(),k(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"replaceWith",this).call(this,b,_))}}],[{key:"formats",value:function(b){return b.tagName===this.tagName?void 0:k(f.__proto__||Object.getPrototypeOf(f),"formats",this).call(this,b)}}]),f}(c.default);r.blotName="list-item",r.tagName="LI";var s=function(n){i(f,n),T(f,null,[{key:"create",value:function(b){var _=b==="ordered"?"OL":"UL",N=k(f.__proto__||Object.getPrototypeOf(f),"create",this).call(this,_);return(b==="checked"||b==="unchecked")&&N.setAttribute("data-checked",b==="checked"),N}},{key:"formats",value:function(b){if(b.tagName==="OL")return"ordered";if(b.tagName==="UL")return b.hasAttribute("data-checked")?b.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function f(w){l(this,f);var b=u(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,w)),_=function(g){if(g.target.parentNode===w){var v=b.statics.formats(w),y=p.default.find(g.target);v==="checked"?y.format("list","unchecked"):v==="unchecked"&&y.format("list","checked")}};return w.addEventListener("touchstart",_),w.addEventListener("mousedown",_),b}return T(f,[{key:"format",value:function(b,_){this.children.length>0&&this.children.tail.format(b,_)}},{key:"formats",value:function(){return a({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(b,_){if(b instanceof r)k(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"insertBefore",this).call(this,b,_);else{var N=_==null?this.length():_.offset(this),g=this.split(N);g.parent.insertBefore(b,g)}}},{key:"optimize",value:function(b){k(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"optimize",this).call(this,b);var _=this.next;_!=null&&_.prev===this&&_.statics.blotName===this.statics.blotName&&_.domNode.tagName===this.domNode.tagName&&_.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(_.moveChildren(this),_.remove())}},{key:"replace",value:function(b){if(b.statics.blotName!==this.statics.blotName){var _=p.default.create(this.statics.defaultChild);b.moveChildren(_),this.appendChild(_)}k(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"replace",this).call(this,b)}}]),f}(e.default);s.blotName="list",s.scope=p.default.Scope.BLOCK_BLOT,s.tagName=["OL","UL"],s.defaultChild="list-item",s.allowedChildren=[r],d.ListItem=r,d.default=s},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(56),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function c(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){c(t,e);function t(){return p(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(k.default);o.blotName="italic",o.tagName=["EM","I"],d.default=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function a(l,u){for(var i=0;i<u.length;i++){var r=u[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(l,r.key,r)}}return function(l,u,i){return u&&a(l.prototype,u),i&&a(l,i),l}}(),k=function a(l,u,i){l===null&&(l=Function.prototype);var r=Object.getOwnPropertyDescriptor(l,u);if(r===void 0){var s=Object.getPrototypeOf(l);return s===null?void 0:a(s,u,i)}else{if("value"in r)return r.value;var n=r.get;return n===void 0?void 0:n.call(i)}},E=h(6),p=m(E);function m(a){return a&&a.__esModule?a:{default:a}}function c(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function o(a,l){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:a}function e(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(a,l):a.__proto__=l)}var t=function(a){e(l,a);function l(){return c(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return T(l,null,[{key:"create",value:function(i){return i==="super"?document.createElement("sup"):i==="sub"?document.createElement("sub"):k(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,i)}},{key:"formats",value:function(i){if(i.tagName==="SUB")return"sub";if(i.tagName==="SUP")return"super"}}]),l}(p.default);t.blotName="script",t.tagName=["SUB","SUP"],d.default=t},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(6),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function c(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){c(t,e);function t(){return p(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(k.default);o.blotName="strike",o.tagName="S",d.default=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=h(6),k=E(T);function E(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function c(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){c(t,e);function t(){return p(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(k.default);o.blotName="underline",o.tagName="U",d.default=o},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function u(i,r){for(var s=0;s<r.length;s++){var n=r[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,r,s){return r&&u(i.prototype,r),s&&u(i,s),i}}(),k=function u(i,r,s){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,r);if(n===void 0){var f=Object.getPrototypeOf(i);return f===null?void 0:u(f,r,s)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(s)}},E=h(0),p=c(E),m=h(27);function c(u){return u&&u.__esModule?u:{default:u}}function o(u,i){if(!(u instanceof i))throw new TypeError("Cannot call a class as a function")}function e(u,i){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:u}function t(u,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);u.prototype=Object.create(i&&i.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(u,i):u.__proto__=i)}var a=["alt","height","width"],l=function(u){t(i,u);function i(){return o(this,i),e(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return T(i,[{key:"format",value:function(s,n){a.indexOf(s)>-1?n?this.domNode.setAttribute(s,n):this.domNode.removeAttribute(s):k(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,s,n)}}],[{key:"create",value:function(s){var n=k(i.__proto__||Object.getPrototypeOf(i),"create",this).call(this,s);return typeof s=="string"&&n.setAttribute("src",this.sanitize(s)),n}},{key:"formats",value:function(s){return a.reduce(function(n,f){return s.hasAttribute(f)&&(n[f]=s.getAttribute(f)),n},{})}},{key:"match",value:function(s){return/\.(jpe?g|gif|png)$/.test(s)||/^data:image\/.+;base64/.test(s)}},{key:"sanitize",value:function(s){return(0,m.sanitize)(s,["http","https","data"])?s:"//:0"}},{key:"value",value:function(s){return s.getAttribute("src")}}]),i}(p.default.Embed);l.blotName="image",l.tagName="IMG",d.default=l},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0});var T=function(){function u(i,r){for(var s=0;s<r.length;s++){var n=r[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,r,s){return r&&u(i.prototype,r),s&&u(i,s),i}}(),k=function u(i,r,s){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,r);if(n===void 0){var f=Object.getPrototypeOf(i);return f===null?void 0:u(f,r,s)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(s)}},E=h(4),p=h(27),m=c(p);function c(u){return u&&u.__esModule?u:{default:u}}function o(u,i){if(!(u instanceof i))throw new TypeError("Cannot call a class as a function")}function e(u,i){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:u}function t(u,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);u.prototype=Object.create(i&&i.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(u,i):u.__proto__=i)}var a=["height","width"],l=function(u){t(i,u);function i(){return o(this,i),e(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return T(i,[{key:"format",value:function(s,n){a.indexOf(s)>-1?n?this.domNode.setAttribute(s,n):this.domNode.removeAttribute(s):k(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,s,n)}}],[{key:"create",value:function(s){var n=k(i.__proto__||Object.getPrototypeOf(i),"create",this).call(this,s);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("src",this.sanitize(s)),n}},{key:"formats",value:function(s){return a.reduce(function(n,f){return s.hasAttribute(f)&&(n[f]=s.getAttribute(f)),n},{})}},{key:"sanitize",value:function(s){return m.default.sanitize(s)}},{key:"value",value:function(s){return s.getAttribute("src")}}]),i}(E.BlockEmbed);l.blotName="video",l.className="ql-video",l.tagName="IFRAME",d.default=l},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.FormulaBlot=void 0;var T=function(){function s(n,f){for(var w=0;w<f.length;w++){var b=f[w];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(n,b.key,b)}}return function(n,f,w){return f&&s(n.prototype,f),w&&s(n,w),n}}(),k=function s(n,f,w){n===null&&(n=Function.prototype);var b=Object.getOwnPropertyDescriptor(n,f);if(b===void 0){var _=Object.getPrototypeOf(n);return _===null?void 0:s(_,f,w)}else{if("value"in b)return b.value;var N=b.get;return N===void 0?void 0:N.call(w)}},E=h(35),p=t(E),m=h(5),c=t(m),o=h(9),e=t(o);function t(s){return s&&s.__esModule?s:{default:s}}function a(s,n){if(!(s instanceof n))throw new TypeError("Cannot call a class as a function")}function l(s,n){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:s}function u(s,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);s.prototype=Object.create(n&&n.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(s,n):s.__proto__=n)}var i=function(s){u(n,s);function n(){return a(this,n),l(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return T(n,null,[{key:"create",value:function(w){var b=k(n.__proto__||Object.getPrototypeOf(n),"create",this).call(this,w);return typeof w=="string"&&(window.katex.render(w,b,{throwOnError:!1,errorColor:"#f00"}),b.setAttribute("data-value",w)),b}},{key:"value",value:function(w){return w.getAttribute("data-value")}}]),n}(p.default);i.blotName="formula",i.className="ql-formula",i.tagName="SPAN";var r=function(s){u(n,s),T(n,null,[{key:"register",value:function(){c.default.register(i,!0)}}]);function n(){a(this,n);var f=l(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return f}return n}(e.default);d.FormulaBlot=i,d.default=r},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.CodeToken=d.CodeBlock=void 0;var T=function(){function w(b,_){for(var N=0;N<_.length;N++){var g=_[N];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(b,g.key,g)}}return function(b,_,N){return _&&w(b.prototype,_),N&&w(b,N),b}}(),k=function w(b,_,N){b===null&&(b=Function.prototype);var g=Object.getOwnPropertyDescriptor(b,_);if(g===void 0){var v=Object.getPrototypeOf(b);return v===null?void 0:w(v,_,N)}else{if("value"in g)return g.value;var y=g.get;return y===void 0?void 0:y.call(N)}},E=h(0),p=l(E),m=h(5),c=l(m),o=h(9),e=l(o),t=h(13),a=l(t);function l(w){return w&&w.__esModule?w:{default:w}}function u(w,b){if(!(w instanceof b))throw new TypeError("Cannot call a class as a function")}function i(w,b){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return b&&(typeof b=="object"||typeof b=="function")?b:w}function r(w,b){if(typeof b!="function"&&b!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof b);w.prototype=Object.create(b&&b.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(w,b):w.__proto__=b)}var s=function(w){r(b,w);function b(){return u(this,b),i(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}return T(b,[{key:"replaceWith",value:function(N){this.domNode.textContent=this.domNode.textContent,this.attach(),k(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"replaceWith",this).call(this,N)}},{key:"highlight",value:function(N){var g=this.domNode.textContent;this.cachedText!==g&&((g.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=N(g),this.domNode.normalize(),this.attach()),this.cachedText=g)}}]),b}(a.default);s.className="ql-syntax";var n=new p.default.Attributor.Class("token","hljs",{scope:p.default.Scope.INLINE}),f=function(w){r(b,w),T(b,null,[{key:"register",value:function(){c.default.register(n,!0),c.default.register(s,!0)}}]);function b(_,N){u(this,b);var g=i(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,_,N));if(typeof g.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var v=null;return g.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(v),v=setTimeout(function(){g.highlight(),v=null},g.options.interval)}),g.highlight(),g}return T(b,[{key:"highlight",value:function(){var N=this;if(!this.quill.selection.composing){this.quill.update(c.default.sources.USER);var g=this.quill.getSelection();this.quill.scroll.descendants(s).forEach(function(v){v.highlight(N.options.highlight)}),this.quill.update(c.default.sources.SILENT),g!=null&&this.quill.setSelection(g,c.default.sources.SILENT)}}}]),b}(e.default);f.DEFAULTS={highlight:function(){return window.hljs==null?null:function(w){var b=window.hljs.highlightAuto(w);return b.value}}(),interval:1e3},d.CodeBlock=s,d.CodeToken=n,d.default=f},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(I,d){I.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(I,d){I.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(I,d){I.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(I,d){I.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(I,d){I.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(I,d,h){"use strict";Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.BubbleTooltip=void 0;var T=function b(_,N,g){_===null&&(_=Function.prototype);var v=Object.getOwnPropertyDescriptor(_,N);if(v===void 0){var y=Object.getPrototypeOf(_);return y===null?void 0:b(y,N,g)}else{if("value"in v)return v.value;var A=v.get;return A===void 0?void 0:A.call(g)}},k=function(){function b(_,N){for(var g=0;g<N.length;g++){var v=N[g];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(_,v.key,v)}}return function(_,N,g){return N&&b(_.prototype,N),g&&b(_,g),_}}(),E=h(3),p=u(E),m=h(8),c=u(m),o=h(43),e=u(o),t=h(15),a=h(41),l=u(a);function u(b){return b&&b.__esModule?b:{default:b}}function i(b,_){if(!(b instanceof _))throw new TypeError("Cannot call a class as a function")}function r(b,_){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:b}function s(b,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);b.prototype=Object.create(_&&_.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(b,_):b.__proto__=_)}var n=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],f=function(b){s(_,b);function _(N,g){i(this,_),g.modules.toolbar!=null&&g.modules.toolbar.container==null&&(g.modules.toolbar.container=n);var v=r(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,N,g));return v.quill.container.classList.add("ql-bubble"),v}return k(_,[{key:"extendToolbar",value:function(g){this.tooltip=new w(this.quill,this.options.bounds),this.tooltip.root.appendChild(g.container),this.buildButtons([].slice.call(g.container.querySelectorAll("button")),l.default),this.buildPickers([].slice.call(g.container.querySelectorAll("select")),l.default)}}]),_}(e.default);f.DEFAULTS=(0,p.default)(!0,{},e.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(_){_?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var w=function(b){s(_,b);function _(N,g){i(this,_);var v=r(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,N,g));return v.quill.on(c.default.events.EDITOR_CHANGE,function(y,A,x,L){if(y===c.default.events.SELECTION_CHANGE)if(A!=null&&A.length>0&&L===c.default.sources.USER){v.show(),v.root.style.left="0px",v.root.style.width="",v.root.style.width=v.root.offsetWidth+"px";var B=v.quill.getLines(A.index,A.length);if(B.length===1)v.position(v.quill.getBounds(A));else{var F=B[B.length-1],V=v.quill.getIndex(F),M=Math.min(F.length()-1,A.index+A.length-V),R=v.quill.getBounds(new t.Range(V,M));v.position(R)}}else document.activeElement!==v.textbox&&v.quill.hasFocus()&&v.hide()}),v}return k(_,[{key:"listen",value:function(){var g=this;T(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){g.root.classList.remove("ql-editing")}),this.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!g.root.classList.contains("ql-hidden")){var v=g.quill.getSelection();v!=null&&g.position(g.quill.getBounds(v))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(g){var v=T(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"position",this).call(this,g),y=this.root.querySelector(".ql-tooltip-arrow");if(y.style.marginLeft="",v===0)return v;y.style.marginLeft=-1*v-y.offsetWidth/2+"px"}}]),_}(o.BaseTooltip);w.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),d.BubbleTooltip=w,d.default=f},function(I,d,h){I.exports=h(63)}]).default})});var ft=mt(vt(),1);var _t=I=>I.toLowerCase().replace(/\s/g,"-"),Nt=(I,d)=>{let h=_t(I);return`
    [data-quill-id="${d}"] .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="${h}"]::before,
    [data-quill-id="${d}"] .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="${h}"]::before {
        content: '${I}';
    }

    [data-quill-id="${d}"] .ql-font-${h} {
        font-family: '${I}', sans-serif;
    }
    `},pt=I=>typeof I=="function",Tt=(I,d)=>`
    [data-quill-id="${d}"] .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="${I}"]::before,
    [data-quill-id="${d}"] .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="${I}"]::before {
        content: '${I}';
    }
    `,xt=I=>document.head.querySelector(`style#quill--${I.replace(".","-")}`),Ot=I=>document.querySelector(`label[for="${I}"]`),St=I=>I.ops.filter(d=>d.insert&&d.insert.image).map(d=>d.insert.image),Pt=(I,d,h)=>{if(!I)return;let k=document.querySelector(h)?.offsetHeight??0,E=new IntersectionObserver(p=>{p.forEach(m=>{m.isIntersecting?(d?.classList.remove("sticky"),d?.style.removeProperty("--sticky-offset")):(d?.classList.add("sticky"),d?.style.setProperty("--sticky-offset",`${k+2}px`))},{threshold:.25,rootMargin:`${k}px`})});return E.observe(I),E};var Et=mt(vt(),1),$t=Et.default.import("blots/block/embed"),ht=class extends $t{static create(d){let h=super.create(d);if(d===!0)return h;let T=document.createElement("img");return T.setAttribute("src",d),h.appendChild(T),h}deleteAt(d,h){super.deleteAt(d,h),this.cache={}}static value(d){let{src:h,custom:T}=d.dataset;return{src:h,custom:T}}};ht.blotName="imageBlot";ht.className="fl-ql-img-uploading";ht.tagName="span";Et.default.register({"formats/imageBlot":ht});var qt=ht;var gt=class{constructor(d,h){this.quill=d,this.options=h,this.range=null,this.placeholderDelta=null,this.allowImages=h.allowImages,pt(this.options.upload)||console.warn("[Missing config] upload function that returns a promise is required");let T=this.quill.getModule("toolbar");T&&T.addHandler("image",this.selectLocalImage.bind(this)),this.handleDrop=this.handleDrop.bind(this),this.handlePaste=this.handlePaste.bind(this),this.quill.root.addEventListener("drop",this.handleDrop,!1),this.quill.root.addEventListener("paste",this.handlePaste,!1)}selectLocalImage(){this.allowImages&&(this.quill.focus(),this.range=this.quill.getSelection(),this.fileHolder=document.createElement("input"),this.fileHolder.setAttribute("type","file"),this.fileHolder.setAttribute("accept","image/*"),this.fileHolder.setAttribute("style","visibility:hidden"),this.fileHolder.onchange=this.fileChanged.bind(this),document.body.appendChild(this.fileHolder),this.fileHolder.click(),window.requestAnimationFrame(()=>{document.body.removeChild(this.fileHolder)}))}handleDrop(d){if(d.dataTransfer&&d.dataTransfer.files&&d.dataTransfer.files.length){if(d.stopPropagation(),d.preventDefault(),!this.allowImages)return;if(document.caretRangeFromPoint){let T=document.getSelection(),k=document.caretRangeFromPoint(d.clientX,d.clientY);T&&k&&T.setBaseAndExtent(k.startContainer,k.startOffset,k.startContainer,k.startOffset)}else{let T=document.getSelection(),k=document.caretPositionFromPoint(d.clientX,d.clientY);T&&k&&T.setBaseAndExtent(k.offsetNode,k.offset,k.offsetNode,k.offset)}this.quill.focus(),this.range=this.quill.getSelection();let h=d.dataTransfer.files[0];setTimeout(()=>{this.quill.focus(),this.range=this.quill.getSelection(),this.readAndUploadFile(h)},0)}}handlePaste(d){let h=d.clipboardData||window.clipboardData;if(h&&(h.items||h.files)){let T=h.items||h.files,k=/^image\/(jpe?g|gif|png|svg|webp)$/i;for(let E=0;E<T.length;E++)if(this.allowImages&&k.test(T[E].type)){let p=T[E].getAsFile?T[E].getAsFile():T[E];p&&(this.quill.focus(),this.range=this.quill.getSelection(),d.preventDefault(),setTimeout(()=>{this.quill.focus(),this.range=this.quill.getSelection(),this.readAndUploadFile(p)},0))}}}readAndUploadFile(d){let h=!1,T=new FileReader;T.addEventListener("load",()=>{h||this.insertBase64Image(T.result)},!1),d&&T.readAsDataURL(d),this.options.upload(d).then(k=>{this.insertToEditor(k)},k=>{h=!0,this.removeBase64Image(),console.warn(k)})}fileChanged(){let d=this.fileHolder.files[0];this.readAndUploadFile(d)}insertBase64Image(d){let h=this.range;this.placeholderDelta=this.quill.insertEmbed(h.index,qt.blotName,d,"user")}insertToEditor(d){let h=this.range,T=this.calculatePlaceholderInsertLength();this.quill.deleteText(h.index,T,"api"),this.quill.insertEmbed(h.index,"image",d,"user"),h.index++,this.quill.setSelection(h,"user")}calculatePlaceholderInsertLength(){return this.placeholderDelta.ops.reduce((d,h)=>(h.hasOwnProperty("insert")&&d++,d),0)}removeBase64Image(d="user"){let h=this.range,T=this.calculatePlaceholderInsertLength();this.quill.deleteText(h.index,T??1,d)}};window.QuillImageUploader=gt;var Lt=gt;var Rt=mt(vt(),1),Vt=Rt.default.import("parchment"),yt=class extends Vt.Embed{};yt.blotName="ShiftEnter";yt.tagName="br";var It=yt;var At=mt(vt(),1);At.default.register(It);var jt=At.default.import("delta"),kt=class{constructor(d,h){this.quill=d,this.options=h,this.quill.keyboard.bindings[13].unshift({key:13,shiftKey:!0,handler:function(T){return this.quill.updateContents(new jt().retain(T.index).delete(T.length).insert({ShiftEnter:!0}),"user"),this.quill.getLeaf(T.index+1)[0].next||this.quill.updateContents(new jt().retain(T.index+1).delete(0).insert({ShiftEnter:!0}),"user"),this.quill.setSelection(T.index+1,"silent"),!1}})}},Mt=kt;ft.default.register("modules/imageUploader",Lt);ft.default.register("modules/insertBr",Mt);window.Quill=ft.default;function Zt({state:I,statePath:d,placeholder:h=null,handlers:T={},options:k={},wireId:E=void 0,allowImages:p=!0,hasHistory:m=!1,onTextChangedHandler:c=void 0,onInit:o=void 0,stickyToolbar:e=!1}){return{state:I,statePath:d,placeholder:h,options:k,handlers:T,wireId:E,allowImages:p,hasHistory:m,onTextChangedHandler:c,onInit:o,stickyToolbar:e,editorInstance:void 0,labelClickHandler:void 0,stickyObserverInstance:void 0,init(){let t=window.__quillStylesLoaded?0:25;setTimeout(()=>{this.initEditor(I.initialValue)},t),this.$watch("state",()=>{this.$refs.quill.contains(document.activeElement)||this.setEditorValue(this.state,"silent")}),this.labelClickHandler=()=>this.focus(),Ot(this.statePath)?.addEventListener("click",this.labelClickHandler),this.stickyToolbar&&(this.stickyObserverInstance=Pt(this.$refs.stickyToolbar,this.$refs.toolbar,".fi-topbar.sticky"))},destroy(){xt(this.statePath)?.remove(),Ot(this.statePath)?.removeEventListener("click",this.labelClickHandler),this.editorInstance=this.$root._editor=void 0,this.stickyObserverInstance&&this.stickyObserverInstance.disconnect()},initEditor(t){let a=this,l=a.loadFonts(),u=a.loadFontSizes();a.addStylesToDom(l+u),a.$root._editor=a.editorInstance=new ft.default(a.$refs.quill,{theme:a.options.theme,scrollingContainer:"html",placeholder:a.placeholder,modules:a.getModules()}),a.editorInstance.on("text-change",(r,s,n)=>{pt(a.onTextChangedHandler)&&a.onTextChangedHandler(r,s,n,a)===!1||(p&&a.checkForImageDifferences(s,n),a.state=a.editorInstance.root.innerHTML)});let i=a.$refs.quill.querySelector(".ql-editor");["prose","max-w-none","dark:prose-invert"].forEach(r=>i?.classList.add(r)),a.setEditorValue(t,"silent"),a.options.autofocus&&queueMicrotask(()=>a.focus()),pt(a.onInit)&&a.onInit(a.editor(),a)},wire(){return window.Livewire.find(this.wireId)},editor(){return Alpine.raw(this.editorInstance)},focus(){this.editor()?.focus()},setEditorValue(t,a="user"){let l=this.editor().clipboard.convert(t);this.editor().setContents(l,a)},loadFonts(){if(!k.fonts)return"";let t=ft.default.import("attributors/class/font");return t.whitelist=k.fonts.map(a=>_t(a)),ft.default.register(t,!0),k.fonts.map(a=>Nt(a,this.statePath)).join("")},loadFontSizes(){if(!k.fontSizes)return"";let t=ft.default.import("attributors/style/size");return t.whitelist=k.fontSizes,ft.default.register(t,!0),k.fontSizes.map(a=>Tt(a,this.statePath)).join("")},addStylesToDom(t){if(!t)return;let a=document.createElement("style");a.id=`quill--${this.statePath.replace(".","-")}`,a.innerHTML=t,document.head.appendChild(a)},getModules(){let t=this,a={};t.hasHistory&&(a.undo=function(){this.quill.history.undo()},a.redo=function(){this.quill.history.redo()});let l={toolbar:{container:t.$refs.toolbar,handlers:{...a,...T}},imageUploader:{allowImages:p,upload:u=>new Promise(i=>{t.wire()?.upload(`componentFileAttachments.${t.statePath}`,u,()=>{t.wire().getFormComponentFileAttachmentUrl(`${t.statePath}`).then(r=>{this.$dispatch("quill-image-uploaded",{url:r,statePath:t.statePath}),i(r)})})})},insertBr:{}};return t.hasHistory&&(l.history={delay:1e3,maxStack:100,userOnly:!1}),l},clearHistory({detail:{id:t}}){t===this.statePath&&this.hasHistory&&this.editor().history.clear()},checkForImageDifferences(t,a){if(a!=="user")return;let l=St(this.editor().getContents().diff(t));l.length&&this.$dispatch("quill-images-deleted",{urls:l,statePath:this.statePath})}}}export{Zt as default};
/*! Bundled license information:

quill/dist/quill.js:
  (*!
   * Quill Editor v1.3.7
   * https://quilljs.com/
   * Copyright (c) 2014, Jason Chen
   * Copyright (c) 2013, salesforce.com
   *)
*/
