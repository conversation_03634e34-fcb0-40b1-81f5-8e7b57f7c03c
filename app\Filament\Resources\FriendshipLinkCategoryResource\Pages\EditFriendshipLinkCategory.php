<?php

namespace App\Filament\Resources\FriendshipLinkCategoryResource\Pages;

use App\Filament\Resources\FriendshipLinkCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFriendshipLinkCategory extends EditRecord
{
    protected static string $resource = FriendshipLinkCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
