<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class WangEditorUploadController extends Controller
{
    public function image(Request $request)
    {
        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('wangeditor/images', 'public');
            $url = Storage::disk('public')->url($path);
            return response()->json([
                'errno' => 0,
                'data' => [ $url ],
            ]);
        }
        return response()->json(['errno' => 1, 'message' => '上传失败'], 400);
    }

    public function video(Request $request)
    {
        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('wangeditor/videos', 'public');
            $url = Storage::disk('public')->url($path);
            return response()->json([
                'errno' => 0,
                'data' => [ $url ],
            ]);
        }
        return response()->json(['errno' => 1, 'message' => '上传失败'], 400);
    }
} 