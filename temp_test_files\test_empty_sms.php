<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\Activity;
use App\Models\SmsTemplate;
use App\Models\Registration;
use App\Http\Controllers\RegistrationController;
use App\Services\SmsService;
use App\Services\WechatService;

echo "=== 创建测试活动来重现空值问题 ===\n\n";

// 创建一个测试活动
$activity = new Activity();
$activity->title = '';
$activity->save();

echo "创建了空标题的活动 ID: {$activity->id}\n";

// 获取consultation_notice模板
$template = SmsTemplate::where('code', 'consultation_notice')->first();

// 创建活动详情
$activityDetail = new ActivityDetail();
$activityDetail->activity_id = $activity->id;
$activityDetail->theme = '';
$activityDetail->activity_time = '2025-07-16 14:22:30';
$activityDetail->address = '学校多功能厅';
$activityDetail->target = '';
$activityDetail->sms_template_id = $template->id;
$activityDetail->save();

echo "创建了活动详情 ID: {$activityDetail->id}\n";

// 创建注册记录
$registration = new Registration();
$registration->name = '测试用户';
$registration->phone = '15168356799';
$registration->activity_detail_id = $activityDetail->id;
$registration->save();

echo "创建了注册记录 ID: {$registration->id}\n";

// 重新加载数据以确保关联正确
$registration = Registration::with(['activityDetail.activity', 'activityDetail.smsTemplate'])
    ->find($registration->id);

echo "\n=== 测试buildTemplateData方法 ===\n";

// 手动构建模板数据
$activityDetail = $registration->activityDetail;
$templateData = [
    'name' => $registration->name,
    'topic' => $activityDetail->activity ? $activityDetail->activity->title : '',
    'time' => $activityDetail->activity_time,
    'address' => $activityDetail->address,
    'obj' => $activityDetail->target
];

echo "模板数据:\n";
foreach ($templateData as $key => $value) {
    echo "  {$key}: '{$value}'" . (empty($value) ? ' (空值!)' : '') . "\n";
}

// 模拟SMS参数处理
echo "\n=== 模拟SMS参数处理 ===\n";
$pairs = [];
foreach ($templateData as $key => $value) {
    $smsKey = "#{$key}#";
    $pairs[] = urlencode($smsKey) . '=' . urlencode($value);
}
$tplValue = implode('&', $pairs);

echo "tpl_value: {$tplValue}\n";

// 解码验证
echo "\n=== 解码验证 ===\n";
parse_str($tplValue, $decoded);
foreach ($decoded as $key => $value) {
    echo "  {$key}: '{$value}'" . (empty($value) ? ' (空值!)' : '') . "\n";
}

echo "\n=== 清理测试数据 ===\n";
$registration->delete();
$activityDetail->delete();
$activity->delete();
echo "测试数据已清理\n";

echo "\n测试完成。这个测试重现了空值问题。\n";