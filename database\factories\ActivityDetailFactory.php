<?php

namespace Database\Factories;

use App\Models\ActivityDetail;
use App\Models\Activity;
use Illuminate\Database\Eloquent\Factories\Factory;

class ActivityDetailFactory extends Factory
{
    protected $model = ActivityDetail::class;

    public function definition(): array
    {
        return [
            'activity_id' => Activity::factory(),
            'theme' => $this->faker->sentence(),
            'deadline' => $this->faker->dateTimeThisMonth(),
            'quota' => $this->faker->numberBetween(10, 100),
            'target_audience' => $this->faker->randomElement(['学生', '教师', '家长', '社会人士']),
            'current_count' => 0,
            'activity_time' => $this->faker->dateTimeThisMonth(),
            'fee' => $this->faker->randomElement(['免费', '50元', '100元', '200元']),
            'reminder' => $this->faker->paragraph(),
            'registration_method' => $this->faker->randomElement(['线上报名', '电话报名', '现场报名']),
            'address' => $this->faker->address(),
            'sms_template' => $this->faker->paragraph(),
        ];
    }
} 