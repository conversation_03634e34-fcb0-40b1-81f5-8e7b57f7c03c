<?php

namespace App\Filament\Resources\FriendshipLinkResource\Pages;

use App\Filament\Resources\FriendshipLinkResource;
use Filament\Resources\Pages\CreateRecord;

class CreateFriendshipLink extends CreateRecord
{
    protected static string $resource = FriendshipLinkResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
