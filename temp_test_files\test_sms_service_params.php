<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 测试SmsService参数处理 ===\n";

// 模拟SmsService中的tpl_value构建逻辑
function buildTplValue($data) {
    $tplValue = '';
    if (!empty($data)) {
        $pairs = [];
        foreach ($data as $key => $value) {
            // 确保变量名包含#号
            $varName = strpos($key, '#') === false ? "#{$key}#" : $key;
            $pairs[] = urlencode($varName) . '=' . urlencode($value);
        }
        $tplValue = implode('&', $pairs);
    }
    return $tplValue;
}

// 测试数据1：不带#号的键名
$data1 = [
    "name" => "林秀英",
    "topic" => "2024年春季教学研讨会 - 第一场",
    "time" => "2025年07月01日 14:22",
    "address" => "学校多功能厅",
    "obj" => "小学教师"
];

echo "\n=== 测试数据1（不带#号） ===\n";
foreach ($data1 as $key => $value) {
    echo "{$key}: '{$value}'\n";
}

$tplValue1 = buildTplValue($data1);
echo "\n构建的tpl_value:\n{$tplValue1}\n";

// 测试数据2：带#号的键名
$data2 = [
    "#name#" => "林秀英",
    "#topic#" => "2024年春季教学研讨会 - 第一场",
    "#time#" => "2025年07月01日 14:22",
    "#address#" => "学校多功能厅",
    "#obj#" => "小学教师"
];

echo "\n=== 测试数据2（带#号） ===\n";
foreach ($data2 as $key => $value) {
    echo "{$key}: '{$value}'\n";
}

$tplValue2 = buildTplValue($data2);
echo "\n构建的tpl_value:\n{$tplValue2}\n";

echo "\n=== 对比结果 ===\n";
echo "数据1结果: {$tplValue1}\n";
echo "数据2结果: {$tplValue2}\n";

if ($tplValue1 === $tplValue2) {
    echo "✅ 两种格式产生相同结果\n";
} else {
    echo "❌ 两种格式产生不同结果\n";
}

echo "\n=== 测试完成 ===\n";
