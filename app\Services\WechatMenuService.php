<?php

namespace App\Services;

use App\Models\WechatAccount;
use App\Models\WechatMenu;
use EasyWeChat\Factory;
use Illuminate\Support\Facades\Log;
use Exception;

class WechatMenuService
{
    /**
     * 创建微信菜单
     */
    public function createMenu(WechatAccount $account): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $menus = $this->getMenuStructure($account);
            
            if (empty($menus)) {
                return [
                    'success' => false,
                    'message' => '没有找到启用的菜单项'
                ];
            }

            $result = $app->menu->create($menus);
            
            if ($result['errcode'] === 0) {
                Log::info('微信菜单创建成功', [
                    'account_id' => $account->id,
                    'account_name' => $account->name,
                    'menus' => $menus
                ]);
                
                return [
                    'success' => true,
                    'message' => '菜单创建成功',
                    'data' => $result
                ];
            } else {
                Log::error('微信菜单创建失败', [
                    'account_id' => $account->id,
                    'error' => $result
                ]);
                
                return [
                    'success' => false,
                    'message' => '菜单创建失败：' . $result['errmsg'],
                    'error' => $result
                ];
            }
        } catch (Exception $e) {
            Log::error('微信菜单创建异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '菜单创建异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 删除微信菜单
     */
    public function deleteMenu(WechatAccount $account): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $result = $app->menu->delete();
            
            if ($result['errcode'] === 0) {
                Log::info('微信菜单删除成功', [
                    'account_id' => $account->id,
                    'account_name' => $account->name
                ]);
                
                return [
                    'success' => true,
                    'message' => '菜单删除成功',
                    'data' => $result
                ];
            } else {
                Log::error('微信菜单删除失败', [
                    'account_id' => $account->id,
                    'error' => $result
                ]);
                
                return [
                    'success' => false,
                    'message' => '菜单删除失败：' . $result['errmsg'],
                    'error' => $result
                ];
            }
        } catch (Exception $e) {
            Log::error('微信菜单删除异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '菜单删除异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取当前微信菜单
     */
    public function getCurrentMenu(WechatAccount $account): array
    {
        try {
            $app = Factory::officialAccount($account->getWechatConfig());
            $result = $app->menu->current();
            
            if (isset($result['menu'])) {
                return [
                    'success' => true,
                    'message' => '获取菜单成功',
                    'data' => $result['menu']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '暂无菜单数据',
                    'data' => null
                ];
            }
        } catch (Exception $e) {
            Log::error('获取微信菜单异常', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '获取菜单异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 同步本地菜单到微信
     */
    public function syncMenuToWechat(WechatAccount $account): array
    {
        // 先删除现有菜单
        $deleteResult = $this->deleteMenu($account);
        if (!$deleteResult['success']) {
            return $deleteResult;
        }

        // 等待一秒确保删除生效
        sleep(1);

        // 创建新菜单
        return $this->createMenu($account);
    }

    /**
     * 获取菜单结构
     */
    private function getMenuStructure(WechatAccount $account): array
    {
        $topMenus = WechatMenu::where('wechat_account_id', $account->id)
            ->active()
            ->topLevel()
            ->orderBy('sort_order')
            ->with(['children' => function ($query) {
                $query->active()->orderBy('sort_order');
            }])
            ->get();

        $menus = [];
        foreach ($topMenus as $menu) {
            $menuData = $menu->toWechatFormat();
            if (!empty($menuData)) {
                $menus[] = $menuData;
            }
        }

        return ['button' => $menus];
    }

    /**
     * 验证菜单结构
     */
    public function validateMenuStructure(WechatAccount $account): array
    {
        $errors = [];
        
        $topMenus = WechatMenu::where('wechat_account_id', $account->id)
            ->active()
            ->topLevel()
            ->with(['children' => function ($query) {
                $query->active();
            }])
            ->get();

        // 检查顶级菜单数量
        if ($topMenus->count() > 3) {
            $errors[] = '顶级菜单不能超过3个';
        }

        foreach ($topMenus as $menu) {
            // 检查菜单名称长度
            if (mb_strlen($menu->name) > 5) {
                $errors[] = "菜单 '{$menu->name}' 名称不能超过5个字符";
            }

            // 检查子菜单数量
            if ($menu->children->count() > 5) {
                $errors[] = "菜单 '{$menu->name}' 的子菜单不能超过5个";
            }

            // 检查子菜单名称长度
            foreach ($menu->children as $child) {
                if (mb_strlen($child->name) > 8) {
                    $errors[] = "子菜单 '{$child->name}' 名称不能超过8个字符";
                }

                // 检查必填字段
                if ($child->type === WechatMenu::TYPE_CLICK && empty($child->key)) {
                    $errors[] = "子菜单 '{$child->name}' 缺少key参数";
                }
                if ($child->type === WechatMenu::TYPE_VIEW && empty($child->url)) {
                    $errors[] = "子菜单 '{$child->name}' 缺少url参数";
                }
            }

            // 如果有子菜单，父菜单不能设置动作
            if ($menu->children->count() > 0) {
                if (!empty($menu->type)) {
                    $errors[] = "菜单 '{$menu->name}' 有子菜单时不能设置动作类型";
                }
            } else {
                // 没有子菜单时必须设置动作
                if (empty($menu->type)) {
                    $errors[] = "菜单 '{$menu->name}' 没有子菜单时必须设置动作类型";
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 批量导入菜单
     */
    public function importMenus(WechatAccount $account, array $menuData): array
    {
        try {
            // 清空现有菜单
            WechatMenu::where('wechat_account_id', $account->id)->delete();

            $sortOrder = 1;
            foreach ($menuData as $topMenu) {
                $menu = WechatMenu::create([
                    'wechat_account_id' => $account->id,
                    'name' => $topMenu['name'],
                    'type' => $topMenu['type'] ?? null,
                    'key' => $topMenu['key'] ?? null,
                    'url' => $topMenu['url'] ?? null,
                    'sort_order' => $sortOrder++,
                    'is_active' => true,
                ]);

                if (isset($topMenu['sub_button']) && is_array($topMenu['sub_button'])) {
                    $subSortOrder = 1;
                    foreach ($topMenu['sub_button'] as $subMenu) {
                        WechatMenu::create([
                            'wechat_account_id' => $account->id,
                            'parent_id' => $menu->id,
                            'name' => $subMenu['name'],
                            'type' => $subMenu['type'] ?? null,
                            'key' => $subMenu['key'] ?? null,
                            'url' => $subMenu['url'] ?? null,
                            'media_id' => $subMenu['media_id'] ?? null,
                            'appid' => $subMenu['appid'] ?? null,
                            'pagepath' => $subMenu['pagepath'] ?? null,
                            'sort_order' => $subSortOrder++,
                            'is_active' => true,
                        ]);
                    }
                }
            }

            return [
                'success' => true,
                'message' => '菜单导入成功'
            ];
        } catch (Exception $e) {
            Log::error('菜单导入失败', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '菜单导入失败：' . $e->getMessage()
            ];
        }
    }
}