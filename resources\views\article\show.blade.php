@extends('layouts.app')

@section('title', $article->title)

@section('content')
<div class="container mx-auto px-4 py-8">
    <article class="max-w-4xl mx-auto">
        <!-- 文章头部 -->
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ $article->title }}</h1>
            
            <div class="flex items-center text-gray-600 text-sm mb-4">
                <span>发布时间：{{ $article->published_at?->format('Y-m-d H:i') ?? $article->created_at->format('Y-m-d H:i') }}</span>
                @if($article->author)
                    <span class="mx-2">•</span>
                    <span>作者：{{ $article->author->name }}</span>
                @endif
                <span class="mx-2">•</span>
                <span>阅读量：{{ $article->views ?? 0 }}</span>
            </div>
            
            @if($article->excerpt)
                <div class="text-lg text-gray-700 leading-relaxed">
                    {{ $article->excerpt }}
                </div>
            @endif
        </header>
        
        <!-- 文章内容 -->
        <div class="prose prose-lg max-w-none">
            {!! \App\Helpers\TiptapHelper::render($article->content) !!}
        </div>
        
        <!-- 文章标签 -->
        @if($article->tags && $article->tags->count() > 0)
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-900 mb-2">标签：</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach($article->tags as $tag)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ $tag->name }}
                        </span>
                    @endforeach
                </div>
            </div>
        @endif
        
        <!-- 分享按钮 -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <h3 class="text-sm font-medium text-gray-900 mb-4">分享文章：</h3>
            <div class="flex space-x-4">
                <button onclick="shareToWeChat()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.5 12.5c0 .5-.4.9-.9.9s-.9-.4-.9-.9.4-.9.9-.9.9.4.9.9zm7.4 0c0 .5-.4.9-.9.9s-.9-.4-.9-.9.4-.9.9-.9.9.4.9.9z"/>
                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8z"/>
                    </svg>
                    微信分享
                </button>
                
                <button onclick="copyLink()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    复制链接
                </button>
            </div>
        </div>
    </article>
</div>

<!-- 视频相关的CSS样式 -->
<style>
.prose video {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin: 1.5rem 0;
}

.prose .video-wrapper {
    margin: 1.5rem 0;
    text-align: center;
}

.prose video:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 响应式视频 */
@media (max-width: 768px) {
    .prose video {
        margin: 1rem 0;
    }
}
</style>

<!-- JavaScript for sharing functionality -->
<script>
function shareToWeChat() {
    // 这里可以集成微信分享SDK
    alert('微信分享功能需要配置微信SDK');
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        alert('链接已复制到剪贴板');
    }, function(err) {
        console.error('复制失败: ', err);
        alert('复制失败，请手动复制链接');
    });
}

// 视频播放统计（可选）
document.addEventListener('DOMContentLoaded', function() {
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
        video.addEventListener('play', function() {
            // 这里可以发送视频播放统计
            console.log('视频开始播放:', video.src);
        });
        
        video.addEventListener('ended', function() {
            // 这里可以发送视频播放完成统计
            console.log('视频播放完成:', video.src);
        });
    });
});
</script>
@endsection