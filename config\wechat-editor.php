<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 微信编辑器配置
    |--------------------------------------------------------------------------
    |
    | 这里配置微信公众号文章编辑器的相关设置，
    | 包括工具栏、样式、上传等功能配置
    |
    */

    /*
    |--------------------------------------------------------------------------
    | CKEditor 工具栏配置
    |--------------------------------------------------------------------------
    |
    | 配置类似微信公众号原生编辑器的工具栏
    |
    */
    'ckeditor_toolbar' => [
        'heading', '|',
        'bold', 'italic', 'underline', 'strikethrough', '|',
        'fontSize', 'fontColor', 'fontBackgroundColor', '|',
        'alignment', '|',
        'bulletedList', 'numberedList', 'todoList', '|',
        'outdent', 'indent', '|',
        'blockQuote', 'insertTable', '|',
        'link', 'imageUpload', 'mediaEmbed', '|',
        'code', 'codeBlock', '|',
        'horizontalLine', '|',
        'undo', 'redo', '|',
        'sourceEditing', 'fullScreen'
    ],

    /*
    |--------------------------------------------------------------------------
    | 图片上传配置
    |--------------------------------------------------------------------------
    |
    | 配置图片上传的相关参数
    |
    */
    'image_upload' => [
        'max_size' => 5120, // 5MB
        'allowed_types' => ['image/jpeg', 'image/png', 'image/webp'],
        'directory' => 'wechat/content/images',
        'resize_width' => 800,
        'resize_height' => null, // 保持比例
    ],

    /*
    |--------------------------------------------------------------------------
    | 视频上传配置
    |--------------------------------------------------------------------------
    |
    | 配置视频上传的相关参数
    |
    */
    'video_upload' => [
        'max_size' => 51200, // 50MB
        'allowed_types' => ['video/mp4', 'video/webm'],
        'directory' => 'wechat/content/videos',
    ],

    /*
    |--------------------------------------------------------------------------
    | 封面图片配置
    |--------------------------------------------------------------------------
    |
    | 配置封面图片的相关参数
    |
    */
    'cover_image' => [
        'max_size' => 5120, // 5MB
        'allowed_types' => ['image/jpeg', 'image/png', 'image/webp'],
        'directory' => 'wechat/thumbs',
        'resize_width' => 800,
        'resize_height' => 450,
        'aspect_ratio' => '16:9',
    ],

    /*
    |--------------------------------------------------------------------------
    | 编辑器样式配置
    |--------------------------------------------------------------------------
    |
    | 配置编辑器的样式，使其更接近微信公众号
    |
    */
    'editor_styles' => [
        'font_family' => '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        'font_size' => '16px',
        'line_height' => '1.6',
        'color' => '#333333',
        'background_color' => '#ffffff',
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信公众号特殊功能
    |--------------------------------------------------------------------------
    |
    | 配置微信公众号特有的功能
    |
    */
    'wechat_features' => [
        'enable_vote' => false, // 投票功能（需要额外开发）
        'enable_audio' => false, // 音频功能（需要额外开发）
        'enable_miniprogram' => false, // 小程序卡片（需要额外开发）
        'enable_template' => true, // 模板功能
    ],
];