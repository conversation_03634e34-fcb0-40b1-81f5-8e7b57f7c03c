<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\DatabaseBackup;
use App\Models\Setting;

class DingTalkService
{
    /**
     * 发送钉钉消息
     */
    public function sendMessage(string $title, string $content, array $extra = []): bool
    {
        try {
            $webhook = Setting::getValue('dingtalk_webhook', '');
            
            if (empty($webhook)) {
                Log::warning('钉钉Webhook未配置');
                return false;
            }

            $message = [
                'msgtype' => 'markdown',
                'markdown' => [
                    'title' => $title,
                    'text' => $content
                ]
            ];

            $response = Http::timeout(10)->post($webhook, $message);

            if ($response->successful()) {
                $result = $response->json();
                if ($result['errcode'] === 0) {
                    Log::info('钉钉消息发送成功', ['title' => $title]);
                    return true;
                } else {
                    Log::error('钉钉消息发送失败', [
                        'title' => $title,
                        'errcode' => $result['errcode'],
                        'errmsg' => $result['errmsg'] ?? '未知错误'
                    ]);
                    return false;
                }
            } else {
                Log::error('钉钉API请求失败', [
                    'title' => $title,
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('钉钉消息发送异常', [
                'title' => $title,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 发送备份完成通知
     */
    public function sendBackupCompletedNotification(DatabaseBackup $backup): bool
    {
        $title = '✅ 数据备份完成通知';
        
        $content = "### {$title}\n\n";
        $content .= "**备份名称：** {$backup->name}\n\n";
        $content .= "**备份类型：** " . DatabaseBackup::getBackupTypes()[$backup->backup_type] . "\n\n";
        $content .= "**文件大小：** {$backup->formatted_file_size}\n\n";
        $content .= "**备份时间：** {$backup->backup_completed_at->format('Y-m-d H:i:s')}\n\n";
        $content .= "**耗时：** {$backup->duration}\n\n";
        
        if ($backup->schedule) {
            $content .= "**执行方式：** 计划任务 ({$backup->schedule->name})\n\n";
        } else {
            $content .= "**执行方式：** 手动备份\n\n";
        }
        
        $content .= "**状态：** <font color=\"#52c41a\">备份成功</font>\n\n";
        $content .= "---\n\n";
        $content .= "💡 **提示：** 请及时下载并保存备份文件到安全位置";

        return $this->sendMessage($title, $content);
    }

    /**
     * 发送备份失败通知
     */
    public function sendBackupFailedNotification(DatabaseBackup $backup): bool
    {
        $title = '❌ 数据备份失败通知';
        
        $content = "### {$title}\n\n";
        $content .= "**备份名称：** {$backup->name}\n\n";
        $content .= "**备份类型：** " . DatabaseBackup::getBackupTypes()[$backup->backup_type] . "\n\n";
        $content .= "**失败时间：** {$backup->backup_completed_at->format('Y-m-d H:i:s')}\n\n";
        
        if ($backup->schedule) {
            $content .= "**执行方式：** 计划任务 ({$backup->schedule->name})\n\n";
        } else {
            $content .= "**执行方式：** 手动备份\n\n";
        }
        
        $content .= "**状态：** <font color=\"#ff4d4f\">备份失败</font>\n\n";
        $content .= "**错误信息：** \n```\n{$backup->error_message}\n```\n\n";
        $content .= "---\n\n";
        $content .= "⚠️ **请立即检查：**\n";
        $content .= "- 数据库连接是否正常\n";
        $content .= "- 磁盘空间是否充足\n";
        $content .= "- 备份权限是否正确\n";
        $content .= "- 系统日志中的详细错误信息";

        return $this->sendMessage($title, $content);
    }

    /**
     * 发送计划任务状态通知
     */
    public function sendScheduleStatusNotification(string $scheduleName, string $status, string $message = ''): bool
    {
        $title = $status === 'success' ? '✅ 备份计划执行成功' : '❌ 备份计划执行失败';
        
        $content = "### {$title}\n\n";
        $content .= "**计划名称：** {$scheduleName}\n\n";
        $content .= "**执行时间：** " . now()->format('Y-m-d H:i:s') . "\n\n";
        $content .= "**状态：** " . ($status === 'success' ? 
            '<font color="#52c41a">执行成功</font>' : 
            '<font color="#ff4d4f">执行失败</font>') . "\n\n";
        
        if ($message) {
            $content .= "**详细信息：** {$message}\n\n";
        }
        
        $content .= "---\n\n";
        $content .= "📊 **系统信息：** CMS管理系统自动备份";

        return $this->sendMessage($title, $content);
    }

    /**
     * 测试钉钉连接
     */
    public function testConnection(): array
    {
        try {
            $webhook = Setting::getValue('dingtalk_webhook', '');
            
            if (empty($webhook)) {
                return [
                    'success' => false,
                    'message' => '钉钉Webhook未配置'
                ];
            }

            $title = '🔔 钉钉通知测试';
            $content = "### {$title}\n\n";
            $content .= "**测试时间：** " . now()->format('Y-m-d H:i:s') . "\n\n";
            $content .= "**测试内容：** 这是一条来自CMS管理系统的测试消息\n\n";
            $content .= "---\n\n";
            $content .= "✅ 如果您收到此消息，说明钉钉通知配置正确！";

            $success = $this->sendMessage($title, $content);

            return [
                'success' => $success,
                'message' => $success ? '测试消息发送成功！' : '测试消息发送失败，请检查配置'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '测试失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证Webhook格式
     */
    public static function validateWebhook(string $webhook): bool
    {
        // 钉钉机器人Webhook格式验证
        $pattern = '/^https:\/\/oapi\.dingtalk\.com\/robot\/send\?access_token=[a-zA-Z0-9]+$/';
        return preg_match($pattern, $webhook) === 1;
    }

    /**
     * 获取Webhook配置说明
     */
    public static function getWebhookInstructions(): string
    {
        return "钉钉机器人Webhook配置说明：\n\n" .
               "1. 在钉钉群中添加自定义机器人\n" .
               "2. 选择安全设置（建议选择IP地址或关键词）\n" .
               "3. 复制生成的Webhook地址\n" .
               "4. 格式示例：https://oapi.dingtalk.com/robot/send?access_token=xxxxxx\n\n" .
               "注意：请确保服务器IP在钉钉机器人的白名单中";
    }
}
