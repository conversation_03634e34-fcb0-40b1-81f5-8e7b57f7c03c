<?php

namespace App\Observers;

use App\Models\Article;

class ArticleObserver
{
    public function updated(Article $article): void
    {
        // 检查是否需要创建新版本
        if ($this->shouldCreateVersion($article)) {
            $article->createVersion(
                'v' . (substr($article->getOriginal('version') ?? 'v1.0', 1) + 0.1),
                '自动保存'
            );
        }
    }

    protected function shouldCreateVersion(Article $article): bool
    {
        // 检查关键字段是否发生变化
        $dirtyFields = $article->getDirty();
        $importantFields = ['title', 'content', 'summary', 'cover_image', 'meta_data'];

        foreach ($importantFields as $field) {
            if (isset($dirtyFields[$field])) {
                return true;
            }
        }

        return false;
    }

    public function saving(Article $article)
    {
        $options = $article->extra_options ?? [];
        $content = $article->content;

        // 下载远程图片
        if (in_array('download_remote', $options)) {
            $content = preg_replace_callback('/<img.*?src=[\'\"](http.*?)[\'\"]/i', function ($matches) {
                $url = $matches[1];
                $ext = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
                $filename = 'articles/' . uniqid() . '.' . $ext;
                try {
                    $img = @file_get_contents($url);
                    if ($img) {
                        \Illuminate\Support\Facades\Storage::disk('public')->put($filename, $img);
                        $localUrl = \Illuminate\Support\Facades\Storage::url($filename);
                        return str_replace($url, $localUrl, $matches[0]);
                    }
                } catch (\Exception $e) {
                    // 忽略异常
                }
                return $matches[0];
            }, $content);
        }

        // 提取首图为缩略图
        if (in_array('first_image_thumb', $options)) {
            if (preg_match('/<img.*?src=[\'\"](.*?)[\'\"]/i', $content, $m)) {
                $article->thumb_image = $m[1];
            }
        }

        $article->content = $content;
    }
} 