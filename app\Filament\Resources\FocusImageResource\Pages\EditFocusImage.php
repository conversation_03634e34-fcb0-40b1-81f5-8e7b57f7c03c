<?php

namespace App\Filament\Resources\FocusImageResource\Pages;

use App\Filament\Resources\FocusImageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFocusImage extends EditRecord
{
    protected static string $resource = FocusImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}