<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->string('short_title')->nullable()->comment('简略标题');
            $table->string('custom_flags', 32)->nullable()->comment('自定义属性标记');
            $table->integer('weight')->default(0)->comment('权重');
            $table->string('thumb_image')->nullable()->comment('缩略图');
            $table->string('source')->nullable()->comment('文章来源');
            $table->string('author')->nullable()->comment('作者');
            $table->boolean('allow_comment')->default(true)->comment('允许评论');
            $table->string('view_permission', 32)->default('public')->comment('阅读权限');
            $table->string('title_color', 16)->nullable()->comment('标题颜色');
            $table->json('extra_options')->nullable()->comment('附加选项');
        });

        Schema::create('post_sub_category', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('post_id');
            $table->unsignedBigInteger('category_id');
            $table->timestamps();

            $table->unique(['post_id', 'category_id']);
            $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropColumn([
                'short_title', 'custom_flags', 'weight', 'thumb_image', 'source',
                'author', 'allow_comment', 'view_permission', 'title_color', 'extra_options'
            ]);
        });
        Schema::dropIfExists('post_sub_category');
    }
}; 