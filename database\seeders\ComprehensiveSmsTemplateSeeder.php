<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SmsTemplate;

class ComprehensiveSmsTemplateSeeder extends Seeder
{
    public function run()
    {
        $templates = [
            [
                'name' => '嘉定幸福课程身份验证',
                'code' => 'jiading_verification',
                'yunpian_template_id' => '2875701',
                'description' => '【上海市嘉定区教育学院】验证码#code#，您正在进行嘉定幸福课程身份验证，感谢关注',
                'template_params' => json_encode([
                    'code' => '验证码'
                ]),
                'param_description' => '参数说明：#code#=验证码',
                'template_category' => 'verification'
            ],
            [
                'name' => '通用验证码',
                'code' => 'general_verification',
                'yunpian_template_id' => '2875702',
                'description' => '【上海市嘉定区教育学院】您的验证码是#code#。如非本人操作，请忽略本短信',
                'template_params' => json_encode([
                    'code' => '验证码'
                ]),
                'param_description' => '参数说明：#code#=验证码',
                'template_category' => 'verification'
            ],
            [
                'name' => '现场咨询通知',
                'code' => 'consultation_notice',
                'yunpian_template_id' => '2875703',
                'description' => '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。',
                'template_params' => json_encode([
                    'topic' => '咨询主题',
                    'time' => '咨询时间',
                    'address' => '咨询地点',
                    'obj' => '咨询对象'
                ]),
                'param_description' => '参数说明：#topic#=咨询主题，#time#=咨询时间，#address#=咨询地点，#obj#=咨询对象',
                'template_category' => 'consultation'
            ],
            [
                'name' => '账号审核通过',
                'code' => 'account_approved',
                'yunpian_template_id' => '2875704',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已通过审核，感谢关注',
                'template_params' => json_encode([
                    'name' => '用户姓名'
                ]),
                'param_description' => '参数说明：#name#=用户姓名',
                'template_category' => 'account'
            ],
            [
                'name' => '账号审核未通过',
                'code' => 'account_rejected',
                'yunpian_template_id' => '2875705',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号未能通过审核，可能的原因是您不是本课程的适用对象，感谢关注！',
                'template_params' => json_encode([
                    'name' => '用户姓名'
                ]),
                'param_description' => '参数说明：#name#=用户姓名',
                'template_category' => 'account'
            ],
            [
                'name' => '账号开通通知',
                'code' => 'account_activated',
                'yunpian_template_id' => '2875706',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已开通，请用微信关注公众号嘉定区幸福课程，并用手机号登录，密码为#pwd#',
                'template_params' => json_encode([
                    'name' => '用户姓名',
                    'pwd' => '登录密码'
                ]),
                'param_description' => '参数说明：#name#=用户姓名，#pwd#=登录密码',
                'template_category' => 'account'
            ],
            [
                'name' => '心灵嘉园身份验证',
                'code' => 'xinling_verification',
                'yunpian_template_id' => '2875707',
                'description' => '【上海市嘉定区教育学院】验证码#code#，您正在进行心灵嘉园身份验证，感谢关注',
                'template_params' => json_encode([
                    'code' => '验证码'
                ]),
                'param_description' => '参数说明：#code#=验证码',
                'template_category' => 'verification'
            ],
            [
                'name' => '暑期大培训报名成功',
                'code' => 'summer_training_success',
                'yunpian_template_id' => '2875708',
                'description' => '【上海市嘉定区教育学院】您好，您报的暑期大培训课程"#name#"已报名成功，请关注开课时间，准时参加！',
                'template_params' => json_encode([
                    'name' => '课程名称'
                ]),
                'param_description' => '参数说明：#name#=课程名称',
                'template_category' => 'registration'
            ],
            [
                'name' => '活动报名确认',
                'code' => 'activity_registration_confirm',
                'yunpian_template_id' => '2875709',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您已成功报名参加"#topic#"活动，时间：#time#，地点：#address#，请准时参加！',
                'template_params' => json_encode([
                    'name' => '参与者姓名',
                    'topic' => '活动主题',
                    'time' => '活动时间',
                    'address' => '活动地点'
                ]),
                'param_description' => '参数说明：#name#=参与者姓名，#topic#=活动主题，#time#=活动时间，#address#=活动地点',
                'template_category' => 'registration'
            ]
        ];

        foreach ($templates as $template) {
            SmsTemplate::updateOrCreate(
                ['code' => $template['code']],
                $template
            );
        }
    }
}