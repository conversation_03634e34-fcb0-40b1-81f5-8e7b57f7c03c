<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Models\Activity;
use Illuminate\Support\Facades\DB;

echo "=== 检查空字段问题 ===\n";

try {
    // 获取测试注册记录
    $registration = Registration::with(['activityDetail.activity'])->first();
    
    if (!$registration) {
        echo "没有找到测试注册记录\n";
        exit(1);
    }
    
    $activityDetail = $registration->activityDetail;
    $activity = $activityDetail->activity;
    
    echo "注册记录ID: {$registration->id}\n";
    echo "活动详情ID: {$activityDetail->id}\n";
    echo "活动ID: {$activity->id}\n";
    
    echo "\n=== 原始数据检查 ===\n";
    echo "activity_details.activity_time: '" . ($activityDetail->activity_time ?? 'NULL') . "'\n";
    echo "activity_details.target: '" . ($activityDetail->target ?? 'NULL') . "'\n";
    echo "activity_details.target_audience: '" . ($activityDetail->target_audience ?? 'NULL') . "'\n";
    echo "activity_details.topic: '" . ($activityDetail->topic ?? 'NULL') . "'\n";
    echo "activity_details.theme: '" . ($activityDetail->theme ?? 'NULL') . "'\n";
    echo "activity_details.address: '" . ($activityDetail->address ?? 'NULL') . "'\n";
    echo "activities.title: '" . ($activity->title ?? 'NULL') . "'\n";
    
    // 检查数据库中的原始值
    echo "\n=== 数据库原始查询 ===\n";
    $rawActivityDetail = DB::table('activity_details')->where('id', $activityDetail->id)->first();
    if ($rawActivityDetail) {
        echo "DB activity_time: '" . ($rawActivityDetail->activity_time ?? 'NULL') . "'\n";
        echo "DB target: '" . ($rawActivityDetail->target ?? 'NULL') . "'\n";
        echo "DB target_audience: '" . ($rawActivityDetail->target_audience ?? 'NULL') . "'\n";
        echo "DB topic: '" . ($rawActivityDetail->topic ?? 'NULL') . "'\n";
        echo "DB theme: '" . ($rawActivityDetail->theme ?? 'NULL') . "'\n";
        echo "DB address: '" . ($rawActivityDetail->address ?? 'NULL') . "'\n";
    }
    
    // 查找有完整数据的记录
    echo "\n=== 查找有完整数据的记录 ===\n";
    $completeRecords = DB::table('activity_details')
        ->whereNotNull('activity_time')
        ->where('activity_time', '!=', '')
        ->whereNotNull('target_audience')
        ->where('target_audience', '!=', '')
        ->limit(3)
        ->get();
    
    if ($completeRecords->count() > 0) {
        echo "找到 {$completeRecords->count()} 条有完整数据的记录:\n";
        foreach ($completeRecords as $record) {
            echo "ID: {$record->id}, activity_time: '{$record->activity_time}', target_audience: '{$record->target_audience}'\n";
        }
    } else {
        echo "没有找到有完整数据的记录\n";
    }
    
    // 统计空字段情况
    echo "\n=== 空字段统计 ===\n";
    $totalRecords = DB::table('activity_details')->count();
    $emptyTime = DB::table('activity_details')->whereNull('activity_time')->orWhere('activity_time', '')->count();
    $emptyTarget = DB::table('activity_details')->whereNull('target_audience')->orWhere('target_audience', '')->count();
    
    echo "总记录数: {$totalRecords}\n";
    echo "activity_time为空的记录: {$emptyTime}\n";
    echo "target_audience为空的记录: {$emptyTarget}\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
