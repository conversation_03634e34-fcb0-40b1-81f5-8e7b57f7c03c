<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SmsTemplate;
use Illuminate\Support\Facades\DB;

echo "=== 修复SMS模板内容 ===\n";

try {
    // 查看所有SMS模板
    $templates = SmsTemplate::all();
    
    echo "当前SMS模板列表:\n";
    foreach ($templates as $template) {
        echo "ID: {$template->id}, 名称: '{$template->name}', 模板代码: '{$template->template_code}', 内容长度: " . strlen($template->content ?? '') . "\n";
        if (empty($template->content)) {
            echo "  ⚠ 内容为空！\n";
        } else {
            echo "  内容: '{$template->content}'\n";
        }
    }
    
    // 查找"现场咨询通知"模板
    $consultationTemplate = SmsTemplate::where('name', '现场咨询通知')->first();
    
    if ($consultationTemplate) {
        echo "\n找到现场咨询通知模板 (ID: {$consultationTemplate->id})\n";
        
        if (empty($consultationTemplate->content)) {
            echo "模板内容为空，需要修复\n";
            
            // 根据用户反馈的SMS格式设置正确的模板内容
            $correctContent = "【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。";
            
            $consultationTemplate->content = $correctContent;
            $consultationTemplate->save();
            
            echo "✓ 已修复模板内容为: '{$correctContent}'\n";
        } else {
            echo "模板内容: '{$consultationTemplate->content}'\n";
        }
    } else {
        echo "\n未找到现场咨询通知模板，创建新模板\n";
        
        $newTemplate = new SmsTemplate();
        $newTemplate->name = '现场咨询通知';
        $newTemplate->template_code = 'consultation_notice';
        $newTemplate->content = "【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。";
        $newTemplate->template_params = json_encode(['name', 'topic', 'time', 'address', 'obj']);
        $newTemplate->save();
        
        echo "✓ 已创建新的现场咨询通知模板 (ID: {$newTemplate->id})\n";
    }
    
    // 检查其他可能为空的模板
    $emptyTemplates = SmsTemplate::where(function($query) {
        $query->whereNull('content')->orWhere('content', '');
    })->get();
    
    if ($emptyTemplates->count() > 0) {
        echo "\n发现其他空内容模板:\n";
        foreach ($emptyTemplates as $template) {
            echo "- ID: {$template->id}, 名称: '{$template->name}'\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 修复完成 ===\n";
