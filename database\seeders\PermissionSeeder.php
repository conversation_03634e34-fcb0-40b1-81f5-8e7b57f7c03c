<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        $permissions = [
            // 系统管理权限
            ['name' => 'system.view', 'guard_name' => 'web'],
            ['name' => 'system.manage', 'guard_name' => 'web'],
            ['name' => 'system.audit', 'guard_name' => 'web'],
            
            // 用户管理权限
            ['name' => 'users.view', 'guard_name' => 'web'],
            ['name' => 'users.create', 'guard_name' => 'web'],
            ['name' => 'users.edit', 'guard_name' => 'web'],
            ['name' => 'users.delete', 'guard_name' => 'web'],
            
            // 角色管理权限
            ['name' => 'roles.view', 'guard_name' => 'web'],
            ['name' => 'roles.create', 'guard_name' => 'web'],
            ['name' => 'roles.edit', 'guard_name' => 'web'],
            ['name' => 'roles.delete', 'guard_name' => 'web'],
            
            // 内容管理权限
            ['name' => 'posts.view', 'guard_name' => 'web'],
            ['name' => 'posts.create', 'guard_name' => 'web'],
            ['name' => 'posts.edit', 'guard_name' => 'web'],
            ['name' => 'posts.delete', 'guard_name' => 'web'],
            
            // 分类管理权限
            ['name' => 'categories.view', 'guard_name' => 'web'],
            ['name' => 'categories.create', 'guard_name' => 'web'],
            ['name' => 'categories.edit', 'guard_name' => 'web'],
            ['name' => 'categories.delete', 'guard_name' => 'web'],
            
            // 媒体管理权限
            ['name' => 'media.view', 'guard_name' => 'web'],
            ['name' => 'media.upload', 'guard_name' => 'web'],
            ['name' => 'media.edit', 'guard_name' => 'web'],
            ['name' => 'media.delete', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }
    }
} 