<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

echo "=== 综合修复短信参数空值问题 ===\n\n";

// 开始事务
DB::beginTransaction();

try {
    // 1. 修复activities表中的空title
    echo "1. 检查并修复activities表中的空title...\n";
    $emptyTitleActivities = DB::table('activities')
        ->where('title', '')
        ->orWhereNull('title')
        ->get();
    
    $fixedActivities = 0;
    foreach ($emptyTitleActivities as $activity) {
        // 尝试从activity_details获取theme作为title
        $activityDetail = DB::table('activity_details')
            ->where('activity_id', $activity->id)
            ->first();
        
        $newTitle = '默认活动标题';
        if ($activityDetail && !empty($activityDetail->theme)) {
            $newTitle = $activityDetail->theme;
        } else {
            $newTitle = "活动 #{$activity->id}";
        }
        
        DB::table('activities')
            ->where('id', $activity->id)
            ->update(['title' => $newTitle]);
        
        echo "  - 活动ID {$activity->id}: 设置标题为 '{$newTitle}'\n";
        $fixedActivities++;
    }
    echo "  ✅ 修复了 {$fixedActivities} 个空标题活动\n\n";
    
    // 2. 修复activity_details表中的空值
    echo "2. 检查并修复activity_details表中的空值...\n";
    $emptyDetailsCount = 0;
    
    // 修复空的theme
    $emptyThemeDetails = DB::table('activity_details')
        ->where('theme', '')
        ->orWhereNull('theme')
        ->get();
    
    foreach ($emptyThemeDetails as $detail) {
        $activity = DB::table('activities')->where('id', $detail->activity_id)->first();
        $newTheme = $activity && !empty($activity->title) ? $activity->title : "活动主题 #{$detail->id}";
        
        DB::table('activity_details')
            ->where('id', $detail->id)
            ->update(['theme' => $newTheme]);
        
        echo "  - 活动详情ID {$detail->id}: 设置主题为 '{$newTheme}'\n";
        $emptyDetailsCount++;
    }
    
    // 修复空的target
    $emptyTargetDetails = DB::table('activity_details')
        ->where('target', '')
        ->orWhereNull('target')
        ->get();
    
    foreach ($emptyTargetDetails as $detail) {
        DB::table('activity_details')
            ->where('id', $detail->id)
            ->update(['target' => '全体人员']);
        
        echo "  - 活动详情ID {$detail->id}: 设置目标对象为 '全体人员'\n";
        $emptyDetailsCount++;
    }
    
    // 修复空的address
    $emptyAddressDetails = DB::table('activity_details')
        ->where('address', '')
        ->orWhereNull('address')
        ->get();
    
    foreach ($emptyAddressDetails as $detail) {
        DB::table('activity_details')
            ->where('id', $detail->id)
            ->update(['address' => '待定地点']);
        
        echo "  - 活动详情ID {$detail->id}: 设置地点为 '待定地点'\n";
        $emptyDetailsCount++;
    }
    
    // 修复空的activity_time
    $emptyTimeDetails = DB::table('activity_details')
        ->whereNull('activity_time')
        ->get();
    
    foreach ($emptyTimeDetails as $detail) {
        $defaultTime = Carbon::now()->addDays(7)->format('Y-m-d H:i:s');
        DB::table('activity_details')
            ->where('id', $detail->id)
            ->update(['activity_time' => $defaultTime]);
        
        echo "  - 活动详情ID {$detail->id}: 设置活动时间为 '{$defaultTime}'\n";
        $emptyDetailsCount++;
    }
    
    echo "  ✅ 修复了 {$emptyDetailsCount} 个空值字段\n\n";
    
    // 3. 检查sms_templates表
    echo "3. 检查sms_templates表...\n";
    $consultationTemplate = DB::table('sms_templates')
        ->where('code', 'consultation_notice')
        ->first();
    
    if ($consultationTemplate) {
        echo "  ✅ 现场咨询模板存在 (ID: {$consultationTemplate->id})\n";
        echo "  - 云片模板ID: {$consultationTemplate->yunpian_template_id}\n";
        echo "  - 模板描述: {$consultationTemplate->description}\n";
    } else {
        echo "  ❌ 现场咨询模板不存在，需要创建\n";
        // 创建模板
        $templateId = DB::table('sms_templates')->insertGetId([
            'name' => '现场咨询通知',
            'code' => 'consultation_notice',
            'yunpian_template_id' => '6065626',
            'description' => '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。',
            'template_params' => json_encode([
                'topic' => '咨询主题',
                'time' => '咨询时间', 
                'address' => '咨询地点',
                'obj' => '咨询对象'
            ], JSON_UNESCAPED_UNICODE),
            'param_description' => 'topic:主题,time:时间,address:地点,obj:对象',
            'template_category' => 'consultation',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "  ✅ 创建了现场咨询模板 (ID: {$templateId})\n";
        $consultationTemplate = (object)['id' => $templateId];
    }
    echo "\n";
    
    // 4. 确保activity_details有正确的sms_template_id
    echo "4. 检查并设置activity_details的sms_template_id...\n";
    $detailsWithoutTemplate = DB::table('activity_details')
        ->whereNull('sms_template_id')
        ->orWhere('sms_template_id', 0)
        ->get();
    
    $updatedDetails = 0;
    foreach ($detailsWithoutTemplate as $detail) {
        DB::table('activity_details')
            ->where('id', $detail->id)
            ->update(['sms_template_id' => $consultationTemplate->id]);
        
        echo "  - 活动详情ID {$detail->id}: 设置短信模板ID为 {$consultationTemplate->id}\n";
        $updatedDetails++;
    }
    echo "  ✅ 更新了 {$updatedDetails} 个活动详情的短信模板ID\n\n";
    
    // 5. 测试修复后的数据
    echo "5. 测试修复后的数据...\n";
    $testRegistration = DB::table('registrations')
        ->join('activity_details', 'registrations.activity_detail_id', '=', 'activity_details.id')
        ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
        ->select(
            'registrations.id as registration_id',
            'registrations.name',
            'activities.title as activity_title',
            'activity_details.theme',
            'activity_details.activity_time',
            'activity_details.address',
            'activity_details.target',
            'activity_details.sms_template_id'
        )
        ->first();
    
    if ($testRegistration) {
        echo "  测试报名记录ID: {$testRegistration->registration_id}\n";
        echo "  构建的短信参数:\n";
        echo "  - name: '{$testRegistration->name}'\n";
        echo "  - topic: '{$testRegistration->activity_title}'\n";
        echo "  - time: '{$testRegistration->activity_time}'\n";
        echo "  - address: '{$testRegistration->address}'\n";
        echo "  - obj: '{$testRegistration->target}'\n";
        
        // 检查是否还有空值
        $emptyParams = [];
        if (empty($testRegistration->name)) $emptyParams[] = 'name';
        if (empty($testRegistration->activity_title)) $emptyParams[] = 'topic';
        if (empty($testRegistration->activity_time)) $emptyParams[] = 'time';
        if (empty($testRegistration->address)) $emptyParams[] = 'address';
        if (empty($testRegistration->target)) $emptyParams[] = 'obj';
        
        if (empty($emptyParams)) {
            echo "  ✅ 所有参数都有值，修复成功！\n";
        } else {
            echo "  ❌ 仍有空值参数: " . implode(', ', $emptyParams) . "\n";
        }
    } else {
        echo "  ❌ 没有找到测试数据\n";
    }
    
    // 6. 生成修复报告
    echo "\n6. 修复报告:\n";
    $totalActivities = DB::table('activities')->count();
    $totalActivityDetails = DB::table('activity_details')->count();
    $totalRegistrations = DB::table('registrations')->count();
    $totalSmsTemplates = DB::table('sms_templates')->count();
    
    echo "  数据库表统计:\n";
    echo "  - activities: {$totalActivities} 条记录\n";
    echo "  - activity_details: {$totalActivityDetails} 条记录\n";
    echo "  - registrations: {$totalRegistrations} 条记录\n";
    echo "  - sms_templates: {$totalSmsTemplates} 条记录\n";
    echo "\n";
    
    echo "  修复统计:\n";
    echo "  - 修复空标题活动: {$fixedActivities} 个\n";
    echo "  - 修复空值字段: {$emptyDetailsCount} 个\n";
    echo "  - 更新短信模板ID: {$updatedDetails} 个\n";
    
    // 提交事务
    DB::commit();
    echo "\n✅ 所有修复操作已成功完成并提交到数据库！\n";
    
} catch (Exception $e) {
    // 回滚事务
    DB::rollback();
    echo "\n❌ 修复过程中出现错误，已回滚所有更改:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 修复完成 ===\n";