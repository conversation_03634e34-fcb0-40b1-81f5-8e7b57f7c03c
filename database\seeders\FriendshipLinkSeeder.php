<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FriendshipLink;
use App\Models\FriendshipLinkCategory;

class FriendshipLinkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建友情链接分类
        $categories = [
            [
                'name' => '教育机构',
                'slug' => 'education',
                'description' => '教育相关的机构和网站',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => '政府部门',
                'slug' => 'government',
                'description' => '政府相关部门网站',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => '合作伙伴',
                'slug' => 'partners',
                'description' => '合作伙伴网站',
                'sort_order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            FriendshipLinkCategory::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // 创建友情链接
        $links = [
            [
                'name' => '教育部',
                'url' => 'http://www.moe.gov.cn/',
                'image' => 'friendship/moe.png',
                'description' => '中华人民共和国教育部官方网站',
                'title' => '教育部官网',
                'friendship_link_category_id' => FriendshipLinkCategory::where('slug', 'government')->first()->id,
                'rel' => 'nofollow',
                'sort_order' => 1,
                'is_active' => true,
                'open_new_window' => true,
            ],
            [
                'name' => '上海市教委',
                'url' => 'http://edu.sh.gov.cn/',
                'image' => 'friendship/sh-edu.png',
                'description' => '上海市教育委员会官方网站',
                'title' => '上海市教委',
                'friendship_link_category_id' => FriendshipLinkCategory::where('slug', 'government')->first()->id,
                'rel' => 'nofollow',
                'sort_order' => 2,
                'is_active' => true,
                'open_new_window' => true,
            ],
            [
                'name' => '中国教育在线',
                'url' => 'https://www.eol.cn/',
                'image' => 'friendship/eol.png',
                'description' => '中国教育在线官方网站',
                'title' => '中国教育在线',
                'friendship_link_category_id' => FriendshipLinkCategory::where('slug', 'education')->first()->id,
                'rel' => 'nofollow',
                'sort_order' => 3,
                'is_active' => true,
                'open_new_window' => true,
            ],
            [
                'name' => '学而思网校',
                'url' => 'https://www.xueersi.com/',
                'image' => 'friendship/xueersi.png',
                'description' => '学而思网校在线教育平台',
                'title' => '学而思网校',
                'friendship_link_category_id' => FriendshipLinkCategory::where('slug', 'partners')->first()->id,
                'rel' => 'nofollow',
                'sort_order' => 4,
                'is_active' => true,
                'open_new_window' => true,
            ],
            [
                'name' => '新东方在线',
                'url' => 'https://www.koolearn.com/',
                'image' => 'friendship/koolearn.png',
                'description' => '新东方在线教育平台',
                'title' => '新东方在线',
                'friendship_link_category_id' => FriendshipLinkCategory::where('slug', 'partners')->first()->id,
                'rel' => 'nofollow',
                'sort_order' => 5,
                'is_active' => true,
                'open_new_window' => true,
            ],
            [
                'name' => '腾讯课堂',
                'url' => 'https://ke.qq.com/',
                'image' => 'friendship/ke-qq.png',
                'description' => '腾讯课堂在线学习平台',
                'title' => '腾讯课堂',
                'friendship_link_category_id' => FriendshipLinkCategory::where('slug', 'education')->first()->id,
                'rel' => 'nofollow',
                'sort_order' => 6,
                'is_active' => true,
                'open_new_window' => true,
            ],
        ];

        foreach ($links as $linkData) {
            FriendshipLink::firstOrCreate(
                ['url' => $linkData['url']],
                $linkData
            );
        }

        $this->command->info('友情链接数据创建成功！');
        $this->command->info('- 创建了 3 个友情链接分类');
        $this->command->info('- 创建了 6 个友情链接');
    }
}
