@props(['src', 'controls' => true, 'width' => '100%', 'height' => 'auto', 'poster' => null])

<div class="video-wrapper" style="margin: 1rem 0;">
    <video 
        src="{{ $src }}"
        @if($controls) controls @endif
        @if($poster) poster="{{ $poster }}" @endif
        style="max-width: {{ $width }}; height: {{ $height }}; border-radius: 8px; display: block;"
        class="video-player"
    >
        <p>您的浏览器不支持视频播放。请 <a href="{{ $src }}">下载视频</a> 观看。</p>
    </video>
</div>

<style>
.video-wrapper {
    position: relative;
    max-width: 100%;
}

.video-player {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-player:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}
</style>