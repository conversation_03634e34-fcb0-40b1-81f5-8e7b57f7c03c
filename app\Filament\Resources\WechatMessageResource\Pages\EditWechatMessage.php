<?php

namespace App\Filament\Resources\WechatMessageResource\Pages;

use App\Filament\Resources\WechatMessageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditWechatMessage extends EditRecord
{
    protected static string $resource = WechatMessageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}