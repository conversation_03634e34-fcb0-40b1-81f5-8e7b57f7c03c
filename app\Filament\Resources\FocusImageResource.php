<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FocusImageResource\Pages;
use App\Models\FocusImage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class FocusImageResource extends Resource
{
    protected static ?string $model = FocusImage::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationGroup = 'Banner图管理';

    protected static ?string $navigationLabel = 'Banner图';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 7;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('标题')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('subtitle')
                            ->label('副标题')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('描述')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('图片设置')
                    ->schema([
                        Forms\Components\FileUpload::make('image')
                            ->label('焦点图片')
                            ->image()
                            ->required()
                            ->disk('public')
                            ->directory('focus-images')
                            ->visibility('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('alt_text')
                            ->label('图片替代文本')
                            ->maxLength(255)
                            ->helperText('用于SEO和无障碍访问'),
                    ]),
                Forms\Components\Section::make('链接设置')
                    ->schema([
                        Forms\Components\TextInput::make('link_url')
                            ->label('链接地址')
                            ->url()
                            ->maxLength(255)
                            ->helperText('点击图片时跳转的链接'),
                        Forms\Components\Select::make('link_target')
                            ->label('链接打开方式')
                            ->options([
                                '_self' => '当前窗口',
                                '_blank' => '新窗口',
                            ])
                            ->default('_self'),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('显示设置')
                    ->schema([
                        Forms\Components\Select::make('position')
                            ->label('显示位置')
                            ->options([
                                'home_banner' => '首页轮播',
                                'home_featured' => '首页推荐',
                                'sidebar' => '侧边栏', 
                                'footer' => '页脚',
                                'floating_window' => '首页飘窗(128*95)', // 新增
                            ])
                            ->required()
                            ->default('home_banner'),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('是否启用')
                            ->default(true),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('时间设置')
                    ->schema([
                        Forms\Components\DateTimePicker::make('start_time')
                            ->label('开始时间')
                            ->helperText('留空表示立即生效'),
                        Forms\Components\DateTimePicker::make('end_time')
                            ->label('结束时间')
                            ->helperText('留空表示永久有效'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('图片')
                    ->disk('public')
                    ->circular()
                    ->size(60),
                Tables\Columns\TextColumn::make('title')
                    ->label('标题')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('subtitle')
                    ->label('副标题')
                    ->searchable()
                    ->limit(30),
                Tables\Columns\BadgeColumn::make('position')
                    ->label('显示位置')
                    ->colors([
                        'primary' => 'home_banner',
                        'success' => 'home_featured',
                        'warning' => 'sidebar',
                        'danger' => 'footer',
                        'info' => 'floating_window',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'home_banner' => '首页轮播',
                        'home_featured' => '首页推荐',
                        'sidebar' => '侧边栏',
                        'footer' => '页脚',
                        'floating_window' => '首页飘窗',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('link_url')
                    ->label('链接地址')
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('开始时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('结束时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('position')
                    ->label('显示位置')
                    ->options([
                        'home_banner' => '首页轮播',
                        'home_featured' => '首页推荐',
                        'sidebar' => '侧边栏',
                        'footer' => '页脚',
                        'floating_window' => '首页飘窗',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('启用')
                    ->falseLabel('禁用')
                    ->native(false),
                Tables\Filters\Filter::make('active_period')
                    ->label('有效期内')
                    ->query(fn ($query) => $query->where(function ($q) {
                        $now = now();
                        $q->where(function ($subQ) use ($now) {
                            $subQ->whereNull('start_time')
                                ->orWhere('start_time', '<=', $now);
                        })
                        ->where(function ($subQ) use ($now) {
                            $subQ->whereNull('end_time')
                                ->orWhere('end_time', '>=', $now);
                        });
                    })),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFocusImages::route('/'),
            'create' => Pages\CreateFocusImage::route('/create'),
            'view' => Pages\ViewFocusImage::route('/{record}'),
            'edit' => Pages\EditFocusImage::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return '焦点图';
    }

    public static function getPluralModelLabel(): string
    {
        return '焦点图';
    }
}