<?php

namespace Database\Factories;

use App\Models\Article;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ArticleFactory extends Factory
{
    protected $model = Article::class;

    public function definition(): array
    {
        $title = $this->faker->sentence();
        return [
            'title' => $title,
            'content' => json_encode([
                'blocks' => [
                    [
                        'type' => 'paragraph',
                        'data' => [
                            'text' => $this->faker->paragraphs(3, true)
                        ]
                    ]
                ]
            ]),
            'summary' => $this->faker->sentence(),
            'cover_image' => 'articles/default.jpg',
            'meta_data' => json_encode([
                'keywords' => $this->faker->words(3, true),
                'description' => $this->faker->sentence()
            ]),
            'user_id' => User::factory(),
            'view_count' => $this->faker->numberBetween(0, 1000),
            'comment_count' => $this->faker->numberBetween(0, 100),
            'like_count' => $this->faker->numberBetween(0, 500),
            'hot_score' => $this->faker->randomFloat(2, 0, 100),
            'last_viewed_at' => $this->faker->dateTimeThisMonth(),
            'last_commented_at' => $this->faker->dateTimeThisMonth(),
            'short_title' => Str::limit($title, 20),
            'custom_flags' => $this->faker->randomElement(['featured', 'recommended', null]),
            'weight' => $this->faker->numberBetween(0, 100),
            'thumb_image' => 'articles/thumb.jpg',
            'source' => $this->faker->company(),
            'author' => $this->faker->name(),
            'title_color' => $this->faker->hexColor(),
            'allow_comment' => true,
            'view_permission' => 'public',
            'status' => $this->faker->randomElement(['draft', 'pending', 'published', 'rejected']),
            'published_at' => $this->faker->dateTimeThisMonth(),
            'extra_options' => [
                'featured' => $this->faker->boolean(),
                'sticky' => $this->faker->boolean()
            ],
        ];
    }
} 