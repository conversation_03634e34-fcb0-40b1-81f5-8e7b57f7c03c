<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Http\Controllers\RegistrationController;
use Illuminate\Http\Request;

echo "=== 调试实际短信发送过程 ===\n\n";

// 1. 获取活动详情
$activity = ActivityDetail::with(['activity', 'smsTemplate'])->first();
echo "活动信息:\n";
echo "  标题: {$activity->activity->title}\n";
echo "  时间: {$activity->activity_time}\n";
echo "  地点: {$activity->address}\n";
echo "  对象: {$activity->target}\n\n";

// 2. 模拟报名数据
$registration = (object) [
    'name' => '测试用户',
    'phone' => '13800138000'
];

echo "报名用户: {$registration->name}\n\n";

// 3. 直接构建模板数据（模拟buildTemplateData方法的逻辑）
$templateData = [
    'name' => $registration->name,
    'topic' => $activity->activity->title,
    'time' => $activity->activity_time,
    'address' => $activity->address,
    'obj' => $activity->target ?? '全体人员'
];

echo "buildTemplateData返回的数据:\n";
foreach ($templateData as $key => $value) {
    echo "  {$key}: '{$value}'\n";
}
echo "\n";

// 4. 模拟SmsService的处理
echo "SmsService处理过程:\n";
$tplValue = '';
if (!empty($templateData)) {
    $pairs = [];
    foreach ($templateData as $key => $value) {
        $varName = strpos($key, '#') === false ? "#{$key}#" : $key;
        $pairs[] = urlencode($varName) . '=' . urlencode($value);
        echo "  处理参数 {$key} -> {$varName} = '{$value}'\n";
    }
    $tplValue = implode('&', $pairs);
}

echo "\n构建的tpl_value:\n{$tplValue}\n\n";

// 5. 解码验证
parse_str($tplValue, $decoded);
echo "解码后的参数:\n";
foreach ($decoded as $key => $value) {
    echo "  {$key}: '{$value}'\n";
}

echo "\n=== 调试完成 ===\n";