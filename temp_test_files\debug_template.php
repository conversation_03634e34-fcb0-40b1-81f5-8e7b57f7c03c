<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SmsTemplate;

echo "=== 调试短信模板数据 ===\n\n";

$template = SmsTemplate::where('code', 'activity_registration_confirm')->first();

if ($template) {
    echo "模板名称: {$template->name}\n";
    echo "模板代码: {$template->code}\n";
    echo "云片网模板ID: {$template->yunpian_template_id}\n";
    echo "模板参数 (原始): " . $template->template_params . "\n";
    echo "模板参数 (解析后): " . json_encode($template->getTemplateParamsList(), JSON_UNESCAPED_UNICODE) . "\n";
    $paramsList = $template->getTemplateParamsList();
    if (empty($paramsList) || !is_array($paramsList)) {
        echo "参数键名: 无\n";
    } else {
        echo "参数键名: " . implode(', ', array_keys($paramsList)) . "\n";
    }
    
    // 测试参数验证
    $testData = [
        'name' => '张三',
        'topic' => '测试活动',
        'time' => '2024-01-20 14:00',
        'address' => '上海市嘉定区'
    ];
    
    echo "\n测试数据键名: " . implode(', ', array_keys($testData)) . "\n";
    
    $paramsList = $template->getTemplateParamsList();
    $requiredParams = is_array($paramsList) ? array_keys($paramsList) : [];
    $providedParams = array_keys($testData);
    $missingParams = array_diff($requiredParams, $providedParams);
    
    echo "需要的参数: " . (empty($requiredParams) ? '无' : implode(', ', $requiredParams)) . "\n";
    echo "提供的参数: " . implode(', ', $providedParams) . "\n";
    echo "缺少的参数: " . implode(', ', $missingParams) . "\n";
    
    $validation = $template->validateParams($testData);
    if ($validation === true) {
        echo "✓ 参数验证通过\n";
    } else {
        echo "✗ 参数验证失败，缺少: " . implode(', ', $validation) . "\n";
    }
} else {
    echo "未找到模板\n";
}