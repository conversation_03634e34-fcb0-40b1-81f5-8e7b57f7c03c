<?php

namespace App\Filament\Resources\AdministrativeInstitutionResource\Pages;

use App\Filament\Resources\AdministrativeInstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;

class ViewAdministrativeInstitution extends ViewRecord
{
    protected static string $resource = AdministrativeInstitutionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('基本信息')
                    ->schema([
                        TextEntry::make('name')
                            ->label('单位名称'),
                        TextEntry::make('code')
                            ->label('机构代码'),
                        TextEntry::make('administrative_level_name')
                            ->label('行政级别')
                            ->getStateUsing(fn ($record) => $record->administrative_level_name),
                        TextEntry::make('binding_status_name')
                            ->label('绑定状态')
                            ->getStateUsing(fn ($record) => $record->binding_status_name)
                            ->badge()
                            ->color(fn ($record) => $record->binding_status === 'bound' ? 'success' : 'danger'),
                    ])
                    ->columns(2),
                    
                Section::make('地区信息')
                    ->schema([
                        TextEntry::make('province_name')
                            ->label('省份'),
                        TextEntry::make('city_name')
                            ->label('城市'),
                        TextEntry::make('district_name')
                            ->label('区县'),
                        TextEntry::make('town_name')
                            ->label('乡镇'),
                        TextEntry::make('full_region_name')
                            ->label('完整地区')
                            ->getStateUsing(fn ($record) => $record->full_region_name)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Section::make('组织信息')
                    ->schema([
                        TextEntry::make('host_unit')
                            ->label('主办单位'),
                        TextEntry::make('parent.name')
                            ->label('组织归属'),
                        TextEntry::make('hierarchy_path')
                            ->label('层级路径')
                            ->getStateUsing(fn ($record) => $record->getHierarchyPath())
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Section::make('联系信息')
                    ->schema([
                        TextEntry::make('contact_person')
                            ->label('联系人'),
                        TextEntry::make('contact_phone')
                            ->label('联系电话'),
                        TextEntry::make('contact_email')
                            ->label('联系邮箱'),
                        TextEntry::make('address')
                            ->label('详细地址')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Section::make('其他信息')
                    ->schema([
                        TextEntry::make('description')
                            ->label('机构描述')
                            ->columnSpanFull(),
                        TextEntry::make('sort_order')
                            ->label('排序'),
                        IconEntry::make('is_active')
                            ->label('是否启用')
                            ->boolean(),
                        TextEntry::make('created_at')
                            ->label('创建时间')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->label('更新时间')
                            ->dateTime(),
                    ])
                    ->columns(2),
            ]);
    }
}