<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class WechatAccount extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'app_id',
        'app_secret',
        'token',
        'aes_key',
        'account_type',
        'description',
        'avatar',
        'qr_code',
        'is_active',
        'auto_collect',
        'collect_interval',
        'last_collect_at',
        'collect_config',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'auto_collect' => 'boolean',
        'collect_interval' => 'integer',
        'last_collect_at' => 'datetime',
        'collect_config' => 'array',
    ];

    protected $hidden = [
        'app_secret',
        'token',
        'aes_key',
    ];

    /**
     * 关联的微信文章
     */
    public function wechatArticles(): HasMany
    {
        return $this->hasMany(WechatArticle::class);
    }

    /**
     * 获取账号类型标签
     */
    public function getAccountTypeLabel(): string
    {
        return match($this->account_type) {
            'subscription' => '订阅号',
            'service' => '服务号',
            'enterprise' => '企业号',
            default => '未知类型'
        };
    }

    /**
     * 检查是否需要采集
     */
    public function shouldCollect(): bool
    {
        if (!$this->is_active || !$this->auto_collect) {
            return false;
        }

        if (!$this->last_collect_at) {
            return true;
        }

        return $this->last_collect_at->addMinutes($this->collect_interval)->isPast();
    }

    /**
     * 更新最后采集时间
     */
    public function updateLastCollectTime(): void
    {
        $this->update(['last_collect_at' => now()]);
    }

    /**
     * 获取EasyWeChat配置
     */
    public function getWechatConfig(): array
    {
        return [
            'app_id' => $this->app_id,
            'secret' => $this->app_secret,
            'token' => $this->token,
            'aes_key' => $this->aes_key,
            'response_type' => 'array',
        ];
    }

    /**
     * 作用域：启用的账号
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：自动采集的账号
     */
    public function scopeAutoCollect($query)
    {
        return $query->where('auto_collect', true);
    }

    /**
     * 获取采集配置
     */
    public function getCollectConfig(): array
    {
        return $this->collect_config ?? [];
    }
}