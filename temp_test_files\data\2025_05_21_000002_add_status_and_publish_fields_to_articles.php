<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->string('status', 20)->default('draft')->comment('文章状态：draft草稿、pending待审核、published已发布、rejected已驳回')->after('view_permission');
            $table->timestamp('published_at')->nullable()->comment('发布时间')->after('status');
        });
    }

    public function down(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->dropColumn(['status', 'published_at']);
        });
    }
}; 