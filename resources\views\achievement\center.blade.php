@extends('layouts.app')

@section('title', '成就中心')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- 成就总览卡片 -->
    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">成长总积分</h1>
                <div class="text-6xl font-bold mb-4">{{ $userStats['total_points'] ?? 0 }}</div>
                <div class="space-y-1">
                    <div>您在全区的排名为：第{{ $userStats['total_ranking'] ?? 0 }}名</div>
                    <div>您在本校的排名为：第{{ $userStats['year_ranking'] ?? 0 }}名</div>
                </div>
            </div>
            <div class="text-right">
                <div class="w-32 h-32 bg-yellow-300 rounded-full flex items-center justify-center">
                    <svg class="w-20 h-20 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 排行榜 -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b">
            <h2 class="text-xl font-semibold text-gray-800">积分排行榜</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排名</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">教师</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学校</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总积分</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($rankings as $index => $user)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                @if($index + 1 <= 3)
                                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full text-white font-bold
                                        {{ $index + 1 == 1 ? 'bg-yellow-500' : ($index + 1 == 2 ? 'bg-gray-400' : 'bg-orange-400') }}">
                                        {{ $index + 1 }}
                                    </span>
                                @else
                                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white font-bold">
                                        {{ $index + 1 }}
                                    </span>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $user->real_name ?? $user->name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $user->school ?? $user->region }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-semibold text-green-600">{{ $user->total_points ?? 0 }}分</div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- 积分规则说明 -->
    <div class="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">积分规则说明</h3>
        <div class="space-y-2 text-blue-700">
            <p>• 用户使用实名账号登录系统后：</p>
            <p>• 当日首次登录 + 浏览任意页面超过 30 秒，可获得 1 积分</p>
            <p>• 浏览视频内容可获得 5 积分</p>
            <p>• 同一页面/版块1小时内重复刷新不重复计分</p>
            <p>• 游客账号不参与积分统计</p>
        </div>
    </div>
</div>

<script>
// 页面浏览时间跟踪
let startTime = Date.now();
let tracked = false;

// 页面卸载时记录浏览时间
window.addEventListener('beforeunload', function() {
    if (!tracked) {
        let duration = Math.floor((Date.now() - startTime) / 1000);
        if (duration >= 30) {
            // 发送浏览行为记录
            navigator.sendBeacon('/api/track-behavior', JSON.stringify({
                action: 'browse',
                target_type: 'page',
                target_id: 'achievement-center',
                duration: duration,
                url: window.location.href,
                _token: '{{ csrf_token() }}'
            }));
        }
        tracked = true;
    }
});

// 30秒后自动记录一次
setTimeout(function() {
    if (!tracked) {
        let duration = Math.floor((Date.now() - startTime) / 1000);
        fetch('/api/track-behavior', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                action: 'browse',
                target_type: 'page',
                target_id: 'achievement-center',
                duration: duration,
                url: window.location.href
            })
        });
        tracked = true;
    }
}, 30000);
</script>
@endsection
