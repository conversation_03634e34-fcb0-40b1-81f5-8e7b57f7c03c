<?php

namespace App\Providers;

use App\Services\WechatArticleCollectService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Storage;

class WechatCollectServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(WechatArticleCollectService::class, function ($app) {
            return new WechatArticleCollectService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__.'/../../config/wechat-collect.php' => config_path('wechat-collect.php'),
        ], 'wechat-collect-config');

        // 创建存储目录
        $this->createStorageDirectories();

        // 注册命令
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\CollectWechatArticles::class,
            ]);
        }
    }

    /**
     * 创建必要的存储目录
     */
    private function createStorageDirectories(): void
    {
        $directories = [
            config('wechat-collect.thumbnail.storage_path', 'wechat/thumbnails'),
            config('wechat-collect.content.image_storage_path', 'wechat/images'),
        ];

        foreach ($directories as $directory) {
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }
        }
    }
}