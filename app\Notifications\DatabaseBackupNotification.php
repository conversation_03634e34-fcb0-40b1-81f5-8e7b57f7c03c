<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\DatabaseBackup;

class DatabaseBackupNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public DatabaseBackup $backup;
    public string $type;

    /**
     * Create a new notification instance.
     */
    public function __construct(DatabaseBackup $backup, string $type = 'completed')
    {
        $this->backup = $backup;
        $this->type = $type; // completed, failed
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = new MailMessage();

        if ($this->type === 'completed') {
            $message->subject('数据备份完成通知')
                ->greeting('数据备份完成')
                ->line("备份名称：{$this->backup->name}")
                ->line("备份类型：" . DatabaseBackup::getBackupTypes()[$this->backup->backup_type])
                ->line("文件大小：{$this->backup->formatted_file_size}")
                ->line("备份时间：{$this->backup->backup_completed_at->format('Y-m-d H:i:s')}")
                ->line("耗时：{$this->backup->duration}")
                ->action('查看备份', url('/admin/database-backups/' . $this->backup->id))
                ->line('备份已成功完成，请及时下载保存。');
        } else {
            $message->subject('数据备份失败通知')
                ->greeting('数据备份失败')
                ->line("备份名称：{$this->backup->name}")
                ->line("备份类型：" . DatabaseBackup::getBackupTypes()[$this->backup->backup_type])
                ->line("失败时间：{$this->backup->backup_completed_at->format('Y-m-d H:i:s')}")
                ->line("错误信息：{$this->backup->error_message}")
                ->action('查看详情', url('/admin/database-backups/' . $this->backup->id))
                ->line('请检查系统配置并重新尝试备份。');
        }

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'backup_id' => $this->backup->id,
            'backup_name' => $this->backup->name,
            'backup_type' => $this->backup->backup_type,
            'status' => $this->backup->status,
            'type' => $this->type,
            'message' => $this->type === 'completed'
                ? "数据备份 '{$this->backup->name}' 已成功完成"
                : "数据备份 '{$this->backup->name}' 执行失败",
        ];
    }
}
