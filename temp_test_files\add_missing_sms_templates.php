<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use App\Models\SmsTemplate;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 添加缺失的短信模板 ===\n\n";

// 用户提供的云片网模板列表
$yunpianTemplates = [
    [
        'content' => '【上海市嘉定区教育学院】验证码#code#，您正在进行嘉定幸福课程身份验证，感谢关注',
        'code' => 'jiading_verification',
        'name' => '嘉定幸福课程身份验证',
        'params' => ['code' => '验证码']
    ],
    [
        'content' => '【上海市嘉定区教育学院】您的验证码是#code#。如非本人操作，请忽略本短信',
        'code' => 'general_verification', 
        'name' => '通用验证码',
        'params' => ['code' => '验证码']
    ],
    [
        'content' => '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。',
        'code' => 'consultation_notice',
        'name' => '现场咨询通知',
        'params' => ['topic' => '活动主题', 'time' => '活动时间', 'address' => '活动地点', 'obj' => '参与对象']
    ],
    [
        'content' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已通过审核，感谢关注',
        'code' => 'account_approved',
        'name' => '账号审核通过',
        'params' => ['name' => '用户姓名']
    ],
    [
        'content' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号未能通过审核，可能的原因是您不是本课程的适用对象，感谢关注！',
        'code' => 'account_rejected',
        'name' => '账号审核未通过',
        'params' => ['name' => '用户姓名']
    ],
    [
        'content' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已开通，请用微信关注公众号嘉定区幸福课程，并用手机号登录，密码为#pwd#',
        'code' => 'account_activated',
        'name' => '账号开通通知',
        'params' => ['name' => '用户姓名', 'pwd' => '登录密码']
    ],
    [
        'content' => '【上海市嘉定区教育学院】验证码#code#，您正在进行心灵嘉园身份验证，感谢关注',
        'code' => 'xinling_verification',
        'name' => '心灵嘉园身份验证',
        'params' => ['code' => '验证码']
    ],
    [
        'content' => '【上海市嘉定区教育学院】您好，您报的暑期大培训课程"#name#"已报名成功，请关注开课时间，准时参加！',
        'code' => 'summer_training_success',
        'name' => '暑期大培训报名成功',
        'params' => ['name' => '课程名称']
    ]
];

echo "检查现有模板...\n";
$existingTemplates = SmsTemplate::pluck('code')->toArray();
echo "现有模板代码: " . implode(', ', $existingTemplates) . "\n\n";

$addedCount = 0;
$updatedCount = 0;

foreach ($yunpianTemplates as $template) {
    $existing = SmsTemplate::where('code', $template['code'])->first();
    
    if ($existing) {
        echo "✓ 模板 '{$template['code']}' 已存在\n";
        
        // 检查是否需要更新参数
        $currentParams = is_array($existing->template_params) 
            ? $existing->template_params 
            : json_decode($existing->template_params, true);
            
        if (json_encode($currentParams, JSON_UNESCAPED_UNICODE) !== json_encode($template['params'], JSON_UNESCAPED_UNICODE)) {
            $existing->template_params = $template['params'];
            $existing->save();
            echo "  → 更新了参数配置\n";
            $updatedCount++;
        }
    } else {
        echo "+ 添加新模板 '{$template['code']}'\n";
        
        // 为新模板分配一个临时的云片模板ID（需要后续在云片网后台配置）
        $yunpianTemplateId = 6000000 + rand(1000, 9999);
        
        SmsTemplate::create([
            'code' => $template['code'],
            'name' => $template['name'],
            'template_params' => $template['params'],
            'yunpian_template_id' => $yunpianTemplateId,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "  → 云片模板ID: {$yunpianTemplateId} (临时ID，需要在云片网后台配置)\n";
        $addedCount++;
    }
}

echo "\n=== 处理结果 ===\n";
echo "新增模板: {$addedCount} 个\n";
echo "更新模板: {$updatedCount} 个\n";

if ($addedCount > 0) {
    echo "\n⚠️  注意事项：\n";
    echo "1. 新增的模板使用了临时的云片模板ID\n";
    echo "2. 请在云片网后台创建对应的短信模板\n";
    echo "3. 获取真实的模板ID后，请更新数据库中的yunpian_template_id字段\n";
}

echo "\n=== 最终模板列表 ===\n";
$finalTemplates = SmsTemplate::all(['code', 'name', 'yunpian_template_id']);
foreach ($finalTemplates as $template) {
    echo "- {$template->code}: {$template->name} (ID: {$template->yunpian_template_id})\n";
}

echo "\n=== 完成 ===\n";