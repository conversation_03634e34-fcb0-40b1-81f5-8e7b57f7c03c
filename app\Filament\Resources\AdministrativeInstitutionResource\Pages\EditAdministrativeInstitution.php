<?php

namespace App\Filament\Resources\AdministrativeInstitutionResource\Pages;

use App\Filament\Resources\AdministrativeInstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;

class EditAdministrativeInstitution extends EditRecord
{
    protected static string $resource = AdministrativeInstitutionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('查看'),
            Actions\DeleteAction::make()
                ->label('删除')
                ->after(function () {
                    // 删除后清除缓存
                    $this->clearRelatedCaches();
                }),
            Actions\ForceDeleteAction::make()
                ->after(function () {
                    $this->clearRelatedCaches();
                }),
            Actions\RestoreAction::make()
                ->after(function () {
                    $this->clearRelatedCaches();
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return '行政机构更新成功';
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // 根据行政级别自动设置一些默认值
        if ($data['administrative_level'] === 'province') {
            $data['parent_id'] = null; // 省级机构没有上级
        }

        // 根据地区信息自动生成完整地区名称
        $regionParts = [];
        if (!empty($data['province_name'])) $regionParts[] = $data['province_name'];
        if (!empty($data['city_name'])) $regionParts[] = $data['city_name'];
        if (!empty($data['district_name'])) $regionParts[] = $data['district_name'];
        if (!empty($data['town_name'])) $regionParts[] = $data['town_name'];
        
        $data['full_region_name'] = implode('', $regionParts);

        return $data;
    }

    protected function afterSave(): void
    {
        // 保存后清除相关缓存
        $this->clearRelatedCaches();
    }

    /**
     * 清除相关缓存
     */
    private function clearRelatedCaches(): void
    {
        // 清除统计缓存
        Cache::forget('admin_institutions_counts');
        
        // 清除省市区选项缓存
        Cache::forget('province_options');
        Cache::forget('city_options');
        Cache::forget('district_options');
        Cache::forget('town_options');
        
        // 清除级联筛选器缓存
        $this->clearCascadingFilterCaches();
    }
    
    /**
     * 清除级联筛选器缓存
     */
    private function clearCascadingFilterCaches(): void
    {
        // 清除省份缓存
        Cache::forget('admin_institutions_provinces');
        
        // 清除所有城市缓存
        $cacheKeys = [];
        if (function_exists('redis') && Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            try {
                $cacheKeys = Cache::getRedis()->keys('*admin_institutions_cities_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
                
                // 清除所有区县缓存
                $cacheKeys = Cache::getRedis()->keys('*admin_institutions_districts_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
                
                // 清除所有乡镇缓存
                $cacheKeys = Cache::getRedis()->keys('*admin_institutions_towns_*');
                foreach ($cacheKeys as $key) {
                    Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
                }
            } catch (\Exception $e) {
                // 如果Redis不可用，使用备用清理方法
                $this->clearCascadingCachesFallback();
            }
        } else {
            $this->clearCascadingCachesFallback();
        }
    }
    
    /**
     * 备用缓存清理方法
     */
    private function clearCascadingCachesFallback(): void
    {
        // 获取所有省份代码并清除对应缓存
        $provinceCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'province')
            ->pluck('province_code')
            ->unique();
            
        foreach ($provinceCodes as $code) {
            Cache::forget("admin_institutions_cities_{$code}");
        }
        
        // 获取所有城市代码并清除对应缓存
        $cityCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'city')
            ->pluck('city_code')
            ->unique();
            
        foreach ($cityCodes as $code) {
            Cache::forget("admin_institutions_districts_{$code}");
        }
        
        // 获取所有区县代码并清除对应缓存
        $districtCodes = \App\Models\AdministrativeInstitution::where('administrative_level', 'district')
            ->pluck('district_code')
            ->unique();
            
        foreach ($districtCodes as $code) {
            Cache::forget("admin_institutions_towns_{$code}");
        }
    }
}