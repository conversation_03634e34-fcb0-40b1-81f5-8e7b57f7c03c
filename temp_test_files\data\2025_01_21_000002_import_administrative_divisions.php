<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\AdministrativeInstitution;

class ImportAdministrativeDivisions extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 读取JS文件内容
        $jsFilePath = database_path('area_format_object.level4.js');
        
        if (!file_exists($jsFilePath)) {
            throw new Exception('Area data file not found: ' . $jsFilePath);
        }
        
        $jsContent = file_get_contents($jsFilePath);
        
        // 提取数据部分 - 改进的正则表达式
        if (preg_match('/var data=({.*?});\s*return/s', $jsContent, $matches)) {
            $dataString = $matches[1];
        } elseif (preg_match('/var data=({.*?})\s*;/s', $jsContent, $matches)) {
            $dataString = $matches[1];
        } else {
            // 如果正则匹配失败，尝试直接查找数据结构
            $startPos = strpos($jsContent, 'var data={');
            if ($startPos === false) {
                throw new Exception('Could not find data variable in JS file');
            }
            
            $startPos += 9; // 跳过 'var data='
            $braceCount = 0;
            $endPos = $startPos;
            
            for ($i = $startPos; $i < strlen($jsContent); $i++) {
                if ($jsContent[$i] === '{') {
                    $braceCount++;
                } elseif ($jsContent[$i] === '}') {
                    $braceCount--;
                    if ($braceCount === 0) {
                        $endPos = $i + 1;
                        break;
                    }
                }
            }
            
            $dataString = substr($jsContent, $startPos, $endPos - $startPos);
        }
        
        // 将JS对象转换为PHP数组
        $jsonString = $this->convertJsObjectToJson($dataString);
        $data = json_decode($jsonString, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON decode error: ' . json_last_error_msg() . ' - Data preview: ' . substr($jsonString, 0, 500));
        }
        
        // 导入数据
        $this->importData($data);
    }
    
    /**
     * 将JS对象格式转换为JSON格式
     */
    private function convertJsObjectToJson($jsObject)
    {
        // 替换JS对象的键名格式
        $jsonString = preg_replace('/"(\d+)"\s*:\s*{/', '"$1":{', $jsObject);
        $jsonString = preg_replace('/"([ncy])"\s*:\s*"([^"]+)"/', '"$1":"$2"', $jsonString);
        
        return $jsonString;
    }
    
    /**
     * 导入数据到数据库
     */
    private function importData($data)
    {
        DB::beginTransaction();
        
        try {
            foreach ($data as $provinceCode => $provinceData) {
                // 导入省级数据
                $province = $this->createAdministrativeInstitution([
                    'name' => $provinceData['n'],
                    'institution_code' => $provinceCode,
                    'province_code' => $provinceCode,
                    'administrative_level' => 'province',
                    'parent_id' => null,
                    'is_active' => true,
                    'sort_order' => (int)$provinceCode
                ]);
                
                if (isset($provinceData['c'])) {
                    foreach ($provinceData['c'] as $cityCode => $cityData) {
                        // 导入市级数据
                        $city = $this->createAdministrativeInstitution([
                            'name' => $cityData['n'],
                            'institution_code' => $cityCode,
                            'province_code' => $provinceCode,
                            'city_code' => $cityCode,
                            'administrative_level' => 'city',
                            'parent_id' => $province->id,
                            'is_active' => true,
                            'sort_order' => (int)substr($cityCode, -2)
                        ]);
                        
                        if (isset($cityData['c'])) {
                            foreach ($cityData['c'] as $districtCode => $districtData) {
                                // 导入区县级数据
                                $district = $this->createAdministrativeInstitution([
                                    'name' => $districtData['n'],
                                    'institution_code' => $districtCode,
                                    'province_code' => $provinceCode,
                                    'city_code' => $cityCode,
                                    'district_code' => $districtCode,
                                    'administrative_level' => 'district',
                                    'parent_id' => $city->id,
                                    'is_active' => true,
                                    'sort_order' => (int)substr($districtCode, -2)
                                ]);
                                
                                if (isset($districtData['c'])) {
                                    foreach ($districtData['c'] as $townCode => $townData) {
                                        // 导入乡镇级数据
                                        $this->createAdministrativeInstitution([
                                            'name' => $townData['n'],
                                            'institution_code' => $townCode,
                                            'province_code' => $provinceCode,
                                            'city_code' => $cityCode,
                                            'district_code' => $districtCode,
                                            'town_code' => $townCode,
                                            'administrative_level' => 'town',
                                            'parent_id' => $district->id,
                                            'is_active' => true,
                                            'sort_order' => (int)substr($townCode, -3)
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            DB::commit();
            
            echo "Administrative divisions imported successfully!\n";
            
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    
    /**
     * 创建行政机构记录
     */
    private function createAdministrativeInstitution($data)
    {
        return AdministrativeInstitution::create(array_merge($data, [
            'created_at' => now(),
            'updated_at' => now()
        ]));
    }
    
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除所有导入的行政区划数据
        AdministrativeInstitution::whereIn('administrative_level', [
            'province', 'city', 'district', 'town'
        ])->delete();
    }
}