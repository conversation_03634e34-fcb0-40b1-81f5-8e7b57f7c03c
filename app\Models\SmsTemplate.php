<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code', 
        'yunpian_template_id',
        'description',
        'template_params',
        'param_description',
        'template_category'
    ];

    protected $casts = [
        'template_params' => 'array'
    ];

    /**
     * 获取模板参数列表
     */
    public function getTemplateParamsList()
    {
        $params = $this->template_params;
        
        // 如果是字符串，尝试解析为JSON
        if (is_string($params)) {
            $decoded = json_decode($params, true);
            return is_array($decoded) ? $decoded : [];
        }
        
        // 如果已经是数组，直接返回
        if (is_array($params)) {
            return $params;
        }
        
        return [];
    }

    /**
     * 验证参数完整性
     */
    public function validateParams(array $data)
    {
        $templateParams = $this->getTemplateParamsList();
        
        // 如果模板参数为空或不是数组，则认为验证通过
        if (empty($templateParams) || !is_array($templateParams)) {
            return true;
        }
        
        $requiredParams = array_keys($templateParams);
        $providedParams = array_keys($data);
        $missingParams = array_diff($requiredParams, $providedParams);
        
        return empty($missingParams) ? true : $missingParams;
    }

    /**
     * 通过代码查找模板
     */
    public static function findByCode($code)
    {
        return static::where('code', $code)->first();
    }

    /**
     * 获取分类选项
     */
    public static function getCategoryOptions()
    {
        return [
            'verification' => '验证码类',
            'account' => '账号管理类',
            'registration' => '报名类',
            'consultation' => '咨询类',
            'notification' => '通知类',
            'custom' => '自定义类'
        ];
    }
}
