<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class QuillUploadController extends Controller
{
    public function image(Request $request)
    {
        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('quill/images', 'public');
            $url = Storage::disk('public')->url($path);
            return response()->json(['success' => 1, 'file' => ['url' => $url]]);
        }
        return response()->json(['success' => 0, 'message' => '上传失败'], 400);
    }

    public function video(Request $request)
    {
        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('quill/videos', 'public');
            $url = Storage::disk('public')->url($path);
            return response()->json(['success' => 1, 'file' => ['url' => $url]]);
        }
        return response()->json(['success' => 0, 'message' => '上传失败'], 400);
    }
} 