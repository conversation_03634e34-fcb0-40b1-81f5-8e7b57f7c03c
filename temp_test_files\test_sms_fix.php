<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 测试短信参数修复效果 ===\n\n";

// 模拟buildTemplateData方法的逻辑
function simulateBuildTemplateData($registration) {
    $activityDetail = (object)[
        'activity_time' => $registration->activity_time,
        'address' => $registration->address,
        'target' => $registration->target,
        'theme' => $registration->theme
    ];
    
    $activity = (object)[
        'title' => $registration->activity_title
    ];
    
    // 获取用户姓名，确保不为空
    $name = !empty($registration->name) ? $registration->name : "用户";
    
    // 获取活动标题，优先使用activity.title，其次使用activity_detail.theme
    $topic = "";
    if (!empty($activity->title)) {
        $topic = $activity->title;
    } elseif (!empty($activityDetail->theme)) {
        $topic = $activityDetail->theme;
    } else {
        $topic = "活动通知";
    }
    
    // 获取活动时间，确保格式正确
    $time = "";
    if (!empty($activityDetail->activity_time)) {
        try {
            $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
        } catch (Exception $e) {
            $time = "待定时间";
        }
    } else {
        $time = "待定时间";
    }
    
    // 获取活动地点
    $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
    
    // 获取目标对象
    $obj = !empty($activityDetail->target) ? $activityDetail->target : "全体人员";
    
    return [
        "name" => $name,
        "topic" => $topic,
        "time" => $time,
        "address" => $address,
        "obj" => $obj
    ];
}

// 获取所有报名记录进行测试
$registrations = DB::table('registrations')
    ->join('activity_details', 'registrations.activity_detail_id', '=', 'activity_details.id')
    ->join('activities', 'activity_details.activity_id', '=', 'activities.id')
    ->select(
        'registrations.id as registration_id',
        'registrations.name',
        'activities.title as activity_title',
        'activity_details.theme',
        'activity_details.activity_time',
        'activity_details.address',
        'activity_details.target'
    )
    ->get();

echo "找到 {$registrations->count()} 条报名记录进行测试\n\n";

$allTestsPassed = true;
$testResults = [];

foreach ($registrations as $registration) {
    echo "测试报名记录ID: {$registration->registration_id}\n";
    echo "原始数据:\n";
    echo "  - name: '" . ($registration->name ?: '[空]') . "'\n";
    echo "  - activity_title: '" . ($registration->activity_title ?: '[空]') . "'\n";
    echo "  - theme: '" . ($registration->theme ?: '[空]') . "'\n";
    echo "  - activity_time: '" . ($registration->activity_time ?: '[空]') . "'\n";
    echo "  - address: '" . ($registration->address ?: '[空]') . "'\n";
    echo "  - target: '" . ($registration->target ?: '[空]') . "'\n";
    
    // 模拟构建模板数据
    $templateData = simulateBuildTemplateData($registration);
    
    echo "构建的短信参数:\n";
    foreach ($templateData as $key => $value) {
        echo "  - {$key}: '{$value}'\n";
    }
    
    // 检查是否有空值
    $hasEmptyValues = false;
    $emptyParams = [];
    foreach ($templateData as $key => $value) {
        if (empty($value)) {
            $hasEmptyValues = true;
            $emptyParams[] = $key;
        }
    }
    
    if ($hasEmptyValues) {
        echo "  ❌ 仍有空值参数: " . implode(', ', $emptyParams) . "\n";
        $allTestsPassed = false;
        $testResults[] = [
            'registration_id' => $registration->registration_id,
            'status' => 'failed',
            'empty_params' => $emptyParams
        ];
    } else {
        echo "  ✅ 所有参数都有值\n";
        $testResults[] = [
            'registration_id' => $registration->registration_id,
            'status' => 'passed'
        ];
    }
    
    // 模拟短信内容
    $smsContent = "【上海市嘉定区教育学院】现场咨询，主题为：{$templateData['topic']}；时间为：{$templateData['time']}；地点为：{$templateData['address']}；对象为：{$templateData['obj']}；请准时参加。";
    echo "  模拟短信内容: {$smsContent}\n";
    
    echo "\n" . str_repeat('-', 80) . "\n\n";
}

// 生成测试报告
echo "=== 测试报告 ===\n";
echo "总测试数: {$registrations->count()}\n";

$passedCount = count(array_filter($testResults, function($result) {
    return $result['status'] === 'passed';
}));

$failedCount = count(array_filter($testResults, function($result) {
    return $result['status'] === 'failed';
}));

echo "通过测试: {$passedCount}\n";
echo "失败测试: {$failedCount}\n";

if ($allTestsPassed) {
    echo "\n🎉 所有测试通过！短信参数空值问题已完全修复！\n";
} else {
    echo "\n❌ 仍有测试失败，需要进一步检查:\n";
    foreach ($testResults as $result) {
        if ($result['status'] === 'failed') {
            echo "  - 报名ID {$result['registration_id']}: 空值参数 " . implode(', ', $result['empty_params']) . "\n";
        }
    }
}

echo "\n=== 测试完成 ===\n";