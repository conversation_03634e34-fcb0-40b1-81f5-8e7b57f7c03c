<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Models\Article;
use App\Models\Post;
use App\Models\Comment;
use App\Models\Category;
use App\Models\Tag;
use App\Models\MediaFile;
use App\Models\Setting;
use Illuminate\Support\Facades\Hash;

class BusinessTestSeeder extends Seeder
{
    public function run(): void
    {
        // 用户
        User::factory()->count(5)->create();
        // 活动
        Activity::factory()->count(5)->create();
        // 活动详情
        ActivityDetail::factory()->count(5)->create();
        // 报名
        Registration::factory()->count(5)->create();
        // 文章
        Article::factory()->count(5)->create();
        // 帖子
        Post::factory()->count(5)->create();
        // 评论
        Comment::factory()->count(5)->create();
        // 分类
        Category::factory()->count(5)->create();
        // 标签
        Tag::factory()->count(5)->create();
        // 媒体文件
        MediaFile::factory()->count(5)->create();
        // 系统设置
        Setting::factory()->count(5)->create();
    }
} 