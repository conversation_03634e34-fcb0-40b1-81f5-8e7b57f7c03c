<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 添加云片网短信模板需要的缺失字段
     * 根据用户需求添加：code（活动代码）、name（活动名称）、pwd（密码/验证码）
     */
    public function up(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            // 添加云片网短信模板需要的字段
            $table->string('code')->nullable()->comment('活动代码/编号 - 对应云片网#code#参数');
            $table->string('name')->nullable()->comment('活动名称 - 对应云片网#name#参数');
            $table->string('pwd')->nullable()->comment('密码/验证码 - 对应云片网#pwd#参数');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity_details', function (Blueprint $table) {
            $table->dropColumn(['code', 'name', 'pwd']);
        });
    }
};