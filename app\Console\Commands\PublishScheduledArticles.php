<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Article;

class PublishScheduledArticles extends Command
{
    protected $signature = 'articles:publish-scheduled';
    protected $description = '自动发布到期的定时文章';

    public function handle()
    {
        $count = Article::where('status', 'pending')
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->update(['status' => 'published']);
        $this->info("已自动发布 {$count} 篇文章。");
    }
} 