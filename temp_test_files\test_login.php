<?php
/**
 * 管理员登录测试脚本
 */

echo "🔐 CMS管理员登录测试\n";
echo "==================\n\n";

// 测试账号信息
$accounts = [
    [
        'email' => '<EMAIL>',
        'password' => 'password',
        'name' => 'Super Admin',
        'description' => '超级管理员账号'
    ],
    [
        'email' => '<EMAIL>', 
        'password' => 'password',
        'name' => 'Admin',
        'description' => '普通管理员账号'
    ]
];

echo "📋 可用的管理员账号：\n";
echo "====================\n";

foreach ($accounts as $index => $account) {
    echo ($index + 1) . ". {$account['description']}\n";
    echo "   邮箱: {$account['email']}\n";
    echo "   密码: {$account['password']}\n";
    echo "   姓名: {$account['name']}\n";
    echo "\n";
}

echo "🌐 后台登录地址：\n";
echo "================\n";
echo "URL: http://127.0.0.1:8000/admin\n\n";

echo "📝 登录步骤：\n";
echo "============\n";
echo "1. 打开浏览器访问: http://127.0.0.1:8000/admin\n";
echo "2. 使用以上任一账号登录\n";
echo "3. 如果仍然403，请检查以下内容：\n";
echo "   - 确保服务器正在运行 (php artisan serve)\n";
echo "   - 清除浏览器缓存\n";
echo "   - 检查网络连接\n\n";

// 测试服务器连接
echo "🔍 测试服务器连接...\n";
echo "===================\n";

$testUrl = 'http://127.0.0.1:8000/admin';
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'method' => 'GET'
    ]
]);

$response = @file_get_contents($testUrl, false, $context);

if ($response !== false) {
    echo "✅ 服务器连接正常\n";
    echo "✅ 后台页面可以访问\n";
    
    // 检查是否是登录页面
    if (strpos($response, 'login') !== false || strpos($response, 'Login') !== false) {
        echo "✅ 登录页面加载正常\n";
    } else {
        echo "⚠️  页面内容异常，可能不是登录页面\n";
    }
} else {
    echo "❌ 服务器连接失败\n";
    echo "💡 请确保运行: php artisan serve\n";
}

echo "\n";

// 检查数据库连接
echo "🗄️  测试数据库连接...\n";
echo "=====================\n";

try {
    // 简单的数据库连接测试
    $output = shell_exec('php artisan tinker --execute="echo \"Database connection: \" . (DB::connection()->getPdo() ? \"OK\" : \"Failed\") . \"\\n\";"');
    
    if (strpos($output, 'OK') !== false) {
        echo "✅ 数据库连接正常\n";
    } else {
        echo "❌ 数据库连接失败\n";
        echo "💡 请检查 .env 文件中的数据库配置\n";
    }
} catch (Exception $e) {
    echo "❌ 数据库测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🎯 推荐登录账号：\n";
echo "================\n";
echo "邮箱: <EMAIL>\n";
echo "密码: password\n";
echo "说明: 这是超级管理员账号，拥有最高权限\n\n";

echo "🔧 如果仍然无法登录，请尝试：\n";
echo "==============================\n";
echo "1. 清除应用缓存: php artisan cache:clear\n";
echo "2. 清除配置缓存: php artisan config:clear\n";
echo "3. 清除路由缓存: php artisan route:clear\n";
echo "4. 重启服务器: Ctrl+C 然后 php artisan serve\n";
echo "5. 清除浏览器缓存和Cookie\n\n";

echo "📞 技术支持：\n";
echo "============\n";
echo "如果问题仍然存在，请提供以下信息：\n";
echo "- 浏览器控制台错误信息\n";
echo "- Laravel日志文件内容 (storage/logs/laravel.log)\n";
echo "- 具体的错误页面截图\n\n";

echo "✨ 测试完成！\n";
?>
