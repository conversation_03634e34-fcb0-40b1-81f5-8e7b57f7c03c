<?php

namespace App\Filament\Resources\WechatMessageResource\Pages;

use App\Filament\Resources\WechatMessageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListWechatMessages extends ListRecords
{
    protected static string $resource = WechatMessageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}