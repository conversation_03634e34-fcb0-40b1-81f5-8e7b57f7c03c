<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('focus_images', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('标题');
            $table->string('subtitle')->nullable()->comment('副标题');
            $table->text('description')->nullable()->comment('描述');
            $table->string('image')->comment('图片路径');
            $table->string('alt_text')->nullable()->comment('图片替代文本');
            $table->string('link_url')->nullable()->comment('链接地址');
            $table->enum('link_target', ['_self', '_blank'])
                  ->default('_self')
                  ->comment('链接打开方式');
            $table->enum('position', ['home_banner', 'home_featured', 'sidebar', 'footer', 'floating_window'])
                  ->default('home_banner')
                  ->comment('显示位置');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamp('start_time')->nullable()->comment('开始时间');
            $table->timestamp('end_time')->nullable()->comment('结束时间');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['position']);
            $table->index(['is_active']);
            $table->index(['sort_order']);
            $table->index(['start_time']);
            $table->index(['end_time']);
            $table->index(['position', 'is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('focus_images');
    }
};