<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use Carbon\Carbon;

class RegistrationTestDataSeeder extends Seeder
{
    /**
     * 为报名系统创建测试数据
     */
    public function run(): void
    {
        $this->command->info('🎯 创建报名系统测试数据...');

        // 创建活动
        $activities = $this->createActivities();
        
        // 为每个活动创建场次
        foreach ($activities as $activity) {
            $activityDetails = $this->createActivityDetails($activity);
            
            // 为每个场次创建报名记录
            foreach ($activityDetails as $detail) {
                $this->createRegistrations($detail);
            }
        }

        $this->command->info('✅ 报名系统测试数据创建完成！');
    }

    /**
     * 创建活动
     */
    private function createActivities()
    {
        $this->command->info('📅 创建活动...');

        $activities = [
            [
                'title' => '2024年春季教学研讨会',
                'status' => 'published',
                'publish_time' => Carbon::now()->subDays(10),
                'views_count' => 156,
                'qrcode_url' => 'https://example.com/qr/spring-seminar',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example1',
            ],
            [
                'title' => '学生科技创新大赛',
                'status' => 'published', 
                'publish_time' => Carbon::now()->subDays(5),
                'views_count' => 89,
                'qrcode_url' => 'https://example.com/qr/tech-competition',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example2',
            ],
            [
                'title' => '家长开放日活动',
                'status' => 'published',
                'publish_time' => Carbon::now()->subDays(3),
                'views_count' => 234,
                'qrcode_url' => 'https://example.com/qr/parent-day',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example3',
            ],
            [
                'title' => '暑期夏令营报名',
                'status' => 'draft',
                'publish_time' => Carbon::now()->addDays(7),
                'views_count' => 0,
                'qrcode_url' => 'https://example.com/qr/summer-camp',
                'wechat_url' => 'https://mp.weixin.qq.com/s/example4',
            ],
        ];

        $createdActivities = [];
        foreach ($activities as $activityData) {
            $activity = Activity::create($activityData);
            $createdActivities[] = $activity;
            $this->command->info("  ✅ 创建活动: {$activity->title}");
        }

        return $createdActivities;
    }

    /**
     * 创建活动场次
     */
    private function createActivityDetails($activity)
    {
        $this->command->info("📋 为活动 '{$activity->title}' 创建场次...");

        $details = [
            [
                'theme' => $activity->title . ' - 第一场',
                'deadline' => Carbon::now()->addDays(30),
                'registration_deadline' => Carbon::now()->addDays(25),
                'status' => 1,
                'quota' => 100,
                'target' => '全体教师',
                'target_audience' => '小学教师',
                'current_count' => 0,
                'activity_time' => Carbon::now()->addDays(35),
                'fee' => '免费',
                'reminder' => '请准时参加，携带相关材料',
                'registration_method' => '在线报名',
                'address' => '学校多功能厅',
                'sms_template' => '您好，您已成功报名参加{活动名称}，时间：{活动时间}，地点：{活动地点}。',
            ],
            [
                'theme' => $activity->title . ' - 第二场',
                'deadline' => Carbon::now()->addDays(45),
                'registration_deadline' => Carbon::now()->addDays(40),
                'status' => 1,
                'quota' => 80,
                'target' => '骨干教师',
                'target_audience' => '中学教师',
                'current_count' => 0,
                'activity_time' => Carbon::now()->addDays(50),
                'fee' => '50元',
                'reminder' => '请提前15分钟到场',
                'registration_method' => '现场报名',
                'address' => '学校会议室',
                'sms_template' => '您好，您已成功报名参加{活动名称}，时间：{活动时间}，地点：{活动地点}。',
            ],
        ];

        $createdDetails = [];
        foreach ($details as $detailData) {
            $detailData['activity_id'] = $activity->id;
            $detail = ActivityDetail::create($detailData);
            $createdDetails[] = $detail;
            $this->command->info("    ✅ 创建场次: {$detail->theme}");
        }

        return $createdDetails;
    }

    /**
     * 创建报名记录
     */
    private function createRegistrations($activityDetail)
    {
        $this->command->info("👥 为场次 '{$activityDetail->theme}' 创建报名记录...");

        // 生成随机报名数量（10-50人）
        $registrationCount = rand(10, 50);
        
        $schools = [
            '上海市嘉定区实验小学',
            '上海市嘉定区第一中学', 
            '上海市嘉定区第二小学',
            '上海市嘉定区育才中学',
            '上海市嘉定区新城实验小学',
            '上海市嘉定区安亭小学',
            '上海市嘉定区马陆小学',
            '上海市嘉定区南翔小学',
        ];

        $grades = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '高一', '高二', '高三'];
        $sources = ['微信公众号', '学校通知', '朋友推荐', '官网报名', '电话咨询'];

        for ($i = 1; $i <= $registrationCount; $i++) {
            $registration = Registration::create([
                'activity_detail_id' => $activityDetail->id,
                'name' => $this->generateRandomName(),
                'phone' => $this->generateRandomPhone(),
                'organization' => $schools[array_rand($schools)],
                'grade' => $grades[array_rand($grades)],
                'gender' => rand(0, 1) ? 'male' : 'female',
                'source' => $sources[array_rand($sources)],
                'status' => rand(0, 10) > 1 ? 1 : 0, // 90%有效，10%无效
                'created_at' => Carbon::now()->subDays(rand(1, 20)),
            ]);
        }

        // 更新活动场次的当前报名人数
        $activityDetail->update([
            'current_count' => Registration::where('activity_detail_id', $activityDetail->id)
                ->where('status', 1)
                ->count()
        ]);

        $this->command->info("    ✅ 创建了 {$registrationCount} 条报名记录");
    }

    /**
     * 生成随机姓名
     */
    private function generateRandomName()
    {
        $surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗'];
        $names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞'];
        
        return $surnames[array_rand($surnames)] . $names[array_rand($names)];
    }

    /**
     * 生成随机手机号
     */
    private function generateRandomPhone()
    {
        $prefixes = ['138', '139', '150', '151', '152', '158', '159', '188', '189'];
        $prefix = $prefixes[array_rand($prefixes)];
        $suffix = str_pad(rand(0, 99999999), 8, '0', STR_PAD_LEFT);
        
        return $prefix . $suffix;
    }
}
