<?php

namespace App\Filament\Resources\WechatAccountResource\Pages;

use App\Filament\Resources\WechatAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListWechatAccounts extends ListRecords
{
    protected static string $resource = WechatAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('新增公众号'),
        ];
    }
}