<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class FocusImage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'image',
        'alt_text',
        'link_url',
        'link_target',
        'position',
        'sort_order',
        'is_active',
        'start_time',
        'end_time',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 获取显示位置选项
     */
    public static function getPositionOptions(): array
    {
        return [
            'home_banner' => '首页轮播',
            'home_featured' => '首页推荐', 
            'sidebar' => '侧边栏',
            'footer' => '页脚',
            'floating_window' => '首页飘窗', // 新增
        ];
    }

    /**
     * 获取首页飘窗图片
     */
    public static function getFloatingWindows(int $limit = 1)
    {
        return self::displayable()
            ->byPosition('floating_window')
            ->ordered()
            ->limit($limit)
            ->get();
    }

    /**
     * 获取链接打开方式选项
     */
    public static function getLinkTargetOptions(): array
    {
        return [
            '_self' => '当前窗口',
            '_blank' => '新窗口',
        ];
    }

    /**
     * 获取显示位置标签
     */
    public function getPositionLabel(): string
    {
        return self::getPositionOptions()[$this->position] ?? $this->position;
    }

    /**
     * 获取链接打开方式标签
     */
    public function getLinkTargetLabel(): string
    {
        return self::getLinkTargetOptions()[$this->link_target] ?? $this->link_target;
    }

    /**
     * 获取图片完整URL
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }
        
        return Storage::disk('public')->url($this->image);
    }

    /**
     * 检查是否在有效期内
     */
    public function isInValidPeriod(): bool
    {
        $now = now();
        
        // 检查开始时间
        if ($this->start_time && $this->start_time > $now) {
            return false;
        }
        
        // 检查结束时间
        if ($this->end_time && $this->end_time < $now) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否可以显示
     */
    public function canDisplay(): bool
    {
        return $this->is_active && $this->isInValidPeriod();
    }

    /**
     * 作用域：仅获取启用的焦点图
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    /**
     * 作用域：获取指定位置的焦点图
     */
    public function scopeByPosition($query, string $position)
    {
        return $query->where('position', $position);
    }

    /**
     * 作用域：获取有效期内的焦点图
     */
    public function scopeInValidPeriod($query)
    {
        $now = now();
        
        return $query->where(function ($q) use ($now) {
            $q->where(function ($subQ) use ($now) {
                $subQ->whereNull('start_time')
                    ->orWhere('start_time', '<=', $now);
            })
            ->where(function ($subQ) use ($now) {
                $subQ->whereNull('end_time')
                    ->orWhere('end_time', '>=', $now);
            });
        });
    }

    /**
     * 作用域：获取可显示的焦点图
     */
    public function scopeDisplayable($query)
    {
        return $query->active()->inValidPeriod();
    }

    /**
     * 获取首页轮播图
     */
    public static function getHomeBanners(int $limit = 5)
    {
        return self::displayable()
            ->byPosition('home_banner')
            ->ordered()
            ->limit($limit)
            ->get();
    }

    /**
     * 获取首页推荐图
     */
    public static function getHomeFeatured(int $limit = 3)
    {
        return self::displayable()
            ->byPosition('home_featured')
            ->ordered()
            ->limit($limit)
            ->get();
    }

    /**
     * 获取侧边栏图片
     */
    public static function getSidebarImages(int $limit = 2)
    {
        return self::displayable()
            ->byPosition('sidebar')
            ->ordered()
            ->limit($limit)
            ->get();
    }

    /**
     * 获取页脚图片
     */
    public static function getFooterImages(int $limit = 4)
    {
        return self::displayable()
            ->byPosition('footer')
            ->ordered()
            ->limit($limit)
            ->get();
    }
}