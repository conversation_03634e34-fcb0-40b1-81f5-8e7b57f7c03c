<?php

namespace App\Services;

use App\Models\SmsTemplate;
use Illuminate\Support\Facades\Log;
use Exception;

class SmsService
{
    private $apiKey;
    private $baseUrl = 'https://sms.yunpian.com/v2/sms/';

    public function __construct()
    {
        $this->apiKey = config('services.yunpian.api_key');
    }

    /**
     * 发送短信（支持模板代码）
     */
    public function sendSms($mobile, $templateIdOrCode, $data = [])
    {
        try {
            // 如果传入的是模板代码，查找对应模板
            if (!is_numeric($templateIdOrCode)) {
                $template = SmsTemplate::findByCode($templateIdOrCode);
                if (!$template) {
                    throw new Exception("短信模板不存在: {$templateIdOrCode}");
                }
                
                // 验证参数完整性
                $validation = $template->validateParams($data);
                if ($validation !== true) {
                    throw new Exception('缺少必需参数: ' . implode(', ', $validation));
                }
                
                $templateId = $template->yunpian_template_id;
            } else {
                $templateId = $templateIdOrCode;
            }

            // 构建tpl_value参数 - 云片网要求的格式
            $tplValue = '';
            if (!empty($data)) {
                $pairs = [];
                foreach ($data as $key => $value) {
                    // 确保变量名包含#号
                    $varName = strpos($key, '#') === false ? "#{$key}#" : $key;
                    $pairs[] = urlencode($varName) . '=' . urlencode($value);
                }
                $tplValue = implode('&', $pairs);
            }

            $postData = [
                'apikey' => $this->apiKey,
                'mobile' => $mobile,
                'tpl_id' => $templateId,
                'tpl_value' => $tplValue
            ];

            $response = $this->sendRequest('tpl_single_send.json', $postData);
            
            if ($response && isset($response['code']) && $response['code'] == 0) {
                Log::info('短信发送成功', ['mobile' => $mobile, 'template' => $templateIdOrCode]);
                return true;
            } else {
                $errorMsg = $response['msg'] ?? '未知错误';
                $errorCode = $response['code'] ?? 'unknown';
                Log::error('短信发送失败', [
                    'mobile' => $mobile, 
                    'error' => $errorMsg,
                    'error_code' => $errorCode,
                    'full_response' => $response,
                    'request_data' => $postData
                ]);
                return false;
            }
        } catch (Exception $e) {
            Log::error('短信发送异常', ['mobile' => $mobile, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 发送请求
     */
    private function sendRequest($endpoint, $data)
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        // 记录请求详情
        Log::info('SMS API请求', [
            'url' => $url,
            'data' => $data,
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'response' => $response
        ]);
        
        if ($curlError) {
            Log::error('CURL错误', ['error' => $curlError]);
            return null;
        }
        
        if ($httpCode == 200) {
            return json_decode($response, true);
        }
        
        Log::error('HTTP错误', ['http_code' => $httpCode, 'response' => $response]);
        return null;
    }

    /**
     * 验证手机号格式
     */
    public function validateMobile($mobile)
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile);
    }
}