<?php

namespace App\Filament\Resources\DatabaseBackupResource\Widgets;

use App\Models\DatabaseBackup;
use App\Services\DatabaseBackupService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BackupStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $backupService = app(DatabaseBackupService::class);
        $stats = $backupService->getBackupStats();

        return [
            Stat::make('总备份数', $stats['total_backups'])
                ->description('所有备份记录')
                ->descriptionIcon('heroicon-m-server-stack')
                ->color('primary'),
            
            Stat::make('成功备份', $stats['completed_backups'])
                ->description('已完成的备份')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            
            Stat::make('失败备份', $stats['failed_backups'])
                ->description('备份失败的记录')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
            
            Stat::make('总存储大小', $this->formatFileSize($stats['total_size']))
                ->description('所有备份文件大小')
                ->descriptionIcon('heroicon-m-archive-box')
                ->color('warning'),
        ];
    }

    private function formatFileSize(?int $bytes): string
    {
        if (!$bytes) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
