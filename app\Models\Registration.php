<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Registration extends Model
{
    use HasFactory;

    protected $fillable = [
        'activity_detail_id',
        'name',
        'phone',
        'organization',
        'grade',
        'gender',
        'source',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function activityDetail(): BelongsTo
    {
        return $this->belongsTo(ActivityDetail::class);
    }
} 