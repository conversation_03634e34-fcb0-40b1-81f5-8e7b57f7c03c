<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

class CompletePermissionInitSeeder extends Seeder
{
    /**
     * 完整初始化权限系统
     */
    public function run(): void
    {
        $this->command->info('🚀 开始完整初始化权限系统...');

        // 1. 清除现有权限缓存
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // 2. 创建基础角色
        $this->createRoles();

        // 3. 创建权限
        $this->createPermissions();

        // 4. 分配权限给角色
        $this->assignPermissionsToRoles();

        // 5. 创建管理员用户
        $this->createAdminUsers();

        // 6. 验证权限系统
        $this->validatePermissionSystem();

        $this->command->info('✅ 权限系统初始化完成！');
    }

    /**
     * 创建基础角色
     */
    private function createRoles()
    {
        $this->command->info('📋 创建基础角色...');

        $roles = [
            'super_admin' => '超级管理员',
            'admin' => '管理员',
            'editor' => '编辑',
            'user' => '普通用户'
        ];

        foreach ($roles as $name => $description) {
            $role = Role::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web'
            ]);
            
            $this->command->info("  ✅ 创建角色: {$name} ({$description})");
        }
    }

    /**
     * 创建权限
     */
    private function createPermissions()
    {
        $this->command->info('🔑 创建权限...');

        $resources = [
            'user' => '用户管理',
            'role' => '角色管理', 
            'permission' => '权限管理',
            'article' => '文章管理',
            'category' => '分类管理',
            'tag' => '标签管理',
            'comment' => '评论管理',
            'media_file' => '媒体文件',
            'activity' => '活动管理',
            'activity_detail' => '活动场次',
            'registration' => '报名管理',
            'friendship_link' => '友情链接',
            'friendship_link_category' => '友情链接分类',
            'backup_schedule' => '备份计划',
            'database_backup' => '数据库备份',
            'achievement_category' => '成就分类',
            'user_achievement' => '用户成就',
            'user_point' => '用户积分',
            'audit_log' => '审计日志'
        ];

        $actions = ['view_any', 'view', 'create', 'update', 'delete', 'restore', 'force_delete'];

        $permissionCount = 0;
        foreach ($resources as $resource => $description) {
            foreach ($actions as $action) {
                $permissionName = "{$action}_{$resource}";
                
                Permission::firstOrCreate([
                    'name' => $permissionName,
                    'guard_name' => 'web'
                ]);
                
                $permissionCount++;
            }
        }

        $this->command->info("  ✅ 创建了 {$permissionCount} 个权限");
    }

    /**
     * 分配权限给角色
     */
    private function assignPermissionsToRoles()
    {
        $this->command->info('🔗 分配权限给角色...');

        // 获取所有权限
        $allPermissions = Permission::all();

        // 超级管理员拥有所有权限
        $superAdmin = Role::where('name', 'super_admin')->first();
        $superAdmin->syncPermissions($allPermissions);
        $this->command->info("  ✅ super_admin 获得 {$allPermissions->count()} 个权限");

        // 管理员拥有大部分权限（除了角色和权限管理）
        $admin = Role::where('name', 'admin')->first();
        $adminPermissions = $allPermissions->filter(function ($permission) {
            return !str_contains($permission->name, 'role') && 
                   !str_contains($permission->name, 'permission');
        });
        $admin->syncPermissions($adminPermissions);
        $this->command->info("  ✅ admin 获得 {$adminPermissions->count()} 个权限");

        // 编辑拥有内容相关权限
        $editor = Role::where('name', 'editor')->first();
        $editorPermissions = $allPermissions->filter(function ($permission) {
            return str_contains($permission->name, 'article') ||
                   str_contains($permission->name, 'category') ||
                   str_contains($permission->name, 'tag') ||
                   str_contains($permission->name, 'comment') ||
                   str_contains($permission->name, 'media_file');
        });
        $editor->syncPermissions($editorPermissions);
        $this->command->info("  ✅ editor 获得 {$editorPermissions->count()} 个权限");

        // 普通用户只有查看权限
        $user = Role::where('name', 'user')->first();
        $userPermissions = $allPermissions->filter(function ($permission) {
            return str_contains($permission->name, 'view');
        });
        $user->syncPermissions($userPermissions);
        $this->command->info("  ✅ user 获得 {$userPermissions->count()} 个权限");
    }

    /**
     * 创建管理员用户
     */
    private function createAdminUsers()
    {
        $this->command->info('👨‍💼 创建管理员用户...');

        $admins = [
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => 'password',
                'role' => 'super_admin',
                'real_name' => '超级管理员',
                'region' => '系统管理',
                'school' => '管理中心'
            ],
            [
                'name' => 'Admin',
                'email' => '<EMAIL>', 
                'password' => 'password',
                'role' => 'admin',
                'real_name' => '管理员',
                'region' => '系统管理',
                'school' => '管理中心'
            ]
        ];

        foreach ($admins as $adminData) {
            $user = User::updateOrCreate(
                ['email' => $adminData['email']],
                [
                    'name' => $adminData['name'],
                    'password' => Hash::make($adminData['password']),
                    'is_active' => true,
                    'is_verified' => true,
                    'real_name' => $adminData['real_name'],
                    'region' => $adminData['region'],
                    'school' => $adminData['school'],
                    'phone' => '13800000000',
                ]
            );

            $user->assignRole($adminData['role']);
            
            $this->command->info("  ✅ 创建用户: {$adminData['email']} (角色: {$adminData['role']})");
        }
    }

    /**
     * 验证权限系统
     */
    private function validatePermissionSystem()
    {
        $this->command->info('🔍 验证权限系统...');

        // 统计数据
        $roleCount = Role::count();
        $permissionCount = Permission::count();
        $userCount = User::whereHas('roles')->count();

        $this->command->info("  角色数量: {$roleCount}");
        $this->command->info("  权限数量: {$permissionCount}");
        $this->command->info("  有角色的用户: {$userCount}");

        // 验证管理员权限
        $admin = User::where('email', '<EMAIL>')->first();
        if ($admin) {
            $hasRole = $admin->hasRole('super_admin');
            $canAccess = $admin->canAccessPanel(\Filament\Facades\Filament::getDefaultPanel());
            
            $this->command->info("  <EMAIL>:");
            $this->command->info("    有super_admin角色: " . ($hasRole ? '✅' : '❌'));
            $this->command->info("    可访问后台: " . ($canAccess ? '✅' : '❌'));
        }

        $this->command->info('');
        $this->command->info('🎯 登录信息:');
        $this->command->info('URL: http://127.0.0.1:8000/admin');
        $this->command->info('邮箱: <EMAIL>');
        $this->command->info('密码: password');
    }
}
