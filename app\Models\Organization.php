<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Organization extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'parent_id',
        'type',
        'description',
        'contact_person',
        'contact_phone',
        'contact_email',
        'address',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 获取上级组织
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'parent_id');
    }

    /**
     * 获取下级组织
     */
    public function children(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Organization::class, 'parent_id');
    }

    /**
     * 获取组织下的用户
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * 获取组织下的在职用户
     */
    public function activeUsers(): HasMany
    {
        return $this->users()->where('employment_status', 'active')->where('is_active', true);
    }

    /**
     * 获取所有下级组织（递归）
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * 获取组织类型选项
     */
    public static function getTypeOptions(): array
    {
        return [
            'department' => '部门',
            'division' => '科室',
            'team' => '小组',
            'company' => '公司',
            'branch' => '分支机构',
        ];
    }

    /**
     * 获取组织类型标签
     */
    public function getTypeLabel(): string
    {
        return self::getTypeOptions()[$this->type] ?? $this->type;
    }

    /**
     * 获取完整的组织路径
     */
    public function getFullPath(): string
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * 检查是否为根组织
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * 检查是否有子组织
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * 作用域：仅获取启用的组织
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * 作用域：获取根组织
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }
}