<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;
use Filament\Forms\Components\Concerns\HasPlaceholder;
use Filament\Forms\Components\Contracts\HasFileAttachments;
use Filament\Support\Concerns\HasExtraAlpineAttributes;
use Illuminate\Contracts\Support\Htmlable;

class WechatEditor extends Field implements HasFileAttachments
{
    use HasPlaceholder;
    use HasExtraAlpineAttributes;

    protected string $view = 'components.forms.wechat-editor';

    protected \Closure|bool $isDisabled = false;

    protected ?string $disk = null;

    protected ?string $directory = null;

    protected ?string $visibility = null;

    protected bool $shouldPreserveFilenames = false;

    protected ?\Closure $getUploadedFileNameForStorageUsing = null;

    protected ?\Closure $saveUploadedFileUsing = null;

    protected ?\Closure $deleteUploadedFileUsing = null;

    protected ?\Closure $reorderUploadedFilesUsing = null;

    protected function setUp(): void
    {
        parent::setUp();

        $this->default('');

        $this->afterStateHydrated(static function (WechatEditor $component, $state): void {
            $component->state($state ?? '');
        });

        $this->dehydrateStateUsing(static function ($state) {
            return $state;
        });
    }

    public function disabled(bool | \Closure $condition = true): static
    {
        $this->isDisabled = $this->evaluate($condition);

        return $this;
    }

    public function isDisabled(): bool
    {
        return $this->isDisabled || parent::isDisabled();
    }

    public function disk(string | \Closure | null $disk): static
    {
        $this->disk = $disk;

        return $this;
    }

    public function getDisk(): ?string
    {
        return $this->evaluate($this->disk) ?? config('filament.default_filesystem_disk');
    }

    public function directory(string | \Closure | null $directory): static
    {
        $this->directory = $directory;

        return $this;
    }

    public function getDirectory(): ?string
    {
        return $this->evaluate($this->directory);
    }

    public function visibility(string | \Closure | null $visibility): static
    {
        $this->visibility = $visibility;

        return $this;
    }

    public function getVisibility(): ?string
    {
        return $this->evaluate($this->visibility) ?? 'public';
    }

    public function preserveFilenames(bool | \Closure $condition = true): static
    {
        $this->shouldPreserveFilenames = $this->evaluate($condition);

        return $this;
    }

    public function shouldPreserveFilenames(): bool
    {
        return $this->shouldPreserveFilenames;
    }

    public function getUploadedFileNameForStorageUsing(?\Closure $callback): static
    {
        $this->getUploadedFileNameForStorageUsing = $callback;

        return $this;
    }

    public function saveUploadedFileUsing(?\Closure $callback): static
    {
        $this->saveUploadedFileUsing = $callback;

        return $this;
    }

    public function deleteUploadedFileUsing(?\Closure $callback): static
    {
        $this->deleteUploadedFileUsing = $callback;

        return $this;
    }

    public function reorderUploadedFilesUsing(?\Closure $callback): static
    {
        $this->reorderUploadedFilesUsing = $callback;

        return $this;
    }

    public function getChildComponentContainer($key = null): \Filament\Forms\ComponentContainer
    {
        return \Filament\Forms\ComponentContainer::make(\Filament\Facades\Filament::getCurrentPanel())
            ->parentComponent($this)
            ->statePath($key ?? $this->getStatePath());
    }

    public function getUploadedFileNameForStorage(\Illuminate\Http\UploadedFile $file): string
    {
        if ($this->getUploadedFileNameForStorageUsing) {
            return $this->evaluate($this->getUploadedFileNameForStorageUsing, [
                'file' => $file,
            ]);
        }

        if ($this->shouldPreserveFilenames()) {
            return $file->getClientOriginalName();
        }

        return $file->hashName();
    }

    public function saveUploadedFile(\Illuminate\Http\UploadedFile $file): ?string
    {
        if ($this->saveUploadedFileUsing) {
            return $this->evaluate($this->saveUploadedFileUsing, [
                'file' => $file,
            ]);
        }

        $filename = $this->getUploadedFileNameForStorage($file);

        $upload = $file->storeAs(
            $this->getDirectory(),
            $filename,
            $this->getDisk(),
        );

        return $upload ?: null;
    }

    public function deleteUploadedFile(string $file): bool
    {
        if ($this->deleteUploadedFileUsing) {
            return $this->evaluate($this->deleteUploadedFileUsing, [
                'file' => $file,
            ]);
        }

        if (! \Illuminate\Support\Facades\Storage::disk($this->getDisk())->exists($file)) {
            return false;
        }

        return \Illuminate\Support\Facades\Storage::disk($this->getDisk())->delete($file);
    }

    public function reorderUploadedFiles(array $files): array
    {
        if ($this->reorderUploadedFilesUsing) {
            return $this->evaluate($this->reorderUploadedFilesUsing, [
                'files' => $files,
            ]);
        }

        return $files;
    }

    public function saveUploadedFileAttachment(\Illuminate\Http\UploadedFile $attachment): ?string
    {
        return $this->saveUploadedFile($attachment);
    }

    public function getViewData(): array
    {
        return array_merge(parent::getViewData(), [
            'content' => $this->getState() ?? '',
            'name' => $this->getName(),
            'id' => $this->getId(),
            'disabled' => $this->isDisabled(),
            'required' => $this->isRequired(),
        ]);
    }

    /**
     * @return array<string, mixed>
     */
    protected function resolveDefaultClosureDependencyForEvaluationByName(string $parameterName): array
    {
        return match ($parameterName) {
            'state' => [$this->getState()],
            default => parent::resolveDefaultClosureDependencyForEvaluationByName($parameterName),
        };
    }

    /**
     * @return array<string, mixed>
     */
    protected function resolveDefaultClosureDependencyForEvaluationByType(string $parameterType): array
    {
        $dependencies = parent::resolveDefaultClosureDependencyForEvaluationByType($parameterType);

        if (is_subclass_of($parameterType, \Illuminate\Http\UploadedFile::class)) {
            return [app($parameterType)];
        }

        return $dependencies;
    }
}