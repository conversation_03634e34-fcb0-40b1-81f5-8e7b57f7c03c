<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_behaviors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->string('action')->comment('行为类型：login, browse, video_view');
            $table->string('target_type')->nullable()->comment('目标类型：post, video, page');
            $table->string('target_id')->nullable()->comment('目标ID');
            $table->string('url')->nullable()->comment('访问的URL');
            $table->integer('duration')->nullable()->comment('停留时长（秒）');
            $table->string('ip_address')->nullable()->comment('IP地址');
            $table->text('user_agent')->nullable()->comment('用户代理');
            $table->date('behavior_date')->comment('行为发生日期');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'behavior_date']);
            $table->index(['action', 'behavior_date']);
            $table->index(['target_type', 'target_id']);
            $table->index('behavior_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_behaviors');
    }
};
