<?php

namespace App\Jobs;

use App\Models\WechatAccount;
use App\Services\WechatMenuService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncWechatMenuJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected WechatAccount $account;
    protected string $action;

    /**
     * Create a new job instance.
     */
    public function __construct(WechatAccount $account, string $action = 'create')
    {
        $this->account = $account;
        $this->action = $action; // create, delete, get
    }

    /**
     * Execute the job.
     */
    public function handle(WechatMenuService $menuService): void
    {
        try {
            Log::info('开始同步微信菜单', [
                'account_id' => $this->account->id,
                'account_name' => $this->account->name,
                'action' => $this->action,
            ]);

            $result = false;
            
            switch ($this->action) {
                case 'create':
                    $result = $menuService->createMenu($this->account->id);
                    break;
                case 'delete':
                    $result = $menuService->deleteMenu($this->account->id);
                    break;
                case 'get':
                    $result = $menuService->getMenu($this->account->id);
                    break;
            }

            if ($result) {
                Log::info('微信菜单同步成功', [
                    'account_id' => $this->account->id,
                    'action' => $this->action,
                ]);
            } else {
                Log::warning('微信菜单同步失败', [
                    'account_id' => $this->account->id,
                    'action' => $this->action,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('微信菜单同步异常', [
                'account_id' => $this->account->id,
                'action' => $this->action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('微信菜单同步任务失败', [
            'account_id' => $this->account->id,
            'action' => $this->action,
            'error' => $exception->getMessage(),
        ]);
    }
}