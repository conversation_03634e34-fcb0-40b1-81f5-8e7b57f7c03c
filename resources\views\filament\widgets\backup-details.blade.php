<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            备份详情
        </x-slot>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">基本信息</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">备份名称:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100 break-all">{{ $backup->name }}</span>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">备份类型:</span>
                        <div>
                            <x-filament::badge :color="$backup->backup_type_color">
                                {{ \App\Models\DatabaseBackup::getBackupTypes()[$backup->backup_type] ?? $backup->backup_type }}
                            </x-filament::badge>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">状态:</span>
                        <div>
                            <x-filament::badge :color="$backup->status_color">
                                {{ \App\Models\DatabaseBackup::getStatuses()[$backup->status] ?? $backup->status }}
                            </x-filament::badge>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">创建者:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            {{ $backup->creator?->name ?? '系统' }}
                        </span>
                    </div>

                    @if($backup->description)
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">描述:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100 break-words">{{ $backup->description }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- 文件信息 -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">文件信息</h3>

                <div class="space-y-3">
                    @if($backup->file_path)
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">文件路径:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100 font-mono break-all bg-gray-50 dark:bg-gray-800 p-2 rounded">{{ $backup->file_path }}</span>
                    </div>
                    @endif

                    @if($backup->file_size)
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">文件大小:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">{{ $backup->formatted_file_size }}</span>
                    </div>
                    @endif

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">文件存在:</span>
                        <div>
                            @if($backup->fileExists())
                                <x-filament::badge color="success">是</x-filament::badge>
                            @else
                                <x-filament::badge color="danger">否</x-filament::badge>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- 时间信息 -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">时间信息</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            {{ $backup->created_at?->format('Y-m-d H:i:s') }}
                        </span>
                    </div>

                    @if($backup->backup_started_at)
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">开始时间:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            {{ $backup->backup_started_at->format('Y-m-d H:i:s') }}
                        </span>
                    </div>
                    @endif

                    @if($backup->backup_completed_at)
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">完成时间:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            {{ $backup->backup_completed_at->format('Y-m-d H:i:s') }}
                        </span>
                    </div>
                    @endif

                    @if($backup->duration)
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">耗时:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">{{ $backup->duration }}</span>
                    </div>
                    @endif
                </div>
            </div>

        </div>

        <!-- 错误信息 -->
        @if($backup->error_message)
        <div class="mt-6 space-y-4">
            <h3 class="text-lg font-medium text-red-600 dark:text-red-400">错误信息</h3>

            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <p class="text-sm text-red-700 dark:text-red-300 font-mono break-words whitespace-pre-wrap">{{ $backup->error_message }}</p>
            </div>
        </div>
        @endif

        <!-- 备份信息 -->
        @if($backup->schedule)
        <div class="mt-6 space-y-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">备份信息</h3>

            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="grid grid-cols-1 gap-2">
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">备份计划:</span>
                        <span class="text-sm text-blue-900 dark:text-blue-100">{{ $backup->schedule->name }}</span>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">计划描述:</span>
                        <span class="text-sm text-blue-900 dark:text-blue-100">{{ $backup->schedule->description ?? '无' }}</span>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">执行频率:</span>
                        <span class="text-sm text-blue-900 dark:text-blue-100">{{ $backup->schedule->frequency_description }}</span>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </x-filament::section>
</x-filament-widgets::widget>
