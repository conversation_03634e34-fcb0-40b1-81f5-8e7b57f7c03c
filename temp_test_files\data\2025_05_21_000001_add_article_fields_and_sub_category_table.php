<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->string('short_title')->nullable()->comment('简略标题');
            $table->string('custom_flags', 32)->nullable()->comment('自定义属性标记');
            $table->integer('weight')->default(0)->comment('权重');
            $table->string('thumb_image')->nullable()->comment('缩略图');
            $table->string('source')->nullable()->comment('文章来源');
            $table->string('author')->nullable()->comment('作者');
            $table->string('title_color', 16)->nullable()->comment('标题颜色');
            $table->boolean('allow_comment')->default(true)->comment('允许评论');
            $table->string('view_permission', 32)->default('public')->comment('阅读权限');
            $table->json('extra_options')->nullable()->comment('附加选项');
        });

        Schema::create('article_sub_category', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('article_id');
            $table->unsignedBigInteger('category_id');
            $table->timestamps();

            $table->unique(['article_id', 'category_id']);
            $table->foreign('article_id')->references('id')->on('articles')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->dropColumn([
                'short_title', 'custom_flags', 'weight', 'thumb_image', 'source',
                'author', 'title_color', 'allow_comment', 'view_permission', 'extra_options'
            ]);
        });
        Schema::dropIfExists('article_sub_category');
    }
}; 