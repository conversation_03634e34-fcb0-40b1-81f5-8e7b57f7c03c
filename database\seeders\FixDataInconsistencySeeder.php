<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ActivityDetail;
use App\Models\Registration;

class FixDataInconsistencySeeder extends Seeder
{
    /**
     * 修复报名系统数据不一致问题
     */
    public function run(): void
    {
        $this->command->info('🔧 开始修复数据不一致问题...');
        
        $fixedCount = 0;
        
        ActivityDetail::with(['activity', 'registrations'])->get()->each(function ($detail) use (&$fixedCount) {
            $actualValidCount = $detail->registrations->where('status', true)->count();
            $storedCount = $detail->current_count;
            
            if ($actualValidCount !== $storedCount) {
                $this->command->info("修复：{$detail->activity->title} - {$detail->theme}");
                $this->command->info("  原存储计数：{$storedCount}");
                $this->command->info("  实际有效报名：{$actualValidCount}");
                
                // 更新存储的计数
                $detail->update(['current_count' => $actualValidCount]);
                
                $this->command->info("  ✅ 已修复为：{$actualValidCount}");
                $fixedCount++;
            }
        });
        
        if ($fixedCount > 0) {
            $this->command->info("🎉 共修复了 {$fixedCount} 个数据不一致问题");
        } else {
            $this->command->info("✅ 没有发现数据不一致问题");
        }
        
        // 重新验证
        $this->validateAfterFix();
    }
    
    /**
     * 修复后重新验证
     */
    private function validateAfterFix()
    {
        $this->command->info('');
        $this->command->info('🔍 修复后重新验证...');
        
        $inconsistencies = [];
        
        ActivityDetail::with(['activity', 'registrations'])->get()->each(function ($detail) use (&$inconsistencies) {
            $actualValidCount = $detail->registrations->where('status', true)->count();
            $storedCount = $detail->current_count;
            
            if ($actualValidCount !== $storedCount) {
                $inconsistencies[] = [
                    'activity' => $detail->activity->title,
                    'detail' => $detail->theme,
                    'actual' => $actualValidCount,
                    'stored' => $storedCount,
                    'difference' => $actualValidCount - $storedCount
                ];
            }
        });
        
        if (empty($inconsistencies)) {
            $this->command->info('✅ 所有数据现在都是一致的！');
        } else {
            $this->command->error('❌ 仍然存在数据不一致：');
            foreach ($inconsistencies as $issue) {
                $this->command->error("  - {$issue['activity']} - {$issue['detail']}：实际 {$issue['actual']}，存储 {$issue['stored']}");
            }
        }
        
        // 显示最终统计
        $this->showFinalStats();
    }
    
    /**
     * 显示最终统计
     */
    private function showFinalStats()
    {
        $this->command->info('');
        $this->command->info('📊 最终统计数据：');
        
        $totalActivities = \App\Models\Activity::count();
        $totalDetails = ActivityDetail::count();
        $totalRegistrations = Registration::count();
        $validRegistrations = Registration::where('status', true)->count();
        $invalidRegistrations = Registration::where('status', false)->count();
        
        $this->command->info("活动总数：{$totalActivities}");
        $this->command->info("场次总数：{$totalDetails}");
        $this->command->info("报名总数：{$totalRegistrations}");
        $this->command->info("有效报名：{$validRegistrations}");
        $this->command->info("无效报名：{$invalidRegistrations}");
        
        // 计算报名率统计
        $fullActivities = ActivityDetail::whereRaw('current_count >= quota')->count();
        $avgRate = ActivityDetail::selectRaw('AVG(current_count / quota * 100) as avg_rate')->first()->avg_rate ?? 0;
        
        $this->command->info("满员场次：{$fullActivities}");
        $this->command->info("平均报名率：" . round($avgRate, 2) . "%");
        
        // 按活动显示详细统计
        $this->command->info('');
        $this->command->info('📋 各场次详细统计：');
        
        ActivityDetail::with('activity')
            ->orderBy('activity_id')
            ->get()
            ->each(function ($detail) {
                $rate = $detail->quota > 0 ? round(($detail->current_count / $detail->quota) * 100, 1) : 0;
                $status = $detail->current_count >= $detail->quota ? '🔴 满员' : '🟢 可报名';
                
                $this->command->info("  {$status} {$detail->activity->title} - {$detail->theme}");
                $this->command->info("    报名：{$detail->current_count}/{$detail->quota} ({$rate}%)");
            });
    }
}
