@props([
    'content' => '',
    'name' => 'content',
    'id' => null,
    'disabled' => false,
    'required' => false,
])

@php
    $id = $id ?? $name . '_' . uniqid();
    $config = config('wechat-editor');
@endphp

<!-- FontAwesome Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<div class="wechat-editor-wrapper">
    <div class="wechat-editor-toolbar">
        <div class="toolbar-section">
            <button type="button" class="toolbar-btn" data-command="bold" title="加粗">
                <span style="font-weight: bold;">B</span>
            </button>
            <button type="button" class="toolbar-btn" data-command="italic" title="斜体">
                <span style="font-style: italic;">I</span>
            </button>
            <button type="button" class="toolbar-btn" data-command="underline" title="下划线">
                <span style="text-decoration: underline;">U</span>
            </button>
        </div>
        
        <div class="toolbar-divider"></div>
        
        <div class="toolbar-section">
            <select class="toolbar-select" data-command="fontSize">
                <option value="12px">12px</option>
                <option value="14px">14px</option>
                <option value="16px" selected>16px</option>
                <option value="18px">18px</option>
                <option value="20px">20px</option>
                <option value="24px">24px</option>
            </select>
            
            <input type="color" class="toolbar-color" data-command="foreColor" value="#333333" title="文字颜色">
            <input type="color" class="toolbar-color" data-command="backColor" value="#ffffff" title="背景颜色">
        </div>
        
        <div class="toolbar-divider"></div>
        
        <div class="toolbar-section">
            <button type="button" class="toolbar-btn" data-command="justifyLeft" title="左对齐">
                <i class="fas fa-align-left"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="justifyCenter" title="居中">
                <i class="fas fa-align-center"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="justifyRight" title="右对齐">
                <i class="fas fa-align-right"></i>
            </button>
        </div>
        
        <div class="toolbar-divider"></div>
        
        <div class="toolbar-section">
            <button type="button" class="toolbar-btn" data-command="insertUnorderedList" title="无序列表">
                <i class="fas fa-list-ul"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="insertOrderedList" title="有序列表">
                <i class="fas fa-list-ol"></i>
            </button>
        </div>
        
        <div class="toolbar-divider"></div>
        
        <div class="toolbar-section">
            <button type="button" class="toolbar-btn" data-command="createLink" title="插入链接">
                <i class="fas fa-link"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="insertImage" title="插入图片">
                <i class="fas fa-image"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="insertVideo" title="插入视频">
                <i class="fas fa-video"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="insertTable" title="插入表格">
                <i class="fas fa-table"></i>
            </button>
        </div>
        
        <div class="toolbar-divider"></div>
        
        <div class="toolbar-section">
            <button type="button" class="toolbar-btn" data-command="undo" title="撤销">
                <i class="fas fa-undo"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="redo" title="重做">
                <i class="fas fa-redo"></i>
            </button>
        </div>
        
        <div class="toolbar-divider"></div>
        
        <div class="toolbar-section">
            <button type="button" class="toolbar-btn" data-command="viewSource" title="查看源码">
                <i class="fas fa-code"></i>
            </button>
            <button type="button" class="toolbar-btn" data-command="fullscreen" title="全屏">
                <i class="fas fa-expand"></i>
            </button>
        </div>
    </div>
    
    <div class="wechat-editor-content" 
         id="{{ $id }}"
         contenteditable="true"
         @if($disabled) contenteditable="false" @endif
         data-name="{{ $name }}"
         style="min-height: 400px; padding: 20px; border: 1px solid #e5e5e5; border-top: none; font-family: {{ $config['editor_styles']['font_family'] }}; font-size: {{ $config['editor_styles']['font_size'] }}; line-height: {{ $config['editor_styles']['line_height'] }}; color: {{ $config['editor_styles']['color'] }}; background-color: {{ $config['editor_styles']['background_color'] }};">
        {!! $content !!}
    </div>
    
    <input type="hidden" name="{{ $name }}" id="{{ $name }}_input" value="{{ $content }}" @if($required) required @endif>
</div>

<!-- 图片上传模态框 -->
<div id="imageUploadModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>插入图片</h3>
            <button type="button" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div class="upload-tabs">
                <button type="button" class="tab-btn active" data-tab="upload">上传图片</button>
                <button type="button" class="tab-btn" data-tab="url">网络图片</button>
            </div>
            
            <div class="tab-content" id="upload-tab">
                <div class="upload-area">
                    <input type="file" id="imageFileInput" accept="image/*" style="display: none;">
                    <div class="upload-dropzone" onclick="document.getElementById('imageFileInput').click();">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>点击上传图片或拖拽图片到此处</p>
                        <p class="upload-tips">支持 JPG、PNG、WebP 格式，最大 5MB</p>
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="url-tab" style="display: none;">
                <input type="url" id="imageUrlInput" placeholder="请输入图片网址" class="form-input">
                <button type="button" id="previewImageBtn" class="btn btn-secondary">预览</button>
                <div id="imagePreview" style="margin-top: 10px;"></div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeImageModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="insertImage()">插入</button>
        </div>
    </div>
</div>

<!-- 视频上传模态框 -->
<div id="videoUploadModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>插入视频</h3>
            <button type="button" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div class="upload-tabs">
                <button type="button" class="tab-btn active" data-tab="video-upload">上传视频</button>
                <button type="button" class="tab-btn" data-tab="video-url">网络视频</button>
            </div>
            
            <div class="tab-content" id="video-upload-tab">
                <div class="upload-area">
                    <input type="file" id="videoFileInput" accept="video/*" style="display: none;">
                    <div class="upload-dropzone" onclick="document.getElementById('videoFileInput').click();">
                        <i class="fas fa-video"></i>
                        <p>点击上传视频或拖拽视频到此处</p>
                        <p class="upload-tips">支持 MP4、WebM、AVI 格式，最大 50MB</p>
                    </div>
                    <div id="videoUploadProgress" style="display: none; margin-top: 10px;">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%;"></div>
                        </div>
                        <p class="progress-text">上传中... 0%</p>
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="video-url-tab" style="display: none;">
                <input type="url" id="videoUrlInput" placeholder="请输入视频网址" class="form-input">
                <button type="button" id="previewVideoBtn" class="btn btn-secondary">预览</button>
                <div id="videoPreview" style="margin-top: 10px;"></div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeVideoModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="insertVideoFromModal()">插入</button>
        </div>
    </div>
</div>

<style>
.wechat-editor-wrapper {
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
}

.wechat-editor-toolbar {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    flex-wrap: wrap;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 4px;
}

.toolbar-divider {
    width: 1px;
    height: 24px;
    background: linear-gradient(to bottom, transparent 0%, #dee2e6 20%, #dee2e6 80%, transparent 100%);
    margin: 0 6px;
}

.toolbar-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #495057;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.toolbar-btn i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-size: 14px;
    line-height: 1;
    display: inline-block !important;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
}

.toolbar-btn:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
    color: #1976d2;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.toolbar-btn.active {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border-color: #1976d2;
    box-shadow: 0 3px 8px rgba(33, 150, 243, 0.4);
    transform: translateY(-1px);
}

.toolbar-select {
    height: 36px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 30px;
}

.toolbar-select:hover {
    border-color: #2196f3;
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
    transform: translateY(-1px);
}

.toolbar-select:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.toolbar-color {
    width: 36px;
    height: 36px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    padding: 3px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.toolbar-color:hover {
    border-color: #2196f3;
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
}

.wechat-editor-content {
    outline: none;
    overflow-y: auto;
    max-height: 600px;
}

.wechat-editor-content:focus {
    box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e5e5;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e5e5e5;
    background: #f8f9fa;
}

.upload-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

.tab-btn {
    padding: 12px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.upload-dropzone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
}

.upload-dropzone:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.upload-dropzone i {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 16px;
}

.upload-tips {
    color: #666;
    font-size: 14px;
    margin-top: 8px;
}

.form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 12px;
}

.btn {
    padding: 8px 16px;
    border: 1px solid;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* 拖拽效果 */
.upload-dropzone.dragover {
    border-color: #007bff;
    background: #e3f2fd;
    transform: scale(1.02);
}

.upload-dropzone.dragover i {
    color: #007bff;
    transform: scale(1.1);
}

.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const editor = document.getElementById('{{ $id }}');
    const hiddenInput = document.getElementById('{{ $name }}_input');
    
    // 监听内容变化
    editor.addEventListener('input', function() {
        hiddenInput.value = editor.innerHTML;
    });
    
    // 工具栏按钮事件
    document.querySelectorAll('.toolbar-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const command = this.dataset.command;
            
            if (command === 'insertImage') {
                openImageModal();
                return;
            }
            
            if (command === 'insertVideo') {
                insertVideo();
                return;
            }
            
            if (command === 'createLink') {
                const url = prompt('请输入链接地址:');
                if (url) {
                    document.execCommand(command, false, url);
                }
                return;
            }
            
            if (command === 'viewSource') {
                toggleSourceView();
                return;
            }
            
            if (command === 'fullscreen') {
                toggleFullscreen();
                return;
            }
            
            document.execCommand(command, false, null);
            updateToolbarState();
        });
    });
    
    // 字体大小选择
    document.querySelector('.toolbar-select[data-command="fontSize"]').addEventListener('change', function() {
        document.execCommand('fontSize', false, '7');
        const fontElements = document.querySelectorAll('font[size="7"]');
        fontElements.forEach(el => {
            el.removeAttribute('size');
            el.style.fontSize = this.value;
        });
    });
    
    // 颜色选择
    document.querySelectorAll('.toolbar-color').forEach(colorInput => {
        colorInput.addEventListener('change', function() {
            document.execCommand(this.dataset.command, false, this.value);
        });
    });
    
    // 更新工具栏状态
    function updateToolbarState() {
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            const command = btn.dataset.command;
            if (document.queryCommandState(command)) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
    
    editor.addEventListener('selectionchange', updateToolbarState);
    editor.addEventListener('keyup', updateToolbarState);
    editor.addEventListener('mouseup', updateToolbarState);
});

// 图片上传相关函数
function openImageModal() {
    document.getElementById('imageUploadModal').style.display = 'flex';
}

function closeImageModal() {
    document.getElementById('imageUploadModal').style.display = 'none';
    document.getElementById('imageFileInput').value = '';
    document.getElementById('imageUrlInput').value = '';
    document.getElementById('imagePreview').innerHTML = '';
}

function insertImage() {
    const activeTab = document.querySelector('.tab-btn.active').dataset.tab;
    let imageUrl = '';
    
    if (activeTab === 'upload') {
        const fileInput = document.getElementById('imageFileInput');
        if (fileInput.files.length > 0) {
            // 这里需要上传文件到服务器
            uploadImageFile(fileInput.files[0]);
            return;
        }
    } else {
        imageUrl = document.getElementById('imageUrlInput').value;
    }
    
    if (imageUrl) {
        document.execCommand('insertImage', false, imageUrl);
        closeImageModal();
    }
}

function uploadImageFile(file) {
    const formData = new FormData();
    formData.append('upload', file);
    
    fetch('/ckeditor/upload/image', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.url) {
            document.execCommand('insertImage', false, data.url);
            closeImageModal();
        } else {
            alert('图片上传失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('图片上传失败');
    });
}

// 标签页切换
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const tab = this.dataset.tab;
        
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        
        // 切换内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.style.display = 'none';
        });
        document.getElementById(tab + '-tab').style.display = 'block';
    });
});

// 预览网络图片
document.getElementById('previewImageBtn').addEventListener('click', function() {
    const url = document.getElementById('imageUrlInput').value;
    if (url) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = `<img src="${url}" style="max-width: 100%; max-height: 200px;" onerror="this.style.display='none'; this.nextSibling.style.display='block';" /><p style="display: none; color: red;">图片加载失败，请检查网址是否正确</p>`;
    }
});

// 预览网络视频
document.getElementById('previewVideoBtn').addEventListener('click', function() {
    const url = document.getElementById('videoUrlInput').value;
    if (url) {
        const preview = document.getElementById('videoPreview');
        preview.innerHTML = `<video controls style="max-width: 100%; max-height: 200px;" onerror="this.style.display='none'; this.nextSibling.style.display='block';">
            <source src="${url}" type="video/mp4">
            <source src="${url}" type="video/webm">
            您的浏览器不支持视频播放。
        </video><p style="display: none; color: red;">视频加载失败，请检查网址是否正确</p>`;
    }
});

// 模态框关闭事件
document.querySelectorAll('.modal-close').forEach(closeBtn => {
    closeBtn.addEventListener('click', function() {
        const modal = this.closest('.modal');
        if (modal.id === 'imageUploadModal') {
            closeImageModal();
        } else if (modal.id === 'videoUploadModal') {
            closeVideoModal();
        }
    });
});

// 点击模态框背景关闭
document.querySelectorAll('.modal').forEach(modal => {
    modal.addEventListener('click', function(e) {
        if (e.target === this) {
            if (this.id === 'imageUploadModal') {
                closeImageModal();
            } else if (this.id === 'videoUploadModal') {
                closeVideoModal();
            }
        }
    });
});

// 文件拖拽上传
document.querySelectorAll('.upload-dropzone').forEach(dropzone => {
    dropzone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    dropzone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    dropzone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (this.closest('#imageUploadModal')) {
                if (file.type.startsWith('image/')) {
                    document.getElementById('imageFileInput').files = files;
                    uploadImageFile(file);
                } else {
                    alert('请选择图片文件');
                }
            } else if (this.closest('#videoUploadModal')) {
                if (file.type.startsWith('video/')) {
                    document.getElementById('videoFileInput').files = files;
                    // 可以选择立即上传或等待用户点击插入按钮
                } else {
                    alert('请选择视频文件');
                }
            }
        }
    });
});

// 插入视频
function insertVideo() {
    openVideoModal();
}

// 视频上传相关函数
function openVideoModal() {
    document.getElementById('videoUploadModal').style.display = 'flex';
}

function closeVideoModal() {
    document.getElementById('videoUploadModal').style.display = 'none';
    document.getElementById('videoFileInput').value = '';
    document.getElementById('videoUrlInput').value = '';
    document.getElementById('videoPreview').innerHTML = '';
    document.getElementById('videoUploadProgress').style.display = 'none';
}

function insertVideoFromModal() {
    const activeTab = document.querySelector('.upload-tabs .tab-btn.active').dataset.tab;
    let videoUrl = '';
    
    if (activeTab === 'video-upload') {
        const fileInput = document.getElementById('videoFileInput');
        if (fileInput.files.length > 0) {
            // 上传文件到服务器
            uploadVideoFile(fileInput.files[0]);
            return;
        } else {
            alert('请选择要上传的视频文件');
            return;
        }
    } else {
        videoUrl = document.getElementById('videoUrlInput').value;
    }
    
    if (videoUrl) {
        insertVideoElement(videoUrl);
        closeVideoModal();
    } else {
        alert('请输入视频网址');
    }
}

function insertVideoElement(videoUrl) {
    const videoHtml = `<video controls style="max-width: 100%; height: auto; margin: 10px 0;">
        <source src="${videoUrl}" type="video/mp4">
        <source src="${videoUrl}" type="video/webm">
        您的浏览器不支持视频播放。
    </video>`;
    
    // 获取当前选择或光标位置
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        const videoElement = document.createElement('div');
        videoElement.innerHTML = videoHtml;
        range.insertNode(videoElement.firstChild);
        
        // 更新隐藏输入框
        const editor = document.getElementById('{{ $id }}');
        const hiddenInput = document.getElementById('{{ $name }}_input');
        hiddenInput.value = editor.innerHTML;
    }
}

function uploadVideoFile(file) {
    // 检查文件大小（50MB限制）
    if (file.size > 50 * 1024 * 1024) {
        alert('视频文件大小不能超过50MB');
        return;
    }
    
    const formData = new FormData();
    formData.append('upload', file);
    
    // 显示上传进度
    const progressDiv = document.getElementById('videoUploadProgress');
    const progressFill = progressDiv.querySelector('.progress-fill');
    const progressText = progressDiv.querySelector('.progress-text');
    progressDiv.style.display = 'block';
    
    const xhr = new XMLHttpRequest();
    
    // 上传进度监听
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressFill.style.width = percentComplete + '%';
            progressText.textContent = `上传中... ${Math.round(percentComplete)}%`;
        }
    });
    
    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                const data = JSON.parse(xhr.responseText);
                if (data.url) {
                    insertVideoElement(data.url);
                    closeVideoModal();
                } else {
                    alert('视频上传失败：' + (data.error || '未知错误'));
                }
            } catch (e) {
                alert('视频上传失败：服务器响应格式错误');
            }
        } else {
            alert('视频上传失败：HTTP ' + xhr.status);
        }
        progressDiv.style.display = 'none';
    };
    
    xhr.onerror = function() {
        alert('视频上传失败：网络错误');
        progressDiv.style.display = 'none';
    };
    
    xhr.open('POST', '/ckeditor/upload/video');
    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    xhr.send(formData);
}

// 源码查看
let isSourceView = false;
function toggleSourceView() {
    const editor = document.getElementById('{{ $id }}');
    if (isSourceView) {
        editor.innerHTML = editor.textContent;
        editor.contentEditable = true;
        isSourceView = false;
    } else {
        editor.textContent = editor.innerHTML;
        editor.contentEditable = false;
        isSourceView = true;
    }
}

// 全屏模式
function toggleFullscreen() {
    const wrapper = document.querySelector('.wechat-editor-wrapper');
    if (wrapper.classList.contains('fullscreen')) {
        wrapper.classList.remove('fullscreen');
        document.body.style.overflow = '';
    } else {
        wrapper.classList.add('fullscreen');
        document.body.style.overflow = 'hidden';
    }
}

// 全屏样式
const style = document.createElement('style');
style.textContent = `
.wechat-editor-wrapper.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
}

.wechat-editor-wrapper.fullscreen .wechat-editor-content {
    height: calc(100vh - 60px);
    max-height: none;
}
`;
document.head.appendChild(style);
</script>