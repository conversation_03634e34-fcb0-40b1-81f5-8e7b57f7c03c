<?php

namespace App\Filament\Resources\PointCenterResource\Pages;

use App\Filament\Resources\PointCenterResource;
use App\Filament\Resources\PointCenterResource\Widgets\PointCenterStatsWidget;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class ListPointCenter extends ListRecords
{
    protected static string $resource = PointCenterResource::class;

    protected function getHeaderWidgets(): array
    {
        return [
            PointCenterStatsWidget::class,
        ];
    }

    protected function getTableQuery(): Builder
    {
        // 获取地区聚合数据
        $regionData = collect(DB::select("
            SELECT
                region,
                COUNT(DISTINCT users.id) as user_count,
                COALESCE(SUM(user_points.points), 0) as total_points,
                COALESCE(SUM(CASE WHEN YEAR(user_points.earned_date) = YEAR(NOW()) AND MONTH(user_points.earned_date) = MONTH(NOW()) THEN user_points.points ELSE 0 END), 0) as month_points,
                COALESCE(SUM(CASE WHEN YEAR(user_points.earned_date) = YEAR(NOW()) AND QUARTER(user_points.earned_date) = QUARTER(NOW()) THEN user_points.points ELSE 0 END), 0) as quarter_points,
                COALESCE(SUM(CASE WHEN YEAR(user_points.earned_date) = YEAR(NOW()) THEN user_points.points ELSE 0 END), 0) as year_points
            FROM users
            LEFT JOIN user_points ON users.id = user_points.user_id
            WHERE users.is_verified = 1 AND users.region IS NOT NULL
            GROUP BY region
            ORDER BY total_points DESC
        "))->map(function ($item, $index) {
            // 创建一个假的User模型实例来满足Filament的要求
            $user = new \App\Models\User();
            $user->id = $index + 1;
            $user->rank = $index + 1;
            $user->region = $item->region;
            $user->user_count = $item->user_count;
            $user->total_points = $item->total_points;
            $user->month_points = $item->month_points;
            $user->quarter_points = $item->quarter_points;
            $user->year_points = $item->year_points;

            // 设置exists为true，这样Filament就不会尝试保存它
            $user->exists = true;

            return $user;
        });

        // 将数据存储到会话中，以便在分页时使用
        session(['point_center_data' => $regionData]);

        // 返回一个基于User模型的空查询，但我们会重写getTableRecords方法
        return static::getResource()::getEloquentQuery();
    }

    public function getTableRecords(): \Illuminate\Contracts\Pagination\Paginator
    {
        $data = session('point_center_data', collect());

        // 手动分页
        $perPage = max(1, (int) ($this->getTableRecordsPerPage() ?? 10));
        $currentPage = max(1, (int) request()->get('page', 1));
        $offset = ($currentPage - 1) * $perPage;

        $items = $data->slice($offset, $perPage)->values();

        return new LengthAwarePaginator(
            $items,
            $data->count(),
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }
}
