<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FriendshipLinkCategoryResource\Pages;
use App\Models\FriendshipLinkCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use CodeWithDennis\FilamentSelectTree\SelectTree;

class FriendshipLinkCategoryResource extends Resource
{
    protected static ?string $model = FriendshipLinkCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-folder';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?string $navigationLabel = '友链分类';

    protected static ?int $navigationSort = 6;

    protected static ?int $navigationGroupSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('分类名称')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $context, $state, Forms\Set $set) {
                                if ($context === 'create') {
                                    $set('slug', Str::slug($state));
                                }
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->label('别名')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('用于URL，建议使用英文'),
                        SelectTree::make('parent_id')
                            ->label('父级分类')
                            ->relationship('parent', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('选择父级分类（留空为顶级分类）'),
                        Forms\Components\Textarea::make('description')
                            ->label('分类描述')
                            ->maxLength(500)
                            ->rows(3),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小排序越靠前'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('分类名称')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('slug')
                    ->label('别名')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('别名已复制'),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('父级分类')
                    ->default('顶级分类'),
                Tables\Columns\TextColumn::make('children_count')
                    ->label('子分类数')
                    ->counts('children')
                    ->badge(),
                Tables\Columns\TextColumn::make('friendshipLinks_count')
                    ->label('友链数量')
                    ->counts('friendshipLinks')
                    ->badge()
                    ->color('success'),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('父级分类')
                    ->relationship('parent', 'name')
                    ->placeholder('全部分类'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('启用')
                    ->falseLabel('禁用')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (FriendshipLinkCategory $record) {
                        // 检查是否有子分类或友链
                        if ($record->children()->count() > 0) {
                            throw new \Exception('该分类下还有子分类，无法删除');
                        }
                        if ($record->friendshipLinks()->count() > 0) {
                            throw new \Exception('该分类下还有友情链接，无法删除');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFriendshipLinkCategories::route('/'),
            'create' => Pages\CreateFriendshipLinkCategory::route('/create'),
            'edit' => Pages\EditFriendshipLinkCategory::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '友链分类';
    }

    public static function getModelLabel(): string
    {
        return '友链分类';
    }

    public static function getPluralModelLabel(): string
    {
        return '友链分类';
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
