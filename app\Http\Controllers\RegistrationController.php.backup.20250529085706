<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRegistrationRequest;
use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Services\SmsService;
use App\Services\WechatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;
use EasyWeChat\Factory;
use Illuminate\Support\Facades\Validator;

class RegistrationController extends Controller
{
    protected $smsService;
    protected $wechatService;

    public function __construct(SmsService $smsService, WechatService $wechatService)
    {
        $this->smsService = $smsService;
        $this->wechatService = $wechatService;
    }

    public function showForm($id)
    {
        $activityDetail = ActivityDetail::findOrFail($id);

        // 配置微信 JS-SDK
        $app = Factory::officialAccount(config('wechat.official_account.default'));
        $js = $app->jssdk;
        $jsConfig = $js->buildConfig(['getLocation'], false, false, false);

        return view('registration.form', compact('activityDetail', 'jsConfig'));
    }

    public function register(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'organization' => 'nullable|string|max:255',
            'grade' => 'nullable|string|max:255',
            'gender' => 'required|in:male,female',
            'source' => 'required|string|max:255',
            'openid' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        $activityDetail = ActivityDetail::findOrFail($id);

        // 检查报名人数是否已满
        if ($activityDetail->current_count >= $activityDetail->quota) {
            return back()->with('error', '报名人数已满');
        }

        // 检查报名截止时间
        if ($activityDetail->registration_deadline->isPast()) {
            return back()->with('error', '报名已截止');
        }

        // 创建报名记录
        $registration = Registration::create([
            'activity_detail_id' => $id,
            'name' => $request->name,
            'phone' => $request->phone,
            'organization' => $request->organization,
            'grade' => $request->grade,
            'gender' => $request->gender,
            'source' => $request->source,
            'status' => true,
        ]);

        // 更新当前报名人数
        $activityDetail->increment('current_count');

        // 发送短信通知
        $this->sendSmsNotification($registration, $activityDetail);

        // 发送公众号模板消息
        $this->sendWechatNotification($request, $activityDetail);

        return back()->with('success', '报名成功！');
    }

    /**
     * 发送短信通知
     */
    private function sendSmsNotification($registration, $activityDetail)
    {
        try {
            if (!$this->smsService->validateMobile($registration->phone)) {
                Log::warning('无效手机号', ['phone' => $registration->phone]);
                return false;
            }

            // 优先使用云片网模板
            if ($activityDetail->usesYunpianTemplate()) {
                // 获取模板代码，默认使用活动报名确认模板
                $templateCode = $activityDetail->smsTemplate ? $activityDetail->smsTemplate->code : 'activity_registration_confirm';
                $templateData = $this->buildTemplateData($registration, $activityDetail, $templateCode);
                return $this->smsService->sendSms(
                    $registration->phone,
                    $templateCode,
                    $templateData
                );
            }

            // 兼容旧的直接内容发送方式
            $content = $activityDetail->getSmsTemplateContent();
            if ($content) {
                $content = str_replace(
                    ['{name}', '{topic}', '{time}', '{location}'],
                    [
                        $registration->name,
                        $activityDetail->activity->title,
                        $activityDetail->activity_time,
                        $activityDetail->address
                    ],
                    $content
                );
                
                // 这里需要实现直接内容发送的逻辑
                // 或者转换为使用默认模板
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error('短信发送失败', [
                'registration_id' => $registration->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 构建模板数据
     */
    /**
     * 构建短信模板数据
     * @param $registration
     * @return array
     */
    private function buildTemplateData($registration)
    {
        $activityDetail = $registration->activityDetail;
        $activity = $activityDetail->activity;
        
        // 获取用户姓名，确保不为空
        $name = !empty($registration->name) ? $registration->name : "用户";
        
        // 获取活动标题，优先使用activity.title，其次使用activity_detail.theme
        $topic = "";
        if (!empty($activity->title)) {
            $topic = $activity->title;
        } elseif (!empty($activityDetail->theme)) {
            $topic = $activityDetail->theme;
        } else {
            $topic = "活动通知";
        }
        
        // 获取活动时间，确保格式正确
        $time = "";
        if (!empty($activityDetail->activity_time)) {
            try {
                $time = date("Y年m月d日 H:i", strtotime($activityDetail->activity_time));
            } catch (Exception $e) {
                $time = "待定时间";
            }
        } else {
            $time = "待定时间";
        }
        
        // 获取活动地点
        $address = !empty($activityDetail->address) ? $activityDetail->address : "待定地点";
        
        // 获取目标对象
        $obj = !empty($activityDetail->target) ? $activityDetail->target : "全体人员";
        
        $templateData = [
            "name" => $name,
            "topic" => $topic,
            "time" => $time,
            "address" => $address,
            "obj" => $obj
        ];
        
        // 记录日志以便调试
        \Log::info("构建短信模板数据", [
            "registration_id" => $registration->id,
            "activity_detail_id" => $activityDetail->id,
            "activity_id" => $activity->id,
            "template_data" => $templateData
        ]);
        
        return $templateData;
    }
        
        // 基础数据，带有默认值保护
        $baseData = [
            'name' => $registration->name ?: '用户',
            'topic' => $activityDetail->activity->title ?: $activityDetail->theme ?: '活动通知',
            'time' => $activityDetail->activity_time ?: '待定',
            'address' => $activityDetail->address ?: '待定',
            'obj' => $activityDetail->target ?: '全体人员'
        ];
        
        // 记录空值情况用于调试
        $emptyFields = [];
        foreach ($baseData as $key => $value) {
            if (empty($value) || $value === '待定') {
                $emptyFields[] = $key;
            }
        }
        
        if (!empty($emptyFields)) {
            Log::warning('短信模板数据包含空值或默认值', [
                'registration_id' => $registration->id,
                'activity_detail_id' => $activityDetail->id,
                'empty_fields' => $emptyFields,
                'template_code' => $templateCode
            ]);
        }

        // 根据模板类型返回相应数据
        switch ($templateCode) {
            case 'activity_registration_confirm':
            case 'consultation_notice':
                return [
                    'name' => $baseData['name'],
                    'topic' => $baseData['topic'],
                    'time' => $baseData['time'],
                    'address' => $baseData['address'],
                    'obj' => $baseData['obj']
                ];
            
            case 'summer_training_success':
                return [
                    'name' => $baseData['topic'] // 课程名称
                ];
            
            case 'account_approved':
            case 'account_rejected':
                return [
                    'name' => $baseData['name']
                ];
            
            case 'account_activated':
                return [
                    'name' => $baseData['name'],
                    'pwd' => $this->generateTempPassword() // 生成临时密码
                ];
            
            default:
                 return $baseData;
         }
     }

    /**
     * 生成临时密码
     */
    private function generateTempPassword($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $password;
     }

    /**
     * 发送微信公众号模板消息
     */
    private function sendWechatNotification(Request $request, ActivityDetail $activityDetail)
    {
        if ($request->openid && isset($activityDetail->wechatTemplate)) {
            try {
                if (!$this->wechatService) {
                    $this->wechatService = app(WechatService::class);
                }

                $this->wechatService->sendTemplateMessage(
                    $request->openid,
                    $activityDetail->wechatTemplate->code,
                    [
                        'first' => ['value' => '您已成功报名活动'],
                        'keyword1' => ['value' => $activityDetail->theme],
                        'keyword2' => ['value' => $activityDetail->activity_time->format('Y-m-d H:i')],
                        'keyword3' => ['value' => $activityDetail->address],
                        'remark' => ['value' => '请准时参加活动，如有疑问请联系主办方。'],
                    ],
                    route('registration.form', ['id' => $activityDetail->id])
                );
                
                Log::info('微信模板消息发送成功', [
                    'openid' => $request->openid,
                    'activity' => $activityDetail->theme
                ]);
            } catch (Exception $e) {
                // 记录错误但不影响报名流程
                Log::error('发送公众号模板消息失败：' . $e->getMessage(), [
                    'openid' => $request->openid ?? 'unknown',
                    'activity_id' => $activityDetail->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    public function checkStatus($id)
    {
        $activityDetail = ActivityDetail::findOrFail($id);

        return response()->json([
            'quota' => $activityDetail->quota,
            'current_count' => $activityDetail->current_count,
            'deadline' => $activityDetail->registration_deadline,
            'is_available' => $activityDetail->current_count < $activityDetail->quota && !$activityDetail->registration_deadline->isPast(),
        ]);
    }
}