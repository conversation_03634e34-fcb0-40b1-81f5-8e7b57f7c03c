<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class MemoryOptimizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 增加内存限制，特别是对于 Filament 的复杂组件
        if (php_sapi_name() !== 'cli') {
            ini_set('memory_limit', '4G');
        }
        
        // 对于特定的路由增加更多内存
        if (request()->is('admin/*')) {
            ini_set('memory_limit', '4G');
        }
    }
}