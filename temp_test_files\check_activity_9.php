<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Activity;
use App\Models\ActivityDetail;

echo "=== 检查Activity ID 9的数据 ===\n\n";

// 检查Activity表中的数据
$activity = Activity::find(9);
if ($activity) {
    echo "Activity ID 9 数据:\n";
    echo "- ID: {$activity->id}\n";
    echo "- Title: '{$activity->title}'\n";
    echo "- Status: {$activity->status}\n";
    echo "- Created: {$activity->created_at}\n";
    echo "- Updated: {$activity->updated_at}\n";
} else {
    echo "❌ Activity ID 9 不存在\n";
}

echo "\n=== 检查ActivityDetail中关联Activity ID 9的数据 ===\n\n";

// 检查ActivityDetail表中关联Activity ID 9的数据
$activityDetails = ActivityDetail::where('activity_id', 9)->with('activity')->get();

foreach ($activityDetails as $detail) {
    echo "ActivityDetail ID: {$detail->id}\n";
    echo "- Activity ID: {$detail->activity_id}\n";
    echo "- Theme: '{$detail->theme}'\n";
    echo "- Activity Time: '{$detail->activity_time}'\n";
    echo "- Address: '{$detail->address}'\n";
    echo "- Target: '{$detail->target}'\n";
    
    if ($detail->activity) {
        echo "- 关联的Activity Title: '{$detail->activity->title}'\n";
    } else {
        echo "- ❌ 没有找到关联的Activity\n";
    }
    echo "---\n";
}

echo "\n检查完成。\n";