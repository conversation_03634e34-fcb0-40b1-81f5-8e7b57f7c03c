<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\SmsService;
use App\Models\SmsTemplate;
use Illuminate\Support\Facades\Log;

echo "=== 实际短信发送测试 ===\n\n";

// 测试手机号（请替换为真实手机号进行测试）
$testMobile = '13800138000'; // 请替换为真实手机号

echo "测试手机号: {$testMobile}\n";
echo "请确认这是一个真实的手机号，否则请修改脚本中的 \$testMobile 变量\n\n";

// 获取模板
$template = SmsTemplate::findByCode('activity_registration_confirm');
if (!$template) {
    echo "错误：未找到 activity_registration_confirm 模板\n";
    exit(1);
}

echo "找到模板: {$template->name}\n";
echo "云片网模板ID: {$template->yunpian_template_id}\n";
echo "模板内容: {$template->description}\n\n";

// 准备测试数据
$testData = [
    'name' => '测试用户',
    'topic' => '测试活动',
    'time' => '2024-01-20 14:00',
    'address' => '上海市嘉定区教育学院'
];

echo "测试数据:\n";
foreach ($testData as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 验证参数
$validation = $template->validateParams($testData);
if ($validation !== true) {
    echo "参数验证失败，缺少参数: " . implode(', ', $validation) . "\n";
    exit(1);
}
echo "参数验证通过\n\n";

// 初始化短信服务
$smsService = new SmsService();

// 验证手机号
if (!$smsService->validateMobile($testMobile)) {
    echo "手机号格式无效: {$testMobile}\n";
    exit(1);
}
echo "手机号格式验证通过\n\n";

// 发送短信
echo "开始发送短信...\n";
try {
    $result = $smsService->sendSms($testMobile, 'activity_registration_confirm', $testData);
    
    if ($result) {
        echo "✅ 短信发送成功！\n";
    } else {
        echo "❌ 短信发送失败！\n";
        echo "请检查日志文件: storage/logs/laravel.log\n";
    }
} catch (Exception $e) {
    echo "❌ 短信发送异常: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n如果发送失败，可能的原因：\n";
echo "1. 云片网API密钥无效或余额不足\n";
echo "2. 云片网后台未创建模板ID: {$template->yunpian_template_id}\n";
echo "3. 手机号不是真实号码\n";
echo "4. 网络连接问题\n";
echo "\n请查看 storage/logs/laravel.log 获取详细错误信息\n";