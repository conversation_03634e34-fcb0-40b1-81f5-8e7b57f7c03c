<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use App\Models\User;
use App\Models\Article;
use App\Models\Category;
use App\Models\Tag;
use App\Models\Comment;
use Illuminate\Support\Facades\DB;

class DataValidationSeeder extends Seeder
{
    /**
     * 验证系统数据统计的准确性和功能联动
     */
    public function run(): void
    {
        $this->command->info('🔍 开始验证系统数据统计准确性...');

        // 1. 验证报名系统数据一致性
        $this->validateRegistrationSystem();

        // 2. 验证内容管理系统
        $this->validateContentManagement();

        // 3. 验证用户系统
        $this->validateUserSystem();

        // 4. 验证数据关联完整性
        $this->validateDataRelationships();

        // 5. 生成测试报告
        $this->generateTestReport();

        $this->command->info('✅ 数据验证完成！');
    }

    /**
     * 验证报名系统数据一致性
     */
    private function validateRegistrationSystem()
    {
        $this->command->info('');
        $this->command->info('📋 验证报名系统...');

        $inconsistencies = [];

        ActivityDetail::with(['activity', 'registrations'])->get()->each(function ($detail) use (&$inconsistencies) {
            $actualValidCount = $detail->registrations->where('status', true)->count();
            $storedCount = $detail->current_count;

            if ($actualValidCount !== $storedCount) {
                $inconsistencies[] = [
                    'activity' => $detail->activity->title,
                    'detail' => $detail->theme,
                    'actual' => $actualValidCount,
                    'stored' => $storedCount,
                    'difference' => $actualValidCount - $storedCount
                ];
            }
        });

        if (empty($inconsistencies)) {
            $this->command->info('✅ 报名系统数据一致性检查通过');
        } else {
            $this->command->error('❌ 发现数据不一致：');
            foreach ($inconsistencies as $issue) {
                $this->command->error("  - {$issue['activity']} - {$issue['detail']}：实际 {$issue['actual']}，存储 {$issue['stored']}，差异 {$issue['difference']}");
            }
        }

        // 验证报名统计
        $totalRegistrations = Registration::count();
        $validRegistrations = Registration::where('status', true)->count();
        $invalidRegistrations = Registration::where('status', false)->count();

        $this->command->info("总报名数：{$totalRegistrations}");
        $this->command->info("有效报名：{$validRegistrations}");
        $this->command->info("无效报名：{$invalidRegistrations}");

        if ($totalRegistrations === ($validRegistrations + $invalidRegistrations)) {
            $this->command->info('✅ 报名状态统计正确');
        } else {
            $this->command->error('❌ 报名状态统计错误');
        }
    }

    /**
     * 验证内容管理系统
     */
    private function validateContentManagement()
    {
        $this->command->info('');
        $this->command->info('📝 验证内容管理系统...');

        // 验证文章-标签关联
        $articlesWithTags = Article::has('tags')->count();
        $totalArticles = Article::count();

        $this->command->info("总文章数：{$totalArticles}");
        $this->command->info("有标签的文章：{$articlesWithTags}");

        // 验证评论关联
        $articlesWithComments = Article::has('comments')->count();
        $totalComments = Comment::count();

        $this->command->info("有评论的文章：{$articlesWithComments}");
        $this->command->info("总评论数：{$totalComments}");

        // 验证分类使用情况
        $totalCategories = Category::count();

        $this->command->info("总分类数：{$totalCategories}");

        $this->command->info('✅ 内容管理系统验证完成');
    }

    /**
     * 验证用户系统
     */
    private function validateUserSystem()
    {
        $this->command->info('');
        $this->command->info('👥 验证用户系统...');

        $totalUsers = User::count();
        $activeUsers = User::where('is_active', true)->count();
        $verifiedUsers = User::where('is_verified', true)->count();

        $this->command->info("总用户数：{$totalUsers}");
        $this->command->info("活跃用户：{$activeUsers}");
        $this->command->info("已验证用户：{$verifiedUsers}");

        // 验证用户积分系统
        $usersWithPoints = User::has('points')->count();

        $this->command->info("有积分记录的用户：{$usersWithPoints}");

        $this->command->info('✅ 用户系统验证完成');
    }

    /**
     * 验证数据关联完整性
     */
    private function validateDataRelationships()
    {
        $this->command->info('');
        $this->command->info('🔗 验证数据关联完整性...');

        // 验证孤立数据
        $orphanedRegistrations = Registration::whereDoesntHave('activityDetail')->count();
        $orphanedActivityDetails = ActivityDetail::whereDoesntHave('activity')->count();
        $orphanedComments = Comment::whereNull('post_id')->whereNull('article_id')->count();

        if ($orphanedRegistrations === 0 && $orphanedActivityDetails === 0 && $orphanedComments === 0) {
            $this->command->info('✅ 无孤立数据，关联完整性良好');
        } else {
            $this->command->error('❌ 发现孤立数据：');
            if ($orphanedRegistrations > 0) {
                $this->command->error("  - 孤立报名记录：{$orphanedRegistrations}");
            }
            if ($orphanedActivityDetails > 0) {
                $this->command->error("  - 孤立活动详情：{$orphanedActivityDetails}");
            }
            if ($orphanedComments > 0) {
                $this->command->error("  - 孤立评论：{$orphanedComments}");
            }
        }
    }

    /**
     * 生成测试报告
     */
    private function generateTestReport()
    {
        $this->command->info('');
        $this->command->info('📊 生成测试报告...');

        // 报名系统统计
        $registrationStats = [
            'total_activities' => Activity::count(),
            'total_details' => ActivityDetail::count(),
            'total_registrations' => Registration::count(),
            'valid_registrations' => Registration::where('status', true)->count(),
            'full_activities' => ActivityDetail::whereRaw('current_count >= quota')->count(),
            'avg_registration_rate' => ActivityDetail::selectRaw('AVG(current_count / quota * 100) as avg_rate')->first()->avg_rate ?? 0,
        ];

        // 内容管理统计
        $contentStats = [
            'total_articles' => Article::count(),
            'total_categories' => Category::count(),
            'total_tags' => Tag::count(),
            'total_comments' => Comment::count(),
            'avg_comments_per_article' => Comment::count() / max(Article::count(), 1),
        ];

        // 用户统计
        $userStats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'verified_users' => User::where('is_verified', true)->count(),
        ];

        $this->command->info('');
        $this->command->info('📈 报名系统统计：');
        $this->command->info("  活动总数：{$registrationStats['total_activities']}");
        $this->command->info("  场次总数：{$registrationStats['total_details']}");
        $this->command->info("  报名总数：{$registrationStats['total_registrations']}");
        $this->command->info("  有效报名：{$registrationStats['valid_registrations']}");
        $this->command->info("  满员场次：{$registrationStats['full_activities']}");
        $this->command->info("  平均报名率：" . round($registrationStats['avg_registration_rate'], 2) . "%");

        $this->command->info('');
        $this->command->info('📚 内容管理统计：');
        $this->command->info("  文章总数：{$contentStats['total_articles']}");
        $this->command->info("  分类总数：{$contentStats['total_categories']}");
        $this->command->info("  标签总数：{$contentStats['total_tags']}");
        $this->command->info("  评论总数：{$contentStats['total_comments']}");
        $this->command->info("  平均每篇文章评论数：" . round($contentStats['avg_comments_per_article'], 2));

        $this->command->info('');
        $this->command->info('👤 用户统计：');
        $this->command->info("  用户总数：{$userStats['total_users']}");
        $this->command->info("  活跃用户：{$userStats['active_users']}");
        $this->command->info("  已验证用户：{$userStats['verified_users']}");

        // 性能测试
        $this->performanceTest();
    }

    /**
     * 简单的性能测试
     */
    private function performanceTest()
    {
        $this->command->info('');
        $this->command->info('⚡ 性能测试...');

        $start = microtime(true);

        // 测试复杂查询
        $complexQuery = ActivityDetail::with(['activity', 'registrations'])
            ->whereHas('registrations', function ($query) {
                $query->where('status', true);
            })
            ->get();

        $queryTime = microtime(true) - $start;

        $this->command->info("复杂查询耗时：" . round($queryTime * 1000, 2) . "ms");

        if ($queryTime < 1) {
            $this->command->info('✅ 查询性能良好');
        } else {
            $this->command->warn('⚠️  查询性能需要优化');
        }
    }
}
