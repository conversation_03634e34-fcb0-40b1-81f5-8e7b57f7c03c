<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class FriendshipLink extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'url',
        'image',
        'description',
        'title',
        'rel',
        'friendship_link_category_id',
        'sort_order',
        'is_active',
        'open_new_window',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'open_new_window' => 'boolean',
        'sort_order' => 'integer',
    ];

    // 关联友情链接分类
    public function category()
    {
        return $this->belongsTo(FriendshipLinkCategory::class, 'friendship_link_category_id');
    }

    // 获取图片URL
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            // 检查文件是否存在
            if (Storage::disk('public')->exists($this->image)) {
                return Storage::disk('public')->url($this->image);
            }
        }
        // 返回默认图片 - 使用在线占位图
        return 'data:image/svg+xml;base64,' . base64_encode('
            <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="120" height="120" fill="#f3f4f6"/>
                <rect x="30" y="30" width="60" height="40" fill="#d1d5db" rx="4"/>
                <circle cx="45" cy="45" r="6" fill="#9ca3af"/>
                <path d="M30 65l15-10 10 5 25-15v25H30z" fill="#9ca3af"/>
                <text x="60" y="90" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">友情链接</text>
            </svg>
        ');
    }

    // 获取完整的链接属性
    public function getLinkAttributesAttribute()
    {
        $attributes = [
            'href' => $this->url,
            'title' => $this->title ?: $this->name,
        ];

        if ($this->open_new_window) {
            $attributes['target'] = '_blank';
        }

        if ($this->rel) {
            $attributes['rel'] = $this->rel;
        }

        return $attributes;
    }

    // 获取启用的友情链接
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // 按分类获取友情链接
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('friendship_link_category_id', $categoryId);
    }

    // 按排序获取
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    // 检查链接是否有效（简单验证）
    public function isValidUrl()
    {
        return filter_var($this->url, FILTER_VALIDATE_URL) !== false;
    }

    // 获取域名
    public function getDomainAttribute()
    {
        $parsed = parse_url($this->url);
        return $parsed['host'] ?? '';
    }

    // 检查是否为外部链接
    public function isExternalLink()
    {
        $currentDomain = request()->getHost();
        return $this->domain !== $currentDomain;
    }
}
