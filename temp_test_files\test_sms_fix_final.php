<?php

require_once __DIR__ . '/vendor/autoload.php';

// 初始化Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\SmsService;
use App\Models\SmsTemplate;

echo "=== 短信发送修复测试 ===\n\n";

// 1. 检查配置
echo "📋 检查配置:\n";
$apiKey = config('services.yunpian.api_key');
echo "   - API Key: " . ($apiKey ? "✅ 已配置 (" . substr($apiKey, 0, 8) . "...)" : "❌ 未配置") . "\n\n";

if (!$apiKey) {
    echo "❌ 请先配置云片网API密钥\n";
    exit(1);
}

// 2. 测试短信模板查询
echo "📝 测试短信模板:\n";
$template = SmsTemplate::findByCode('activity_registration_confirm');
if ($template) {
    echo "   - 模板代码: {$template->code}\n";
    echo "   - 云片模板ID: {$template->yunpian_template_id}\n";
    echo "   - 模板参数: " . json_encode($template->template_params) . "\n\n";
} else {
    echo "   ❌ 未找到活动报名确认模板\n\n";
}

// 3. 测试参数构建
echo "🔧 测试参数构建:\n";
$smsService = new SmsService();

// 使用正确的模板参数
$testData = [
    'name' => '张三',
    'topic' => '测试活动',
    'time' => '2025-05-28 15:00',
    'address' => '测试地点'
];

echo "   - 模板要求参数: name, topic, time, address\n";

echo "   - 测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n";

// 手动构建tpl_value来验证格式
$tplValue = '';
if (!empty($testData)) {
    $pairs = [];
    foreach ($testData as $key => $value) {
        $varName = strpos($key, '#') === false ? "#{$key}#" : $key;
        $pairs[] = urlencode($varName) . '=' . urlencode($value);
    }
    $tplValue = implode('&', $pairs);
}

echo "   - 构建的tpl_value: {$tplValue}\n\n";

// 4. 测试实际发送（使用测试手机号）
echo "📱 测试短信发送:\n";
$testMobile = '13800138000'; // 使用测试手机号

echo "   - 目标手机号: {$testMobile}\n";
echo "   - 开始发送...\n";

try {
    $result = $smsService->sendSms($testMobile, 'activity_registration_confirm', $testData);
    
    if ($result) {
        echo "   ✅ 短信发送成功！\n";
    } else {
        echo "   ❌ 短信发送失败，请检查日志\n";
    }
} catch (Exception $e) {
    echo "   ❌ 发送异常: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n💡 提示:\n";
echo "   - 如果发送失败，请检查 storage/logs/laravel.log 获取详细错误信息\n";
echo "   - 确保云片网账户有足够余额\n";
echo "   - 确保模板已在云片网后台审核通过\n";