<?php

namespace Database\Seeders;

use App\Models\SmsTemplate;
use Illuminate\Database\Seeder;

class SmsTemplateSeeder extends Seeder
{
    public function run()
    {
        $templates = [
            [
                'name' => '嘉定幸福课程验证码',
                'code' => 'jiading_verification',
                'yunpian_template_id' => '1001', // 替换为实际模板ID
                'description' => '【上海市嘉定区教育学院】验证码#code#，您正在进行嘉定幸福课程身份验证，感谢关注',
                'template_category' => 'verification',
                'template_params' => [
                    [
                        'key' => 'code',
                        'name' => '验证码',
                        'type' => 'string',
                        'required' => true,
                        'description' => '6位数字验证码'
                    ]
                ],
                'param_description' => 'code: 验证码（必填）'
            ],
            [
                'name' => '通用验证码',
                'code' => 'general_verification',
                'yunpian_template_id' => '1002',
                'description' => '【上海市嘉定区教育学院】您的验证码是#code#。如非本人操作，请忽略本短信',
                'template_category' => 'verification',
                'template_params' => [
                    [
                        'key' => 'code',
                        'name' => '验证码',
                        'type' => 'string',
                        'required' => true,
                        'description' => '验证码'
                    ]
                ],
                'param_description' => 'code: 验证码（必填）'
            ],
            [
                'name' => '现场咨询通知',
                'code' => 'consultation_notification',
                'yunpian_template_id' => '1003',
                'description' => '【上海市嘉定区教育学院】现场咨询，主题为：#topic#；时间为：#time#；地点为：#address#；对象为：#obj#；请准时参加。',
                'template_category' => 'notification',
                'template_params' => [
                    [
                        'key' => 'topic',
                        'name' => '主题',
                        'type' => 'string',
                        'required' => true,
                        'description' => '咨询主题'
                    ],
                    [
                        'key' => 'time',
                        'name' => '时间',
                        'type' => 'datetime',
                        'required' => true,
                        'description' => '活动时间'
                    ],
                    [
                        'key' => 'address',
                        'name' => '地点',
                        'type' => 'string',
                        'required' => true,
                        'description' => '活动地点'
                    ],
                    [
                        'key' => 'obj',
                        'name' => '对象',
                        'type' => 'string',
                        'required' => true,
                        'description' => '参与对象'
                    ]
                ],
                'param_description' => 'topic: 主题（必填）, time: 时间（必填）, address: 地点（必填）, obj: 对象（必填）'
            ],
            [
                'name' => '账号审核通过',
                'code' => 'account_approved',
                'yunpian_template_id' => '1004',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已通过审核，感谢关注',
                'template_category' => 'audit',
                'template_params' => [
                    [
                        'key' => 'name',
                        'name' => '用户姓名',
                        'type' => 'string',
                        'required' => true,
                        'description' => '用户真实姓名'
                    ]
                ],
                'param_description' => 'name: 用户姓名（必填）'
            ],
            [
                'name' => '账号审核未通过',
                'code' => 'account_rejected',
                'yunpian_template_id' => '1005',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号未能通过审核，可能的原因是您不是本课程的适用对象，感谢关注！',
                'template_category' => 'audit',
                'template_params' => [
                    [
                        'key' => 'name',
                        'name' => '用户姓名',
                        'type' => 'string',
                        'required' => true,
                        'description' => '用户真实姓名'
                    ]
                ],
                'param_description' => 'name: 用户姓名（必填）'
            ],
            [
                'name' => '账号开通通知',
                'code' => 'account_activated',
                'yunpian_template_id' => '1006',
                'description' => '【上海市嘉定区教育学院】尊敬的#name#，您的嘉定区幸福课程账号已开通，请用微信关注公众号嘉定区幸福课程，并用手机号登录，密码为#pwd#',
                'template_category' => 'account',
                'template_params' => [
                    [
                        'key' => 'name',
                        'name' => '用户姓名',
                        'type' => 'string',
                        'required' => true,
                        'description' => '用户真实姓名'
                    ],
                    [
                        'key' => 'pwd',
                        'name' => '登录密码',
                        'type' => 'string',
                        'required' => true,
                        'description' => '系统生成的登录密码'
                    ]
                ],
                'param_description' => 'name: 用户姓名（必填）, pwd: 登录密码（必填）'
            ],
            [
                'name' => '心灵嘉园验证码',
                'code' => 'xinling_verification',
                'yunpian_template_id' => '1007',
                'description' => '【上海市嘉定区教育学院】验证码#code#，您正在进行心灵嘉园身份验证，感谢关注',
                'template_category' => 'verification',
                'template_params' => [
                    [
                        'key' => 'code',
                        'name' => '验证码',
                        'type' => 'string',
                        'required' => true,
                        'description' => '6位数字验证码'
                    ]
                ],
                'param_description' => 'code: 验证码（必填）'
            ],
            [
                'name' => '暑期培训报名成功',
                'code' => 'summer_training_success',
                'yunpian_template_id' => '1008',
                'description' => '【上海市嘉定区教育学院】您好，您报的暑期大培训课程"#name#"已报名成功，请关注开课时间，准时参加！',
                'template_category' => 'registration',
                'template_params' => [
                    [
                        'key' => 'name',
                        'name' => '课程名称',
                        'type' => 'string',
                        'required' => true,
                        'description' => '培训课程名称'
                    ]
                ],
                'param_description' => 'name: 课程名称（必填）'
            ]
        ];

        foreach ($templates as $template) {
            SmsTemplate::updateOrCreate(
                ['code' => $template['code']],
                $template
            );
        }
    }
}