<?php

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\SmsTemplate;

echo "=== 检查数据库字段与云片网字段映射 ===\n\n";

// 1. 检查当前数据库表结构
echo "1. 当前数据库表结构:\n";
echo "\n--- activities 表字段 ---\n";
$activitiesColumns = Schema::getColumnListing('activities');
foreach ($activitiesColumns as $column) {
    echo "- {$column}\n";
}

echo "\n--- activity_details 表字段 ---\n";
$activityDetailsColumns = Schema::getColumnListing('activity_details');
foreach ($activityDetailsColumns as $column) {
    echo "- {$column}\n";
}

echo "\n--- sms_templates 表字段 ---\n";
$smsTemplatesColumns = Schema::getColumnListing('sms_templates');
foreach ($smsTemplatesColumns as $column) {
    echo "- {$column}\n";
}

// 2. 检查云片网模板参数
echo "\n\n2. 云片网模板参数分析:\n";
$consultationTemplate = SmsTemplate::where('code', 'consultation_notice')->first();
if ($consultationTemplate) {
    echo "\n--- consultation_notice 模板 ---\n";
    echo "- 模板名称: {$consultationTemplate->name}\n";
    echo "- 云片模板ID: {$consultationTemplate->yunpian_template_id}\n";
    echo "- 模板参数: {$consultationTemplate->template_params}\n";
    
    // 解析模板参数
    if ($consultationTemplate->template_params) {
        $params = json_decode($consultationTemplate->template_params, true);
        if ($params) {
            echo "\n--- 解析后的参数 ---\n";
            foreach ($params as $key => $value) {
                echo "- {$key}: {$value}\n";
            }
        }
    }
}

// 3. 当前字段映射分析
echo "\n\n3. 当前字段映射分析:\n";
echo "\n--- buildTemplateData 方法中的字段映射 ---\n";
echo "- name: registration.name (用户姓名)\n";
echo "- topic: activity.title 或 activity_detail.theme (活动主题)\n";
echo "- time: activity_detail.activity_time (活动时间)\n";
echo "- address: activity_detail.address (活动地址)\n";
echo "- obj: activity_detail.target (目标对象)\n";

// 4. 云片网字段要求分析
echo "\n\n4. 云片网字段要求分析:\n";
echo "根据云片网SMS模板，通常使用以下参数格式:\n";
echo "- #name# : 用户姓名\n";
echo "- #topic# : 活动主题/标题\n";
echo "- #time# : 活动时间\n";
echo "- #address# : 活动地址\n";
echo "- #obj# : 目标对象\n";

// 5. 字段一致性检查
echo "\n\n5. 字段一致性检查:\n";
echo "\n--- 当前映射是否与云片网一致 ---\n";
$mappingConsistency = [
    'name' => '✓ 一致 - 用户姓名字段',
    'topic' => '✓ 一致 - 活动主题字段',
    'time' => '✓ 一致 - 活动时间字段',
    'address' => '✓ 一致 - 活动地址字段',
    'obj' => '✓ 一致 - 目标对象字段'
];

foreach ($mappingConsistency as $field => $status) {
    echo "- {$field}: {$status}\n";
}

// 6. 建议的优化方案
echo "\n\n6. 建议的优化方案:\n";
echo "\n--- 数据库字段优化建议 ---\n";
echo "当前字段映射已经与云片网要求基本一致，建议的改进：\n";
echo "\n1. 确保数据完整性:\n";
echo "   - activities.title 不应为空\n";
echo "   - activity_details.activity_time 应有默认值\n";
echo "   - activity_details.address 应有默认值\n";
echo "   - activity_details.target 应有默认值\n";

echo "\n2. 字段命名优化 (可选):\n";
echo "   - activity_details.theme -> activity_details.topic (更直观)\n";
echo "   - activity_details.target -> activity_details.target_audience (更明确)\n";

echo "\n3. 代码优化:\n";
echo "   - buildTemplateData 方法已经有良好的默认值处理\n";
echo "   - 建议添加更多的数据验证\n";

echo "\n\n=== 检查完成 ===\n";
