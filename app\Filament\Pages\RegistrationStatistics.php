<?php

namespace App\Filament\Pages;

use App\Models\Registration;
use App\Filament\Widgets\RegistrationStatsOverview;
use Filament\Pages\Page;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;

class RegistrationStatistics extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationGroup = '报名系统';
    protected static ?string $navigationLabel = '报名统计';
    protected static ?int $navigationSort = 2;
    protected static ?int $navigationGroupSort = 2;
    protected static ?string $title = '报名统计';

    protected static string $view = 'filament.pages.registration-statistics';

    protected function getHeaderWidgets(): array
    {
        return [
            RegistrationStatsOverview::class,
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Registration::query())
            ->columns([
                TextColumn::make('name')
                    ->label('姓名')
                    ->searchable(),
                TextColumn::make('phone')
                    ->label('手机号')
                    ->searchable(),
                TextColumn::make('organization')
                    ->label('单位')
                    ->searchable(),
                TextColumn::make('grade')
                    ->label('年级')
                    ->searchable(),
                TextColumn::make('gender')
                    ->label('性别')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'male' => '男',
                        'female' => '女',
                        default => $state,
                    }),
                TextColumn::make('source')
                    ->label('来源')
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label('报名时间')
                    ->dateTime('Y-m-d H:i:s'),
            ])
            ->filters([
                SelectFilter::make('gender')
                    ->label('性别')
                    ->options([
                        'male' => '男',
                        'female' => '女',
                    ]),
                SelectFilter::make('source')
                    ->label('来源')
                    ->options([
                        'wechat' => '微信',
                        'website' => '网站',
                        'other' => '其他',
                    ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->headerActions([
                \Filament\Tables\Actions\Action::make('exportAll')
                    ->label('导出全部')
                    ->color('primary')
                    ->action(function () {
                        return response()->streamDownload(function () {
                            \App\Models\Registration::with('activityDetail')->chunk(100, function ($records) {
                                $csv = fopen('php://output', 'w');
                                static $headerWritten = false;
                                foreach ($records as $index => $record) {
                                    if (!$headerWritten) {
                                        fputcsv($csv, [
                                            '序号', '姓名', '手机号', '单位', '年级', '性别', '来源', '报名时间',
                                        ]);
                                        $headerWritten = true;
                                    }
                                    fputcsv($csv, [
                                        $record->id,
                                        $record->name,
                                        $record->phone,
                                        $record->organization,
                                        $record->grade,
                                        $record->gender === 'male' ? '男' : ($record->gender === 'female' ? '女' : ''),
                                        $record->source,
                                        $record->created_at ? $record->created_at->format('Y-m-d H:i:s') : '',
                                    ]);
                                }
                                fclose($csv);
                            });
                        }, '报名数据.csv');
                    }),
            ]);
    }
} 