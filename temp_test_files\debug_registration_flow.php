<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\ActivityDetail;
use App\Http\Controllers\RegistrationController;
use App\Services\SmsService;
use App\Services\WechatService;
use Illuminate\Http\Request;

echo "=== 调试注册流程中的短信发送 ===\n\n";

// 获取最新的注册记录
$registration = Registration::with(['activityDetail.activity', 'activityDetail.smsTemplate'])
    ->orderBy('id', 'desc')
    ->first();

echo "最新注册记录:\n";
echo "ID: {$registration->id}\n";
echo "姓名: {$registration->name}\n";
echo "活动: {$registration->activityDetail->activity->title}\n";
echo "短信模板: {$registration->activityDetail->smsTemplate->code}\n\n";

// 模拟控制器中的数据获取
echo "=== 模拟控制器数据获取 ===\n";
$activityDetail = $registration->activityDetail;

echo "ActivityDetail数据:\n";
echo "ID: {$activityDetail->id}\n";
echo "活动时间: {$activityDetail->activity_time}\n";
echo "地址: '{$activityDetail->address}'\n";
echo "对象: '{$activityDetail->target}'\n";

// 检查activity关联
echo "\nActivity关联数据:\n";
if ($activityDetail->activity) {
    echo "活动标题: '{$activityDetail->activity->title}'\n";
} else {
    echo "警告: activity关联为空!\n";
}

// 手动构建模板数据（模拟buildTemplateData方法）
echo "\n=== 构建模板数据 ===\n";
$templateData = [
    'name' => $registration->name,
    'topic' => $activityDetail->activity ? $activityDetail->activity->title : '',
    'time' => $activityDetail->activity_time,
    'address' => $activityDetail->address,
    'obj' => $activityDetail->target
];

echo "模板数据:\n";
foreach ($templateData as $key => $value) {
    echo "  {$key}: '{$value}'\n";
}

// 模拟SmsService的参数处理
echo "\n=== 模拟SMS参数处理 ===\n";
$pairs = [];
foreach ($templateData as $key => $value) {
    $smsKey = "#{$key}#";
    $pairs[] = urlencode($smsKey) . '=' . urlencode($value);
}
$tplValue = implode('&', $pairs);

echo "tpl_value: {$tplValue}\n";

// 解码验证
echo "\n=== 解码验证 ===\n";
parse_str($tplValue, $decoded);
foreach ($decoded as $key => $value) {
    echo "  {$key}: '{$value}'\n";
}

echo "\n=== 检查空值 ===\n";
foreach ($templateData as $key => $value) {
    if (empty($value)) {
        echo "警告: {$key} 为空值!\n";
    }
}

echo "\n调试完成。\n";