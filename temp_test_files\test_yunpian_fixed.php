<?php

require_once 'vendor/autoload.php';

// 加载Laravel环境
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Registration;
use App\Models\SmsTemplate;

echo "=== 测试云片网参数映射 ===\n";

try {
    // 获取SMS模板
    $template = SmsTemplate::where('name', '现场咨询通知')->first();
    if (!$template) {
        echo "没有找到SMS模板\n";
        exit;
    }
    
    echo "模板名称: {$template->name}\n";
    echo "模板内容: {$template->description}\n";
    
    // 安全地显示template_params
    $templateParams = $template->template_params;
    if (is_array($templateParams)) {
        echo "模板参数: " . json_encode($templateParams) . "\n";
    } else {
        echo "模板参数: {$templateParams}\n";
    }
    
    // 获取测试数据
    $registration = Registration::with(['activityDetail.activity'])->first();
    $activityDetail = $registration->activityDetail;
    $activity = $activityDetail->activity;
    
    // 构建模板数据
    $templateData = [
        "name" => $registration->name,
        "topic" => $activity->title . " - 第一场",
        "time" => date("Y年m月d日 H:i", strtotime($activityDetail->activity_time)),
        "address" => $activityDetail->address,
        "obj" => $activityDetail->target_audience
    ];
    
    echo "\n=== 模板数据 ===\n";
    foreach ($templateData as $key => $value) {
        echo "{$key}: '{$value}'\n";
    }
    
    // 手动替换模板内容
    $content = $template->description;
    foreach ($templateData as $key => $value) {
        $content = str_replace("#{$key}#", $value, $content);
    }
    
    echo "\n=== 替换后的内容 ===\n";
    echo $content . "\n";
    
    // 检查是否还有未替换的占位符
    if (preg_match_all('/#([^#]+)#/', $content, $matches)) {
        echo "\n❌ 发现未替换的占位符:\n";
        foreach ($matches[1] as $placeholder) {
            echo "- #{$placeholder}#\n";
        }
    } else {
        echo "\n✅ 所有占位符都已替换\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
