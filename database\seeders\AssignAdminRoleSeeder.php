<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class AssignAdminRoleSeeder extends Seeder
{
    public function run(): void
    {
        $user = User::where('email', '<EMAIL>')->first();
        if ($user) {
            $role = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
            $user->assignRole($role);
            $this->command?->info('已为 <EMAIL> 分配 admin 角色');
        } else {
            $this->command?->warn('未找到 <EMAIL> 用户');
        }
    }
} 