<?php

namespace App\Filament\Resources\FocusImageResource\Pages;

use App\Filament\Resources\FocusImageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFocusImages extends ListRecords
{
    protected static string $resource = FocusImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}