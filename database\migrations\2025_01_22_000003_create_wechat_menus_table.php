<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_menus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wechat_account_id')->constrained()->onDelete('cascade');
            $table->foreignId('parent_id')->nullable()->constrained('wechat_menus')->onDelete('cascade');
            $table->string('name', 16)->comment('菜单名称');
            $table->string('type', 32)->nullable()->comment('菜单类型');
            $table->string('key', 128)->nullable()->comment('菜单KEY值');
            $table->string('url', 1024)->nullable()->comment('网页链接');
            $table->string('media_id', 128)->nullable()->comment('媒体文件ID');
            $table->string('appid', 32)->nullable()->comment('小程序appid');
            $table->string('pagepath', 256)->nullable()->comment('小程序页面路径');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['wechat_account_id', 'parent_id']);
            $table->index(['wechat_account_id', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_menus');
    }
};