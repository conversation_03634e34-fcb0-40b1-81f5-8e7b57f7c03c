<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\School;
use Illuminate\Support\Facades\DB;

class UpdateSchoolFullRegionName extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'school:update-full-region-name {--force : Force update all records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update full_region_name field for all schools based on their region data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始更新学校完整地区名称...');
        
        $force = $this->option('force');
        
        // 获取需要更新的学校记录
        $query = School::query();
        
        if (!$force) {
            // 只更新 full_region_name 为空的记录
            $query->where(function ($q) {
                $q->whereNull('full_region_name')
                  ->orWhere('full_region_name', '')
                  ->orWhere('full_region_name', ' - ');
            });
        }
        
        $schools = $query->get();
        
        if ($schools->isEmpty()) {
            $this->info('没有需要更新的学校记录。');
            return 0;
        }
        
        $this->info("找到 {$schools->count()} 条需要更新的学校记录。");
        
        $progressBar = $this->output->createProgressBar($schools->count());
        $progressBar->start();
        
        $updated = 0;
        $errors = 0;
        
        foreach ($schools as $school) {
            try {
                // 生成完整地区名称
                $fullRegionName = $this->generateFullRegionName($school);
                
                // 只有当 full_region_name 为空或与计算出的值不同时才更新
                if ($school->full_region_name !== $fullRegionName) {
                    $school->update(['full_region_name' => $fullRegionName]);
                    $updated++;
                }
                
                $progressBar->advance();
            } catch (\Exception $e) {
                $errors++;
                $this->error("\n更新学校 ID {$school->id} 时出错: " . $e->getMessage());
            }
        }
        
        $progressBar->finish();
        
        $this->newLine(2);
        $this->info("更新完成！");
        $this->info("成功更新: {$updated} 条记录");
        
        if ($errors > 0) {
            $this->warn("错误: {$errors} 条记录更新失败");
        }
        
        return 0;
    }
    
    /**
     * 生成完整地区名称
     */
    private function generateFullRegionName(School $school): string
    {
        $parts = array_filter([
            $school->province_name,
            $school->city_name,
            $school->district_name,
            $school->town_name,
        ]);
        
        return implode(' - ', $parts);
    }
}