<?php

namespace App\Filament\Widgets;

use App\Models\Activity;
use App\Models\ActivityDetail;
use App\Models\Registration;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class RegistrationStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';
    protected static ?int $sort = 1;
    protected int|string|array $columnSpan = 'full';

    protected function getStats(): array
    {
        $totalRegistrations = Registration::where('status', true)->count();
        $totalActivities = Activity::count();
        $totalActivityDetails = ActivityDetail::count();

        // 获取最近7天的报名趋势
        $recentRegistrations = Registration::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('count(*) as count')
        )
            ->where('created_at', '>=', now()->subDays(7))
            ->where('status', true) // 只统计有效报名
            ->groupBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        // 获取各活动报名人数
        $activityRegistrations = ActivityDetail::withCount(['registrations' => function($query) {
                $query->where('status', true); // 只统计有效报名
            }])
            ->orderByDesc('registrations_count')
            ->limit(5)
            ->get()
            ->pluck('registrations_count', 'theme')
            ->toArray();

        return [
            Stat::make('总报名人数', $totalRegistrations)
                ->description('所有活动的报名总人数')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('success'),

            Stat::make('活动总数', $totalActivities)
                ->description('已创建的活动数量')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary'),

            Stat::make('活动场次', $totalActivityDetails)
                ->description('已创建的活动场次数量')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('warning'),

            Stat::make('最近7天报名趋势', '')
                ->description('点击"报名分析"查看更多趋势图表')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('info'),

            Stat::make('热门活动', '')
                ->description('点击"报名分析"查看更多热门活动')
                ->descriptionIcon('heroicon-m-fire')
                ->color('danger'),
        ];
    }
}